import { Job, Worker } from 'bullmq';
import { pathToFileURL } from 'url';
import { Worker as ThreadWorker } from 'worker_threads';

import { WORKER_CONCURRENCY } from './config';
import { nodeJobsCount, nodeUtilization } from './metrics';
import redis from './redis';
import { logging, JobData, WorkerData, JobType } from 'shared';

const PROCESSOR_POOL = new Map();

export function initializeWorker() {
    const worker = new Worker<JobData>(
        redis.QUEUE_NAME,
        async (job: Job<JobData>) => {
            const workerData: WorkerData = {
                id: job.id as string,
                name: job.name as JobType,
                data: job.data,
            };
            const processor = new ThreadWorker(pathToFileURL(__dirname + '/processor.js'), {
                workerData,
            });
            PROCESSOR_POOL.set(job.id, processor);
            updateMetrics();
            return new Promise((resolve, reject) => {
                processor.on('message', (message) => {
                    void job.updateProgress(message);
                });
                processor.on('error', (error) => {
                    reject(error);
                });
                processor.on('exit', (code) => {
                    resolve(code);
                });
            });
        },
        {
            connection: redis.connection,
            maxStalledCount: Number.MAX_SAFE_INTEGER, // Unlimited recovery from stalled state
            stalledInterval: 10000,
            lockDuration: 10000,
            lockRenewTime: 5000,
            concurrency: WORKER_CONCURRENCY,
        },
    );

    worker.on('active', (job: Job<JobData>) => {
        logging.info(`[BULLMQ] job is active`, { jobId: job.id });
    });

    worker.on('completed', (job: Job<JobData>) => {
        PROCESSOR_POOL.delete(job.id);
        updateMetrics();
        logging.info(`[BULLMQ] job completed`, { jobId: job.id });
    });

    worker.on('failed', (job: Job<JobData> | undefined, err: Error) => {
        if (job && job.id) {
            PROCESSOR_POOL.delete(job.id);
            updateMetrics();
        }

        logging.error(`[BULLMQ] job failed with ${err.name}`, err, {
            jobId: job?.id,
            stack: err.stack?.replace(/\s*\n\s*/g, ' | '),
        });
    });

    const updateMetrics = () => {
        const isActive = worker.isRunning() && !worker.closing;
        nodeJobsCount.labels({ active: String(isActive) }).set(PROCESSOR_POOL.size);
        nodeUtilization.labels({ active: String(isActive) }).set(PROCESSOR_POOL.size / WORKER_CONCURRENCY);
        // inverse metrics should be removed
        nodeJobsCount.remove({ active: String(!isActive) });
        nodeUtilization.remove({ active: String(!isActive) });
    };

    worker.on('error', (err: Error) => {
        logging.error(`[BULLMQ] Worker error ${err.message}`, err, {
            stack: err.stack?.replace(/\s*\n\s*/g, ' | ')
        });
    });

    worker.on('closing', (msg: string) => {
        logging.info(`[BULLMQ] Worker is closing: ${msg}`);
        for (const [id, processor] of PROCESSOR_POOL.entries()) {
            logging.info(`[BULLMQ] Notifying processor ${id} to close`);
            try {
                processor.postMessage({ type: 'closing' });
            } catch (err) {
                logging.warn(`[BULLMQ] Failed to notify processor ${id}`, err);
            }
        }
    });

    return worker;
}
