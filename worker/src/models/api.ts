export interface UserAccountRequest {
    username?: string;
    userId: string;
    password: string;
    countryCode: string;
    areaCode?: string;
    phoneNum?: string;
    email?: string;
    platformId: number;
    playerId?: string;
    deviceId?: string;
}

export interface UserAccountRequestBody {
    appId: number;
    userAccounts: UserAccountRequest[];
}

export interface PlayerStats {
    handsPlayed: number;
    totalBuyIn: number;
    lastBuyIn: number;
    rebuyCount: number;
    stack: number;
}
