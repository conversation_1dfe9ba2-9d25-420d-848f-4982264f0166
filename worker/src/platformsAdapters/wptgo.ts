import fetch from 'node-fetch';

import {
    LoginError,
    logging,
    MttUserData,
    UserData,
    PlatformUserData,
    PkwUserData,
    PkwAuthData,
    getProxyAgent,
} from 'shared';
import { CurrencyType } from '../types';
import { balance, mttData, hashToken } from 'wptgo';

import { ManagingPlatformAdapter, TransferJobData } from '../models/model';
import { Balance } from 'shared';
import { WPTGO_APP_BUNDLE_ID, WPTGO_URL, WPTGO_WS_URL } from '../config';
import { UnrecoverableError } from 'bullmq';

class WptgoAdapter implements ManagingPlatformAdapter {
    init(): void {}

    async login(user: UserData): Promise<PkwUserData> {
        const userData = await this.loginServer(user);
        if (!userData?.token) {
            throw new LoginError(`${userData?.code} ${userData?.description}`);
        }
        return {
            userId: userData.user_id,
            token: userData.token,
            deviceId: user.deviceId,
            pkwAuthData: {
                appIP: userData.ip,
                token: userData.sharedPlayerToken,
                uid: userData.sharedPlayerId,
                gate_addr: Array.from(new Set(userData.pkwGateAddresses)),
                pkw_file_addr: userData.pkwFileAddress,
                web_server: userData.pkwApiAddress,
                client_type: 16, // WPTO client typ
            } as PkwAuthData,
            user: {},
        };
    }

    async resolveMttUserData(platformUser: PlatformUserData): Promise<MttUserData> {
        return mttData(WPTGO_WS_URL, {
            userId: platformUser.userId,
            token: platformUser.token,
            deviceId: platformUser.deviceId,
        });
    }

    private async loginServer(user: UserData): Promise<any> {
        const response = await fetch(`${WPTGO_URL}/api/v1/user/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-AppBundleID': WPTGO_APP_BUNDLE_ID,
            },
            body: JSON.stringify({
                login: user.username,
                password: user.password,
                deviceType: 'android',
                device_uuid: user.deviceId,
                version: '1.0.0',
                clientType: 5,
                language: 'en_US',
                location: {},
            }),
            agent: getProxyAgent(),
        });
        return response.json();
    }

    async balance(platformUser: PlatformUserData): Promise<Balance> {
        const usd = await balance(WPTGO_WS_URL, {
            userId: platformUser.userId,
            token: platformUser.token,
            deviceId: platformUser.deviceId,
        });
        return { usd } as Balance;
    }

    async transfer(platformUser: PlatformUserData, jobData: TransferJobData): Promise<void> {
        if (jobData.currency !== CurrencyType.USD) {
            throw new UnrecoverableError(`Unsupported currency for WPTGO transfer: ${jobData.currency}`);
        }
        const sessionId = hashToken(platformUser.token);

        const url = `${WPTGO_URL}/api/v1/payments/p2p/transfer/userId`;
        const headers = {
            'Content-Type': 'application/json',
            'X-userId': `${platformUser.userId}`,
            'X-sessionId': sessionId,
        };
        const body = JSON.stringify({
            userId: jobData.receiverId, // Recipient's user ID
            username: jobData.receiverUsername, // Recipient's username
            amount: jobData.transferAmount, // Amount to transfer
        });
        logging.info(`Transfer Request URL: ${url}`, { headers, body });

        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: body,
            agent: getProxyAgent(),
        });
        if (!response.ok) {
            const text = await response.text();
            logging.error(`Transfer failed: ${text}`, response.status, response);
            throw new UnrecoverableError(`Transfer failed: ${response.status}`);
        }
        const text = await response.text();
        logging.info(`Transfer successful: ${text}`);
    }
}

export const wptgoPlatform = new WptgoAdapter();
