import process from 'node:process';
import { MttConfigType } from 'shared';

const getEnvVar = (name: string): string => {
    const value = process.env[name]
    if (!value) throw new Error(`${name} is not set`)
    return value
}

export const PORT = Number(process.env.PORT) || 3000;
export const WORKER_CONCURRENCY = Number(process.env.WORKER_CONCURRENCY) || 64;

// WPK
export const WPK_URL = getEnvVar('WPK_URL')

// RWPK
export const RWPK_URL = getEnvVar('RWPK_URL')

// WPTGO
export const WPTGO_URL = getEnvVar('WPTGO_URL')
export const WPTGO_WS_URL = getEnvVar('WPTGO_WS_URL')
export const WPTGO_APP_BUNDLE_ID = getEnvVar('WPTGO_APP_BUNDLE_ID')

// MTT
export const MTT_DEFAULT_CONFIG = {
    mttWorld: process.env.MTT_CONFIG_MTTWORLD,
    mttGame: process.env.MTT_CONFIG_MTTGAME,
    mttApi: process.env.MTT_CONFIG_MTTAPI,
} as MttConfigType;
export const MTT_CONFIG_URL = process.env.MTT_CONFIG_URL || '';

// RMTT
export const RMTT_DEFAULT_CONFIG = {
    mttWorld: process.env.RMTT_CONFIG_MTTWORLD,
    mttGame: process.env.RMTT_CONFIG_MTTGAME,
    mttApi: process.env.RMTT_CONFIG_MTTAPI,
} as MttConfigType;
export const RMTT_CONFIG_URL = process.env.RMTT_CONFIG_URL || '';

export let CONTAINER_ID = '';
if (process.env.ECS_CONTAINER_METADATA_URI_V4) {
    // Checking the last part which no not includes /
    const regex = /[^/]+$/;
    const match = process.env.ECS_CONTAINER_METADATA_URI_V4.match(regex);
    if (match) {
        CONTAINER_ID = match[0];
    }
}
