# Changelog

### 0.31.45-dev
- patch: add buyin and rebuy stats calculation to friends tables
- docs: Update helm chart and changelog

### 0.31.44
- docs: Update helm chart and changelog

### 0.31.44-stg
- chore: stage metrics cleanup
- docs: TOP-546 Adjust HPA to scale based on custom metrics for ACTIVE pods
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.31.43
- patch: implement pkw login timeout handling

### 0.31.43-stg
- docs: Update helm chart and changelog

### 0.31.43-dev
- patch: add delay between friends joinRoom and sitDown events
- docs: Update helm chart and changelog

### 0.31.42-dev
- patch: fix worker data typing: thread workerData does not contain platformId
- docs: Update helm chart and changelog

### 0.31.41-dev
- patch: Use ToolInBackpacks.Id as a ticketId
- docs: Update helm chart and changelog

### 0.31.40
- docs: Update helm chart and changelog

### 0.31.40-stg
- docs: Update helm chart and changelog

### 0.31.40-dev
- patch: improve logging for worker and processor
- chore: rework local job starter
- chore: increase termination grace period to 12 hours
- docs: Update helm chart and changelog

### 0.31.39-dev
- patch: friends - don't rebuy when recieving other players data
- docs: Testing out bullmq metric label
- docs: Testing out bullmq metric label
- docs: Testing out bullmq metric label
- docs: Update helm chart and changelog

### 0.31.38
- docs: Update helm chart and changelog

### 0.31.38-stg
- docs: Update helm chart and changelog

### 0.31.38-dev
- patch: update libs (bullmq, ioredis, bull-arena)
- docs: Update helm chart and changelog

### 0.31.37-dev
- patch: extend metrics with worker queue metrics
- Also rename bullmq_active_jobs_count to bullmq_node_jobs_count
- Resolves: ARMS-358
- chore: filter bullmq_node_utilization metric by active label for scaling
- docs: Update helm chart and changelog

### 0.31.36-dev
- patch: remove inverse worker metrics
- docs: Update helm chart and changelog

### 0.31.35-dev
- patch: refactoring: Eslint rule "no return await" added + PKW no-ignore (partially)
- docs: Update helm chart and changelog

### 0.31.34-dev
- patch: reset inverse worker metrics
- docs: Update helm chart and changelog

### 0.31.33-dev
- patch: fix active label for worker metrics
- chore: increasing minReplicas of botworker to 10
- docs: Update helm chart and changelog

### 0.31.32-dev
- patch: Short Cleanups
- docs: Update helm chart and changelog

### 0.31.31-dev
- patch: gradual shutdown for friends scan bot
- docs: Update helm chart and changelog

### 0.31.30-dev
- patch: send verifyDevice params in queryParams instead of body
- docs: Update helm chart and changelog

### 0.31.29-dev
- patch: remove temporary username migration
- docs: Update helm chart and changelog

### 0.31.28-dev
- patch: Eslint rules added; focused on  Await missing/misused
- docs: Update helm chart and changelog

### 0.31.27-dev
- patch: add test that pkw action changes from bet -> raise -> allin, make some methods await so they are testable
- chore: initialize required ENV vars for ACG deployment with URL_NOT_SET, as we don't use RWPK and WPTGO on ACG
- docs: Update helm chart and changelog

### 0.31.26
- docs: Update helm chart and changelog

### 0.31.26-stg
- docs: Update helm chart and changelog

### 0.31.26-dev
- patch: feature "buy Extra-time" was updated according to up-to-date requirements
- docs: Update helm chart and changelog

### 0.31.25-dev
- patch: feature "Show Cards" logic adjusted to up-to-date requirements
- docs: Update helm chart and changelog

### 0.31.24-dev
- patch: send Initialized status with balance job progress to eliminate manager logs error Invalid player status None in the job
- docs: Update helm chart and changelog

### 0.31.23-dev
- patch: error in onShowdownNoti fixed - Error player was not found
- docs: Update helm chart and changelog
- docs: cleanup mtt before resolving game promise

### 0.31.22-dev
- patch: unify caching mechanism and error handling in user data resolution
- docs: Update helm chart and changelog

### 0.31.21-dev
- patch: add base e2e test for mtt signupAndPlay
- docs: Update helm chart and changelog

### 0.31.20
- docs: Update helm chart and changelog

### 0.31.20-stg
- docs: Update helm chart and changelog

### 0.31.20-dev
- patch: fix area code usage in phone login parameters
- docs: Update helm chart and changelog

### 0.31.19-dev
- patch: remove mtt and wptgo env variables
- patch: use mock Unleash if UNLEASH_API_URL is empty
- docs: Update helm chart and changelog

### 0.31.18-dev
- patch: pkw cleanup. deduplicate _requestSitDown, remove comments and duplicate error handling
- docs: Update helm chart and changelog

### 0.31.17-dev
- patch: temporary restrict insurance on flop
- docs: Update helm chart and changelog

### 0.31.16-dev
- patch: some more logging for "Player with uid not found"
- docs: Update helm chart and changelog

### 0.31.15-dev
- patch: logging added to track problem "Player with uid  not found"
- chore: update mtt config prod
- docs: Update helm chart and changelog

### 0.31.14-dev
- patch: rework proxyUrl, don't pass pass proxy url string, but create a single HttpProxyAgent for the whole worker
- docs: Update helm chart and changelog

### 0.31.13-dev
- patch: ensure game state availability
- docs: Update helm chart and changelog

### 0.31.12-dev
- patch: filter tickets by expiry date
- docs: Update helm chart and changelog

### 0.31.11-dev
- patch: simplify wptgo login
- docs: Update helm chart and changelog

### 0.31.10-dev
- patch: remove unused pkw HTTP module
- docs: Update helm chart and changelog
- patch: refactor PKW datahandler, remove singleton pattern

### 0.31.9-dev
- patch: refactor PKW datahandler, remove singleton pattern

### 0.31.7
- docs: Update helm chart and changelog

### 0.31.7-stg
- docs: Update helm chart and changelog

### 0.31.7-dev
- patch: add feature flag for tickets fetching
- docs: Update helm chart and changelog

### 0.31.6
- docs: Update helm chart and changelog

### 0.31.6-stg
- docs: Update helm chart and changelog

### 0.31.6-dev
- patch: check action handling
- docs: Update helm chart and changelog

### 0.31.5-dev
- patch: format error message logging
- docs: Update helm chart and changelog

### 0.31.4
- docs: Update helm chart and changelog

### 0.31.4-stg
- docs: Update helm chart and changelog

### 0.31.4-dev
- patch: use safe user tickets check
- docs: Update helm chart and changelog

### 0.31.3-stg
- docs: Update helm chart and changelog

### 0.31.3-dev
- patch: use arrow functions for cache handling
- docs: Update helm chart and changelog

### 0.31.2
- docs: Update helm chart and changelog

### 0.31.2-stg
- docs: Update helm chart and changelog

### 0.31.2-dev
- patch: refactor tournament status check
- docs: Update helm chart and changelog

### 0.31.1-dev
- patch: refactor game adapter
- docs: Update helm chart and changelog

### 0.31.0-dev
- minor: re-launch rmtt
- Make mtt module configurable (url config, protobuf version, tag) to merge mtt and rmtt modules. Add missing configurations for rmtt.
- Resolves: ARMS-335
- patch: Feature/ARMS-321 fetch mtt tickets data in Balance job

### 0.30.62-dev
- patch: get rid of PKW RoundAction, use StrategyResponseAction instead. Change helper functions to kv objects
- docs: Update helm chart and changelog

### 0.30.61-dev
- patch: refactor - libs/shared is no longer ignored by Eslint
- docs: Update helm chart and changelog

### 0.30.60-dev
- patch: Refactor adaptors namings
- docs: Update helm chart and changelog

### 0.30.59-dev
- patch: delay shouldStop job command from jobData, unify `sleep` helper
- docs: Update helm chart and changelog

### 0.30.58-dev
- patch: new feature added - Show cards (Human-like behavior)
- docs: Update helm chart and changelog

### 0.30.57
- docs: Update helm chart and changelog

### 0.30.57-stg
- docs: Update helm chart and changelog

### 0.30.57-dev
- patch: adjust updateInfo calls for improved game state handling
- docs: Update helm chart and changelog

### 0.30.56-stg
- docs: Update helm chart and changelog

### 0.30.56-dev
- patch: update game state handling and fix tests
- Add test when already in room. Add test that zoom does not reset gameState after first buyin
- Approved-by: Illia Komsa
- docs: Update helm chart and changelog

### 0.30.55-dev
- patch: move update info to the right place
- docs: Update helm chart and changelog

### 0.30.54
- docs: Update helm chart and changelog

### 0.30.54-stg
- docs: Update helm chart and changelog

### 0.30.53-dev
- patch: fix error on user_ip=null

### 0.30.49
- docs: Update helm chart and changelog

### 0.30.50-dev
- patch: add test for pkwGame typeError

### 0.30.49-stg
- docs: Update helm chart and changelog

### 0.30.49-dev
- patch: initialize gameState with default values
- docs: Update helm chart and changelog
- patch: remove redundant pkw CV helpers
- patch: cleanup pwk server error codes
- docs: Update helm chart and changelog
- patch: raise "room not found" instead of TypeError in PKW
- patch: cleanup PKW CV module: remove single-function getLocation helper, use ByteBuffer in ByteArray directly without calling CV
- docs: Update helm chart and changelog
- patch: do not reenter tournaments in status 2
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: use unrecoverable error in tournament reentry handling
- docs: Update helm chart and changelog
- patch: ensure ante is defined in mtt blind structure
- TOP-500 docs: Removing launchdarkly secret from prod
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: ensure ante is defined in game state
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- TOP-500 patch: Triggering deployment
- TOP-500 Adding worker to ACG
- chore: update mtt config prod url

### 0.30.48-dev
- patch: remove redundant pkw CV helpers
- patch: cleanup pwk server error codes
- docs: Update helm chart and changelog

### 0.30.46-dev
- patch: raise "room not found" instead of TypeError in PKW

### 0.30.45-dev
- patch: do not reenter tournaments in status 2
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: use unrecoverable error in tournament reentry handling
- docs: Update helm chart and changelog
- patch: ensure ante is defined in mtt blind structure
- TOP-500 docs: Removing launchdarkly secret from prod
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: ensure ante is defined in game state
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- TOP-500 patch: Triggering deployment
- TOP-500 Adding worker to ACG
- chore: update mtt config prod url

### 0.30.44
- docs: Update helm chart and changelog

### 0.30.44-stg
- docs: Update helm chart and changelog

### 0.30.44-dev
- patch: use unrecoverable error in tournament reentry handling
- docs: Update helm chart and changelog

### 0.30.43-dev
- patch: ensure ante is defined in mtt blind structure
- TOP-500 docs: Removing launchdarkly secret from prod
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: ensure ante is defined in game state
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- TOP-500 patch: Triggering deployment
- TOP-500 Adding worker to ACG
- chore: update mtt config prod url

### 0.30.42-stg
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: ensure ante is defined in game state
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- TOP-500 patch: Triggering deployment
- TOP-500 Adding worker to ACG
- chore: update mtt config prod url

### 0.30.41
- this undefined
- wtf
- wtf
- no conflict
- return with probability
- cleanup
- cleanup
- error fixed
- Merge branch 'main' into feat/humanlike-show-card
- cleanup
- lottery removed
- debug logging + lottery
- debug logging + lottery
- requestShowCard update
- cleanup
- logic refactored
- tiny refactoring
- cleanup
- show card default changed
- cleanup
- cleanup
- ff test
- evaluation fixed
- feature flags refactoring
- tiny refactoring
- error fixed
- loggin improvements
- feature flags + job context
- Merge branch 'main' into feat/humanlike-show-card
- feature flag context in progress
- loggin update
- evaluations and attempt to make a decision added
- showdown evaluation added
- poker evaluator introduction
- random tries
- init

### 0.30.41-stg
- docs: Update helm chart and changelog

### 0.30.41-dev
- patch: ensure ante is defined in game state
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- TOP-500 patch: Triggering deployment
- TOP-500 Adding worker to ACG
- chore: update mtt config prod url

### 0.30.40-dev
- wtf
- wtf
- no conflict
- return with probability
- cleanup
- cleanup
- error fixed
- Merge branch 'main' into feat/humanlike-show-card
- cleanup
- lottery removed
- debug logging + lottery
- debug logging + lottery
- requestShowCard update
- cleanup
- logic refactored
- tiny refactoring
- cleanup
- show card default changed
- cleanup
- cleanup
- ff test
- evaluation fixed
- feature flags refactoring
- tiny refactoring
- error fixed
- loggin improvements
- feature flags + job context
- Merge branch 'main' into feat/humanlike-show-card
- feature flag context in progress
- loggin update
- evaluations and attempt to make a decision added
- showdown evaluation added
- poker evaluator introduction
- random tries
- init

### 0.30.39-dev
- TOP-500 patch: Triggering deployment
- docs: Update helm chart and changelog
- TOP-500 patch: Triggering deployment
- TOP-500 patch: Triggering deployment
- TOP-500 Adding worker to ACG
- chore: update mtt config prod url

### 0.30.38-dev
- TOP-500 patch: Triggering deployment

### 0.30.36-dev
- patch: refactor pkw messagecenter
- docs: Update helm chart and changelog
- patch: reduce timeout for strategy service request
- docs: Update helm chart and changelog
- patch: move encryptUtils module to outer folder
- docs: Update helm chart and changelog
- patch: remove unused pkw helpers
- docs: Update helm chart and changelog
- patch: generalized poker interface, pkw, mtt and friends now reuse single class for filling actions and calling strategyService
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: use get for mtt reenter api
- chore: update mtt config prod url
- docs: Update helm chart and changelog
- chore: add local stub for JobContext
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: use generic Error for tournament reentry

### 0.30.35-dev
- patch: reduce timeout for strategy service request
- docs: Update helm chart and changelog

### 0.30.34-dev
- patch: move encryptUtils module to outer folder
- docs: Update helm chart and changelog

### 0.30.33-dev
- patch: remove unused pkw helpers
- docs: Update helm chart and changelog
- patch: generalized poker interface, pkw, mtt and friends now reuse single class for filling actions and calling strategyService
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: use get for mtt reenter api
- chore: update mtt config prod url
- docs: Update helm chart and changelog
- chore: add local stub for JobContext
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: use generic Error for tournament reentry

### 0.30.32-dev
- patch: generalized poker interface, pkw, mtt and friends now reuse single class for filling actions and calling strategyService
- docs: Update helm chart and changelog

### 0.30.31
- docs: Update helm chart and changelog

### 0.30.31-stg
- docs: Update helm chart and changelog

### 0.30.31-dev
- patch: use get for mtt reenter api
- chore: update mtt config prod url
- docs: Update helm chart and changelog
- chore: add local stub for JobContext
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: use generic Error for tournament reentry

### 0.30.30
- chore: add local stub for JobContext
- docs: Update helm chart and changelog

### 0.30.30-stg
- docs: Update helm chart and changelog

### 0.30.30-dev
- patch: use generic Error for tournament reentry

### 0.30.29
- docs: Update helm chart and changelog

### 0.30.29-stg
- docs: Update helm chart and changelog
- patch: fix insurance calculation
- docs: Update helm chart and changelog
- patch: use native nodejs md5 hash instead of custom modules
- docs: Update helm chart and changelog
- patch: add JobContext for convenient data access
- docs: Update helm chart and changelog
- patch: remove unused code from PKW
- docs: PKW integration test suit example
- chore: update unleash API token in .env.local
- docs: Update helm chart and changelog
- patch: Buy extra time delay calculation fixed  + logging

### 0.30.29-dev
- patch: fix insurance calculation
- docs: Update helm chart and changelog
- patch: use native nodejs md5 hash instead of custom modules
- docs: Update helm chart and changelog
- patch: add JobContext for convenient data access
- docs: Update helm chart and changelog
- patch: remove unused code from PKW
- docs: PKW integration test suit example
- chore: update unleash API token in .env.local
- docs: Update helm chart and changelog
- patch: Buy extra time delay calculation fixed  + logging

### 0.30.28-dev
- patch: use native nodejs md5 hash instead of custom modules
- docs: Update helm chart and changelog

### 0.30.27-dev
- patch: add JobContext for convenient data access
- docs: Update helm chart and changelog
- patch: remove unused code from PKW
- docs: PKW integration test suit example
- chore: update unleash API token in .env.local
- docs: Update helm chart and changelog
- patch: Buy extra time delay calculation fixed  + logging

### 0.30.26-dev
- patch: remove unused code from PKW
- docs: PKW integration test suit example
- chore: update unleash API token in .env.local
- docs: Update helm chart and changelog

### 0.30.25-dev
- patch: Buy extra time delay calculation fixed  + logging

### 0.30.24
- docs: Update helm chart and changelog

### 0.30.24-stg
- docs: Update helm chart and changelog

### 0.30.24-dev
- patch: change api call for mtt reenter to post
- docs: Update helm chart and changelog

### 0.30.23-stg
- docs: fix config values
- docs: Update helm chart and changelog

### 0.30.23-dev
- patch: reduce timeout for mtt api calls to 20s
- docs: Update helm chart and changelog

### 0.30.22-dev
- patch: adjust insurance request delay
- docs: Update helm chart and changelog

### 0.30.20-dev
- patch: improve tournament connection logic to check readiness status

### 0.30.19-dev
- patch: reduce lock timeouts for job processing and base them on feature flag
- docs: Update helm chart and changelog

### 0.30.18-dev
- patch: added feature - request more action time
- docs: Update helm chart and changelog

### 0.30.17-dev
- patch: adjust all-in players condition to check stack against ante
- docs: Update helm chart and changelog

### 0.30.16-dev
- patch: fix RoomData initialization
- docs: Update helm chart and changelog

### 0.30.15-dev
- patch: improve error handling for tournament detail fetching

### 0.30.14-dev
- patch: remove unused pkw variables, comments in chinese and outdated console.log messages, refactor PkwMain
- docs: Update helm chart and changelog

### 0.30.13-dev
- patch: removed unused GameDataManager params and methods, move GameDataManager from separate module to a single object
- patch: pkw garbage cleanup
- docs: Update helm chart and changelog

### 0.30.10-dev
- patch: remove pkwMain unused init param

### 0.30.9
- docs: Update helm chart and changelog

### 0.30.9-stg
- docs: Update helm chart and changelog

### 0.30.8-dev
- patch: remove satellite mode limitations from tournament scanning logic

### 0.30.7-stg
- docs: Update helm chart and changelog

### 0.30.7-dev
- patch: onUserActionDone for rebuy
- docs: Update helm chart and changelog

### 0.30.6-dev
- patch: improve player stats reporting in game state updates
- There is no need to update stats manually, we can directly use latest stats values when needed.

### 0.30.5-stg
- docs: Update helm chart and changelog

### 0.30.5-dev
- patch: refactor rebuy logic and simplify player actions
- docs: Update helm chart and changelog
- patch: add mtt reenter feature
- chore: update strategy service URLs in .env

### 0.30.4-dev
- patch: add mtt reenter feature
- chore: update strategy service URLs in .env

### 0.30.0
- docs: Update helm chart and changelog
- patch: unify login error handling across platforms

### 0.30.3-dev
- patch: unify login error handling across platforms

### 0.30.2-dev
- patch: stop bot gradually after max rebuy count reached
- docs: Update helm chart and changelog

### 0.30.1-dev
- patch: unify PKW scan - move tables filtering out of pkwRoom to adapter
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- minor: Implement Human-like bot's insurance processing, based on the number of opponent's outs

### 0.30.0-stg
- docs: Update helm chart and changelog

### 0.30.0-dev
- minor: Implement Human-like bot's insurance processing, based on the number of opponent's outs
- docs: Update helm chart and changelog

### 0.29.2-stg
- docs: Update helm chart and changelog

### 0.29.2-dev
- patch: fix stuck players in friends when websocket disconnected (no reconnect yet)
- docs: Update helm chart and changelog

### 0.29.1-dev
- patch: change requested seatNum for friends table if recommended seatNum is occupied
- docs: Update helm chart and changelog

### 0.29.0-dev
- Merged in feat/bot-insurance (pull request #432)
- Feat/bot insurance
- Implement Human-like bot's insurance processing, based on the number of opponent's outs
- fix tests
- Apply PR comments
- Apply PR comments
- Apply PR comments
- Apply PR comments
- Apply PR comments
- Apply PR comments
- Apply PR comments
- Merge remote-tracking branch 'origin/main' into feat/bot-insurance
- Resolve imports issues
- minor: Implement Human-like bot's insurance processing, based on the number of opponent's outs
- Resolve conflicts
- Merged in feat/bot-insurance (pull request #430)
- Implement Human-like bot's insurance processing, based on the number of opponent's outs
- Implement Human-like bot's insurance processing, based on the number of opponent's outs
- fix tests
- Apply PR comments
- Apply PR comments
- Apply PR comments
- Apply PR comments
- Apply PR comments
- Apply PR comments
- Apply PR comments
- Merge remote-tracking branch 'origin/main' into feat/bot-insurance
- Resolve imports issues
- Approved-by: Illia Komsa
- docs: Update helm chart and changelog
- patch: change some callbacks to promises

### 0.28.22-dev
- patch: change some callbacks to promises
- docs: Update helm chart and changelog

### 0.28.21-dev
- patch: check for clubId in job data for froends adapter
- docs: Update helm chart and changelog

### 0.28.20-dev
- patch: refactor PKW domain manager
- docs: Update helm chart and changelog

### 0.28.19
- docs: Update helm chart and changelog

### 0.28.19-stg
- docs: Update helm chart and changelog

### 0.28.19-dev
- patch: fix player balance update
- docs: Update helm chart and changelog

### 0.28.18
- docs: Update helm chart and changelog

### 0.28.18-stg
- docs: update prod strategy service URL
- docs: Update helm chart and changelog

### 0.28.18-dev
- patch: split rebuy and buyin logic
- docs: Update helm chart and changelog

### 0.28.17-dev
- patch: scan bot for friends scans only tables of one club
- docs: Update helm chart and changelog

### 0.28.16
- docs: Update helm chart and changelog

### 0.28.16-stg
- docs: Update helm chart and changelog

### 0.28.16-dev
- patch: fix ts error

### 0.28.13-stg
- docs: Update helm chart and changelog

### 0.28.13-dev
- patch: change logging platform to number. catch json parse errors of wpkHttpRequest
- docs: Update helm chart and changelog

### 0.28.12
- docs: Update helm chart and changelog

### 0.28.12-stg
- docs: Update helm chart and changelog

### 0.28.12-dev
- patch: add logging to mtt websocket error handler
- docs: Update helm chart and changelog

### 0.28.11-stg
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.28.11-dev
- patch: bump version

### 0.28.9
- docs: Update helm chart and changelog

### 0.28.9-stg
- docs: Update helm chart and changelog

### 0.28.9-dev
- patch: v2 MTT Protobuf update for PROD
- docs: Update helm chart and changelog

### 0.28.7-dev
- patch: add `platform` field to logging, containing appId
- docs: Update helm chart and changelog

### 0.28.6-dev
- patch: scan bot support for friends
- docs: Update helm chart and changelog

### 0.28.5-dev
- patch: bump version
- docs: Update helm chart and changelog

### 0.28.4-dev
- patch: fix job data handler for friends
- docs: Update helm chart and changelog

### 0.28.3-dev
- patch: merge friends into adapters simplify refactoring
- patch: simplify adapters interface
- refactor: add missing type
- refactor: remove redundant params
- refactor: simplify pkw
- docs: Update helm chart and changelog

### 0.28.2-dev
- patch: disable gto-glue test
- patch: send friends data to manager via job, remove separate login for friends, allow creating new friends tables from manager
- docs: Update helm chart and changelog

### 0.28.1-dev
- patch: change test runners to tsx
- patch: remove teamcity builds
- docs: Update helm chart and changelog

### 0.28.0-dev
- minor: add friends tables game mode
- docs: Update helm chart and changelog

### 0.27.8-dev
- patch: isCurrentGameZoom helper

### 0.27.7-dev
- patch: attempt to fix "no self player found" error
- docs: Update helm chart and changelog

### 0.27.6-dev
- patch: verbose logging for "self player not found" issue
- docs: add test for pkw greatestBet
- docs: Update helm chart and changelog

### 0.27.5-dev
- patch: NLHE value reverted to align with Manager
- docs: Update helm chart and changelog

### 0.27.4-dev
- patch: extend pkw to allow full scan
- docs: fix number in configmap
- docs: fix numbers in configmap
- docs: Update helm chart and changelog

### 0.27.3-dev
- patch: implement dynamic mtt configuration loading

### 0.27.2
- docs: Update helm chart and changelog

### 0.27.2-stg
- docs: Update helm chart and changelog

### 0.27.2-dev
- patch: reset greatest bet per calculation
- docs: Update helm chart and changelog

### 0.27.1
- docs: Update helm chart and changelog

### 0.27.1-stg
- docs: Update helm chart and changelog

### 0.27.1-dev
- patch: add temporary wptgo username backfill logic
- docs: Update helm chart and changelog

### 0.27.0
- docs: Update helm chart and changelog

### 0.27.0-stg
- docs: Update helm chart and changelog

### 0.27.0-dev
- minor: updated dependencies
- patch: replace fetch with axios for mtt http requests
- docs: Update helm chart and changelog

### 0.26.36
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.26.37-dev
- patch: Added new endpoint - get /user_id

### 0.26.36-dev
- patch: fix mttPro import
- docs: Update helm chart and changelog

### 0.26.35-dev
- patch: dynamic import proto with require
- docs: Update helm chart and changelog

### 0.26.34
- docs: Update helm chart and changelog
- docs: checking version being used

### 0.26.34-stg
- docs: Update helm chart and changelog

### 0.26.34-dev
- patch: add protobuf version debug logs
- docs: Update helm chart and changelog

### 0.26.33-dev
- patch: proper token for transfer 101
- docs: Update helm chart and changelog

### 0.26.32
- docs: Update helm chart and changelog

### 0.26.32-stg
- docs: Update helm chart and changelog

### 0.26.32-dev
- patch: support multiple mtt proto versions
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: improve wpk login errors
- docs: Update helm chart and changelog
- patch: update mtt proto
- docs: Update helm chart and changelog
- patch: BetProfile and StrategyProfile updates (only applicable to MTT and PKW accordingly)
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: ProfileName is added to pkw adapted and to shared lib

### 0.26.31-stg
- docs: Update helm chart and changelog

### 0.26.31-dev
- patch: improve wpk login errors
- docs: Update helm chart and changelog

### 0.26.30-dev
- patch: update mtt proto

### 0.26.29-dev
- patch: BetProfile and StrategyProfile updates (only applicable to MTT and PKW accordingly)

### 0.26.28-stg
- docs: Update helm chart and changelog

### 0.26.28-dev
- patch: ProfileName is added to pkw adapted and to shared lib

### 0.26.27-dev
- patch: proper transfer response handling

### 0.26.26-dev
- patch: always run pkwMain with GameModeCode Normal. add tests
- docs: Update helm chart and changelog

### 0.26.25-dev
- patch: bunch of small refactorings
- mtt http api migrate to fetch from axios
- add failing of mtt play bot on reconnect timeout after 5 attempts
- fail transfer bot for WPK on login error instead of success
- docs: Update helm chart and changelog

### 0.26.24-dev
- patch: MTT typings and refactorings
- docs: Update helm chart and changelog

### 0.26.23-dev
- patch: pass mtt url config in arguments
- instead of setting up MTT urls weirdly as writable fields in separate module, pass it as a single object
- Also remove phone field from mtt player
- docs: Update helm chart and changelog

### 0.26.22
- docs: Update helm chart and changelog

### 0.26.22-stg
- docs: Update helm chart and changelog

### 0.26.22-dev
- patch: fix game mode code for non-mtt games
- docs: Update helm chart and changelog

### 0.26.21
- docs: Update helm chart and changelog

### 0.26.21-stg
- docs: Update helm chart and changelog

### 0.26.21-dev
- patch: use RL as default strategy service param

### 0.26.19-dev
- patch: fix tests
- docs: Update helm chart and changelog

### 0.26.18-dev
- patch: obfuscate sensitive data in strategy service calls
- docs: Update helm chart and changelog

### 0.26.17-dev
- patch: add profileName support
- docs: Update helm chart and changelog

### 0.26.16-dev
- patch: log websocket messageid
- docs: Update helm chart and changelog

### 0.26.15-dev
- patch: simplify wpk request handling logic
- docs: Update helm chart and changelog

### 0.26.14
- docs: Update helm chart and changelog

### 0.26.14-stg
- docs: Update helm chart and changelog

### 0.26.14-dev
- patch: always cancel autoplay on room snapshot event
- docs: Update helm chart and changelog

### 0.26.13
- docs: Update helm chart and changelog

### 0.26.13-stg
- docs: Update helm chart and changelog

### 0.26.13-dev
- patch: fix dependencies

### 0.26.12-stg
- docs: Update helm chart and changelog

### 0.26.12-dev
- patch: hash sensitive state data
- docs: Update helm chart and changelog
- patch: handle mtt enter game NO_MTT_ROOM error
- docs: Update helm chart and changelog
- patch: remove playerId, jobType and platform from logging, they all are present in jobId
- docs: Update helm chart and changelog
- patch: improve mtt error handling

### 0.26.11-dev
- patch: handle mtt enter game NO_MTT_ROOM error
- docs: Update helm chart and changelog

### 0.26.10-dev
- patch: remove playerId, jobType and platform from logging, they all are present in jobId
- docs: Update helm chart and changelog

### 0.26.9-dev
- patch: improve mtt error handling

### 0.26.8
- docs: Update helm chart and changelog

### 0.26.8-stg
- docs: Update helm chart and changelog

### 0.26.8-dev
- patch: bump version
- Do not disconnect game websocket when receiving onPlayerLeaveMsg
- Approved-by: nikolai.liubavin
- Approved-by: Alexandr Maximov

### 0.26.7
- docs: Update helm chart and changelog

### 0.26.7-stg
- docs: Update helm chart and changelog

### 0.26.7-dev
- patch: handle wptgo login error
- docs: Update helm chart and changelog

### 0.26.6-dev
- patch: fix mtt events handling
- docs: Update helm chart and changelog

### 0.26.5-dev
- patch: add timeouts to wpk requests
- docs: Update helm chart and changelog

### 0.26.4
- docs: Update helm chart and changelog

### 0.26.4-stg
- docs: Update helm chart and changelog

### 0.26.4-dev
- patch: fix websocket initialization
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: handle mtt login error response

### 0.26.3
- docs: Update helm chart and changelog

### 0.26.3-stg
- docs: Update helm chart and changelog

### 0.26.3-dev
- patch: handle mtt login error response

### 0.26.2
- [No-Jira] Increasing memory limits for botworker on dev/stg
- [No-Jira] Increasing memory limits for botworker on dev/stg
- [No-Jira] Increasing memory limits for botworker on dev/stg
- docs: fix configmap
- docs: Update helm chart and changelog

### 0.26.2-stg
- docs: Update helm chart and changelog

### 0.26.2-dev
- patch: unify rwpk platform load
- docs: Update helm chart and changelog

### 0.26.1-dev
- Merged in fix/handle_device_verification_error (pull request #377)
- patch: add error handling for device verification errors
- patch: add error handling for device verification errors
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.26.0-dev
- minor: add currency field to transfer bot data
- docs: Update helm chart and changelog
- patch: Strategy URL Override unleash flag added
- docs: Update helm chart and changelog
- patch: refactor pkw action making
- remove unused immediate action flag
- rename internal RoundActions
- move static arguments from cvGameNetRequestAction args to calculating inside the method
- docs: Update helm chart and changelog
- patch: minor refactoring and typings
- docs: Update helm chart and changelog
- add iphone useragent and content-type to wpk request
- patch: refactor wpkRobotHttpReq: switch to fetch, change crypto methods to node:crypto

### 0.25.70-dev
- patch: Strategy URL Override unleash flag added
- docs: Update helm chart and changelog

### 0.25.69-dev
- patch: refactor pkw action making
- remove unused immediate action flag
- rename internal RoundActions
- move static arguments from cvGameNetRequestAction args to calculating inside the method
- docs: Update helm chart and changelog

### 0.25.68-dev
- patch: minor refactoring and typings
- docs: Update helm chart and changelog

### 0.25.67-dev
- add iphone useragent and content-type to wpk request
- patch: refactor wpkRobotHttpReq: switch to fetch, change crypto methods to node:crypto

### 0.25.66-dev
- patch: add appId to scan bot table and tournament infos sent to manager

### 0.25.65-dev
- patch: move connection status and inner state callbacks from mtt.ts to player.ts
- Connection status is now controlled completely in BasicPlayer
- Reconnection to websocket is handled in BasicPlayer
- seatNum is now stored in GamePlayer
- Raw websocket data sending is now entirely in Player
- docs: Update helm chart and changelog

### 0.25.64-dev
- patch: remove blworld config params
- patch: remove unused ENV MTT config variable, inject config as a param
- docs: Update helm chart and changelog

### 0.25.63-stg
- docs: update non-prod strategy service URLs
- docs: Update helm chart and changelog

### 0.25.63-dev
- patch: remove unused MTT player methods
- docs: Update helm chart and changelog

### 0.25.62-dev
- patch: enable source maps
- TOP-426 docs: Disabling fluentbit sidecar
- docs: Update helm chart and changelog

### 0.25.61-dev
- patch: remove unused PKW variables
- docs: Update helm chart and changelog

### 0.25.59-dev
- patch: change TipsArray to logging.info, remove getStringData that does nothing
- docs: Update helm chart and changelog

### 0.25.58
- docs: Update helm chart and changelog

### 0.25.58-stg
- docs: Update helm chart and changelog

### 0.25.58-dev
- patch: cleanup [object Object] from logs
- docs: Update helm chart and changelog

### 0.25.57-dev
- patch: wptgo websocket logging added
- docs: Update helm chart and changelog

### 0.25.56-dev
- patch: fix crash on wpk login error handling
- docs: Update helm chart and changelog

### 0.25.55
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.25.55-stg
- patch: add bet_profile_config to logging of strategy service calls

### 0.25.54-dev
- patch: add logging for bet_profile_config
- docs: Update helm chart and changelog

### 0.25.53-dev
- remove mtt info from strategy service logs
- patch: remove duplicate logs for strategy service
- TOP-426 docs: Adding fluentbit sidecontainer for botworker
- docs: Update helm chart and changelog

### 0.25.52-dev
- patch: remove error logs which are not error
- docs: Update helm chart and changelog

### 0.25.51-dev
- patch: simplify PKW websocket handling, remove unused chain of callbacks
- add connectionstatus check
- close websocket after handler removal
- fix login server callback invokation
- docs: Update helm chart and changelog

### 0.25.50-dev
- patch: libs/pkw: js files transformed to ts, redundant files and methods cleaned-up
- docs: Update helm chart and changelog

### 0.25.49-dev
- patch: fix logging for requestMttTournamentDetail
- docs: Update helm chart and changelog
- patch: improve typings and logs
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.25.48-dev
- patch: improve typings and logs
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.25.47
- docs: Update helm chart and changelog

### 0.25.47-stg
- patch: Merged in fix/roomid-logging-fix (pull request #350)
- Approved-by: Illia Komsa
- patch: add active label to worker metrics
- patch: add strict typing to job progress update messages. move common types to shared
- docs: Update helm chart and changelog

### 0.25.45
- docs: Update helm chart and changelog

### 0.25.47-dev
- patch: fix logging of wpkRequest error
- docs: Update helm chart and changelog

### 0.25.46-dev
- patch: reorder logging fields
- docs: Update helm chart and changelog

### 0.25.45-stg
- docs: Update helm chart and changelog

### 0.25.45-dev
- patch: return encrypted password
- docs: Update helm chart and changelog

### 0.25.44-dev
- patch: Merged in feat/room-id-logging-fix (pull request #346)
- RoomId logging issue fixed
- Approved-by: Illia Komsa
- Approved-by: Aleksei Klimakov
- docs: Update helm chart and changelog

### 0.25.43-dev
- patch: migrate from jest to node:test test runner
- change test calls
- fix failing test
- docs: TOP-404 Adjust metrics averageValue
- docs: Update helm chart and changelog

### 0.25.42-stg
- docs: TOP-404 Fix HPA to get bullmq_node_utilization metrics

### 0.25.42-dev
- patch: fix build
- docs: TOP-404 Temporary disable HPA

### 0.25.40-dev
- patch: TOP-404 Update helm chart adding HPA based on custom metric
- docs: Update helm chart and changelog

### 0.25.39-dev
- patch: group some MTT types to one file, migrate couple of files to Typescript
- docs: Update helm chart and changelog

### 0.25.38-dev
- patch: remove webpack dependency. we were using it just to copy other files to dist anyways
- docs: Update helm chart and changelog

### 0.25.37-dev
- patch: logging response with ErrorCode 0 produced errors in grafana. cleaned up
- rename wpkRobot to typescript for straigtforward module resolution
- patch: refactor wpkRequest callback gently
- fix logging import
- patch: rework wpkRobotHttpReq to our logging factory, remove underscore dependency
- fix indentation
- docs: Update helm chart and changelog

### 0.25.36
- docs: Update helm chart and changelog

### 0.25.36-stg
- docs: Update helm chart and changelog

### 0.25.36-dev
- patch: fix mtt data fetch
- docs: Update helm chart and changelog

### 0.25.35
- docs: Update helm chart and changelog

### 0.25.35-stg
- docs: Update helm chart and changelog

### 0.25.35-dev
- patch: fix mtt data fetch
- docs: Update helm chart and changelog

### 0.25.34-dev
- patch: fix Unleash wrong env variable name. Rename some files to camelCase
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.25.33-dev
- patch: fix tsc build

### 0.25.27-dev
- patch: Merged in feat/mtt-logging-improvements (pull request #300)
- Approved-by: Illia Komsa
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog
- patch: remove unused laytpl lib, switch pako from embedded file to npm module

### 0.25.26-dev
- init-debug

### 0.25.25-dev
- patch: convert ecdh to esm module
- docs: Update helm chart and changelog

### 0.25.24-dev
- patch: fix window is undefined in ecdh
- docs: Update helm chart and changelog

### 0.25.23-dev
- patch: remove JSDOM dependency
- docs: Update helm chart and changelog

### 0.25.22
- docs: Update helm chart and changelog

### 0.25.22-stg
- docs: Update helm chart and changelog

### 0.25.22-dev
- patch: non-mtt games seat positions
- docs: Update helm chart and changelog

### 0.25.21-dev
- patch: reduce long logline that can hang Grafana
- docs: Update helm chart and changelog

### 0.25.20-dev
- patch: change custom HashMap implementation to Javascript internal Map
- docs: Update helm chart and changelog

### 0.25.19
- docs: Update helm chart and changelog

### 0.25.19-stg
- docs: Update helm chart and changelog
- patch: remove unused ecdh library. our internal ecdh.js is significantly different and contains other logic

### 0.25.19-dev
- patch: remove unused ecdh library. our internal ecdh.js is significantly different and contains other logic

### 0.25.18-dev
- patch: clamp some logs length
- docs: Update helm chart and changelog

### 0.25.17
- docs: Update helm chart and changelog

### 0.25.17-stg
- docs: Update helm chart and changelog

### 0.25.17-dev
- patch: fix logging context
- docs: Update helm chart and changelog

### 0.25.16
- docs: Update helm chart and changelog

### 0.25.16-stg
- docs: Update helm chart and changelog

### 0.25.16-dev
- patch: improve throttler with queuing
- docs: Update helm chart and changelog

### 0.25.15-dev
- patch: load platform strategy dynamically based on platformId
- docs: Update helm chart and changelog

### 0.25.14-dev
- patch: fix circular dependency error when logging
- docs: Update helm chart and changelog

### 0.25.13-dev
- patch: Merged in feat/logging-context-updated (pull request #320)
- logging context updated
- Approved-by: Illia Komsa
- docs: Update helm chart and changelog

### 0.25.12
- docs: Update helm chart and changelog

### 0.25.12-stg
- docs: Update helm chart and changelog

### 0.25.12-dev
- patch: fix game mode code for zoom
- docs: Update helm chart and changelog

### 0.25.11
- docs: Update helm chart and changelog

### 0.25.11-stg
- docs: Update helm chart and changelog

### 0.25.11-dev
- patch: Merged in fix/protobuf_long_int_fix (pull request #318)
- Approved-by: Illia Komsa
- Approved-by: Aleksei Klimakov
- docs: Update helm chart and changelog

### 0.25.10-stg
- docs: Update helm chart and changelog
- patch: set special client type in pkw for WPTGO

### 0.25.10-dev
- patch: set special client type in pkw for WPTGO
- docs: Update helm chart and changelog

### 0.25.9-dev
- patch: add mtt support with wptgo
- docs: Update helm chart and changelog

### 0.25.8
- docs: Update helm chart and changelog

### 0.25.8-stg
- docs: Update helm chart and changelog

### 0.25.8-dev
- patch: fix game mode code for splash
- docs: Update helm chart and changelog

### 0.25.7
- docs: Update helm chart and changelog

### 0.25.7-stg
- docs: Update helm chart and changelog

### 0.25.7-dev
- patch: fake commit
- Merged in feat/shortdeck-adapter (pull request #304)
- Feat/shortdeck adapter
- room start params updated
- mtt fixed
- fetchStrategy logg
- merged with main
- partial removed
- tweak for big_blind value
- Merge branch 'main' into feat/shortdeck-adapter
- merge issue fixed
- room start params reverted
- changed this._state to this._roomSt
- cleanup
- merge with main
- some more logging added
- ante and bigblind amount fixed
- trying to solve reached max limit
- a bit more logging
- buyIn for R1 uadjusted
- buyin min on pkwgame removed
- cleanup
- multi 100 removed
- reverted
- rebuy again
- Merged main into feat/shortdeck-adapter
- game_type_code removed
- Merged main into feat/shortdeck-adapter
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.25.6-dev
- patch: fix wrong room situation callback, Remove handler for roomSituation from pkwRoom
- docs: Update helm chart and changelog

### 0.25.5-dev
- patch: Change the transfer enpoint based on the AppType
- docs: Update helm chart and changelog

### 0.25.4-dev
- patch: fix user data handling
- docs: Update helm chart and changelog

### 0.25.3-stg
- docs: Update helm chart and changelog

### 0.25.3-dev
- patch: fix platformId property name in user information parsing
- docs: Update helm chart and changelog

### 0.25.2-dev
- patch: add node utilization metric

### 0.25.0-dev
- minor: WPK transfer refactor, WPTGO transfer

### 0.24.9-stg
- docs: Update helm chart and changelog

### 0.24.9-dev
- patch: downgrade wpkHttpURL version parameter to 5.8.8.14
- docs: Update helm chart and changelog

### 0.24.8-dev
- Revert "patch: proposal to migrate from jest to node-test test framework"
- This reverts commit ac9e89dc7c54b2796991769884d781a26856f24a.

### 0.24.5-dev
- patch: remove old way of calculating total buy in and played hands count
- patch: override totalBuyIn from onRoomSituation
- If player disconnects and then reconnects to the same room, their totalBuyIn is calculated incorrectly. Use info provided by the game server instead
- docs: Update helm chart and changelog

### 0.24.4-dev
- patch: migrate feature flags to unleash
- docs: add unleash token secret
- docs: Update helm chart and changelog

### 0.24.3-stg
- docs: Update helm chart and changelog

### 0.24.3-dev
- patch: fix wrong buy in every time player receives NoticePlayerStay
- TOP-402 Add UNLEASH_API_TOKEN secret to helm chart
- docs: Update helm chart and changelog

### 0.24.2
- docs: change mtt api url
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- add typing for job type
- review fixes
- patch: Fix rebuy happening every time somebody stands up from the table
- patch: add R6 game: Diamond Splash
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- minor: add bet profile to strategy
- Also remove unused feature flag calls.

### 0.24.2-stg
- docs: Update helm chart and changelog

### 0.24.2-dev
- add typing for job type
- review fixes
- patch: Fix rebuy happening every time somebody stands up from the table
- patch: add R6 game: Diamond Splash
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- minor: add bet profile to strategy
- Also remove unused feature flag calls.

### 0.24.0
- docs: Update helm chart and changelog

### 0.24.0-stg
- docs: Update helm chart and changelog

### 0.24.0-dev
- minor: add bet profile to strategy
- Also remove unused feature flag calls.
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: fix mtt player status
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: add wptgo prod URL
- patch: change wptgo deafult identifier
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: add linear reconnect delay for mtt
- docs: Update helm chart and changelog
- minor: add WPTGO platform support

### 0.23.4
- patch: fix mtt player status

### 0.23.3
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.23.3-stg
- patch: add wptgo prod URL

### 0.23.1-stg
- docs: Update helm chart and changelog

### 0.23.1-dev
- patch: add linear reconnect delay for mtt
- docs: Update helm chart and changelog
- minor: add WPTGO platform support

### 0.23.0-dev
- minor: add WPTGO platform support

### 0.22.32
- docs: Update helm chart and changelog

### 0.22.32-stg
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.22.32-dev
- patch: fake commit

### 0.22.31-dev
- Merged in feature/pkw_protobuf_logs (pull request #297)
- Feature/pkw protobuf logs
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog
- patch: cleanup protobuf logging

### 0.22.30-dev
- patch: generate device ID only if missing
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.22.29
- docs: Update helm chart and changelog

### 0.22.29-stg
- docs: Update helm chart and changelog

### 0.22.29-dev
- patch: add wait mechanism for bomb pot games to handle community cards event delay

### 0.22.26-dev
- patch: add seat number to player action
- patch: add seat number to player action
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.22.25
- docs: Update helm chart and changelog

### 0.22.25-dev
- patch: handle out-of-memory error by ensuring next user action is called only on success

### 0.22.24
- docs: Update helm chart and changelog

### 0.22.24-stg
- docs: Update helm chart and changelog

### 0.22.24-dev
- patch: Merged in fix/calculated_delay_fix (pull request #289)
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.22.23-dev
- patch: close connection on reconnection
- docs: Update helm chart and changelog

### 0.22.22-dev
- patch: Move Redis connection to separate file, cleanup env variables usage
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.22.21
- docs: Update helm chart and changelog

### 0.22.21-stg
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.22.21-dev
- patch: Merged in feat/zoom-allin-issue-fix (pull request #286)
- Approved-by: Alexandr Maximov

### 0.22.19-dev
- patch: do not fix seats if players are missing

### 0.22.18
- docs: Update helm chart and changelog

### 0.22.18-stg
- docs: Update helm chart and changelog

### 0.22.18-dev
- patch: refactor pb decoding with better error handling and buffer normalization

### 0.22.17
- docs: Update helm chart and changelog

### 0.22.17-stg
- docs: Update helm chart and changelog

### 0.22.17-dev
- patch: log init heap stats on job start
- docs: Update helm chart and changelog

### 0.22.16
- docs: Update helm chart and changelog

### 0.22.16-stg
- docs: Update helm chart and changelog

### 0.22.16-dev
- patch: adjust logging for leave room responses
- docs: Update helm chart and changelog

### 0.22.15
- docs: Update helm chart and changelog

### 0.22.15-stg
- docs: Update helm chart and changelog

### 0.22.15-dev
- patch: add worker heap total and external memory metrics
- docs: Update helm chart and changelog

### 0.22.14
- docs: Update helm chart and changelog

### 0.22.14-stg
- docs: Update helm chart and changelog

### 0.22.14-dev
- patch: add worker heap metrics reporting

### 0.22.13
- docs: Update helm chart and changelog

### 0.22.13-stg
- docs: Update helm chart and changelog

### 0.22.13-dev
- patch: downgrade protobufjs for mtt
- docs: Update helm chart and changelog

### 0.22.12-dev
- patch: add logging for leave room response
- docs: Update helm chart and changelog

### 0.22.11-dev
- patch: handle empty response check error
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: fix event conversion error
- Event logging caused 'Converting circular structure to JSON' error.

### 0.22.10
- docs: Update helm chart and changelog

### 0.22.10-stg
- docs: Update helm chart and changelog

### 0.22.10-dev
- patch: fix event conversion error
- Event logging caused 'Converting circular structure to JSON' error.

### 0.22.9
- docs: Update helm chart and changelog

### 0.22.9-stg
- docs: Update helm chart and changelog

### 0.22.9-dev
- patch: update protobufjs versions
- docs: Update helm chart and changelog

### 0.22.8
- docs: Update helm chart and changelog

### 0.22.8-stg
- docs: Update helm chart and changelog

### 0.22.8-dev
- patch: load game adapters dynamically to reduce memory footprint
- Merged in fix/extreme-logging-removed (pull request #282)
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.22.7
- docs: Update helm chart and changelog

### 0.22.7-stg
- docs: Update helm chart and changelog

### 0.22.7-dev
- patch: remove source map flag from Dockerfile
- docs: Update helm chart and changelog

### 0.22.6
- docs: Update helm chart and changelog

### 0.22.6-stg
- docs: Update helm chart and changelog

### 0.22.6-dev
- patch: exclude dependencies (node_modules) from source map generation
- docs: Update helm chart and changelog

### 0.22.5
- docs: Update helm chart and changelog

### 0.22.5-stg
- docs: Update helm chart and changelog

### 0.22.5-dev
- patch: await async function calls in entrypoint
- docs: Update helm chart and changelog

### 0.22.4
- docs: Update helm chart and changelog

### 0.22.4-stg
- docs: Update helm chart and changelog

### 0.22.4-dev
- patch: improve error handling in game adapter
- In case of the missing mtt token we have to clear the cache and re-login.
- docs: Update helm chart and changelog

### 0.22.3-dev
- patch: enable source maps via flag
- docs: Update helm chart and changelog

### 0.22.2-dev
- patch: use different protobufjs versions in workspaces
- This should preserve isolation and prevents overrides by different workspaces
- docs: Update helm chart and changelog

### 0.22.1-dev
- patch: Merged in feature/logging-improvements (pull request #279)
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.22.0
- docs: Update helm chart and changelog

### 0.22.0-stg
- docs: Update helm chart and changelog

### 0.22.0-dev
- minor: feature persist login token

### 0.21.22
- docs: Update helm chart and changelog

### 0.21.22-stg
- docs: Update helm chart and changelog

### 0.21.22-dev
- patch: use protobufjs fixed 7.5.0 to preserve isolation
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: improve WebSocket connection handling
- patch: handle tournament end case
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: Merged in feature/logging-improvements (pull request #276)
- Approved-by: Aleksei Klimakov

### 0.21.21
- docs: Update helm chart and changelog

### 0.21.21-stg
- docs: Update helm chart and changelog

### 0.21.20-dev
- patch: improve WebSocket connection handling

### 0.21.19
- docs: Update helm chart and changelog

### 0.21.19-stg
- docs: Update helm chart and changelog

### 0.21.19-dev
- patch: Merged in feature/logging-improvements (pull request #276)
- Approved-by: Aleksei Klimakov
- docs: Update helm chart and changelog
- Merged in TOP-339-prod (pull request #272)
- TOP-339 Ability for prometheus to scrape the custom metrics
- TOP-339 Ability for prometheus to scrape the custom metrics
- Approved-by: Alexandr Maximov

### 0.21.18
- docs: Update helm chart and changelog

### 0.21.18-stg
- docs: Update helm chart and changelog

### 0.21.18-dev
- patch: Merged in feature/logging-improvements (pull request #270)
- TOP-339 Testing how botworker behaves on annotation updates
- TOP-339 Testing how botworker behaves on annotation updates
- TOP-339 Ability for prometheus to scrape the custom metrics
- docs: Update helm chart and changelog

### 0.21.16-dev
- patch: handle cancel auto play errors

### 0.21.15-stg
- docs: Update helm chart and changelog

### 0.21.15-dev
- patch: triggering new deployment of worker with different wss server
- docs: udpating helm chart and wss server url
- docs: Update helm chart and changelog

### 0.21.14-dev
- patch: refactor init to use async/await for handling of async operations
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: 1.6.38
- Merged in feature/zoom-remarks-and-fixes (pull request #255)
- a bit extended logging (Zoom debugging)
- feat: zoom remarks and fixes
- Merge remote-tracking branch 'origin/main' into feature/zoom-remarks-and-fixes
- checking allin  again
- testing it again
- Revert "testing it again"
- This reverts commit d5aca6fe7dd29d39d064f4c963173bcfac92bb05.
- Revert "checking allin  again"
- This reverts commit 3889857d80bfc1fdb9147309608b2db1f06d7915.
- remarks fixed
- cleanup
- Approved-by: Aleksei Klimakov
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog
- TOP-339 docs: Ability for prometheus to scrape the custom metrics
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: implement throttling for tournament scanning bot
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: improve tournament detail error logging

### 0.21.13
- docs: Update helm chart and changelog

### 0.21.13-stg
- docs: Update helm chart and changelog

### 0.21.13-dev
- patch: 1.6.38
- Merged in feature/zoom-remarks-and-fixes (pull request #255)
- a bit extended logging (Zoom debugging)
- feat: zoom remarks and fixes
- Merge remote-tracking branch 'origin/main' into feature/zoom-remarks-and-fixes
- checking allin  again
- testing it again
- Revert "testing it again"
- This reverts commit d5aca6fe7dd29d39d064f4c963173bcfac92bb05.
- Revert "checking allin  again"
- This reverts commit 3889857d80bfc1fdb9147309608b2db1f06d7915.
- remarks fixed
- cleanup
- Approved-by: Aleksei Klimakov
- Approved-by: Alexandr Maximov

### 0.21.12
- TOP-339 docs: Ability for prometheus to scrape the custom metrics
- docs: Update helm chart and changelog

### 0.21.12-stg
- docs: Update helm chart and changelog

### 0.21.12-dev
- patch: implement throttling for tournament scanning bot
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.21.11
- docs: Update helm chart and changelog

### 0.21.11-stg
- docs: Update helm chart and changelog

### 0.21.11-dev
- patch: improve tournament detail error logging
- docs: Update helm chart and changelog

### 0.21.9-dev
- patch: add logging for missing seats

### 0.21.8
- docs: Update helm chart and changelog

### 0.21.8-stg
- docs: Update helm chart and changelog

### 0.21.8-dev
- patch: simplify strategy action making
- docs: Update helm chart and changelog

### 0.21.7
- docs: Update helm chart and changelog

### 0.21.7-stg
- docs: Update helm chart and changelog

### 0.21.7-dev
- patch: downgrade protobufjs to 6.x
- docs: Update helm chart and changelog

### 0.21.6-dev
- patch: upgrade node version

### 0.21.5
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: add error handling for tournament detail processing
- Resolves: ARMS-259
- Approved-by: Aleksei Klimakov

### 0.21.5-stg
- chore: cleanup .env
- chore: cleanup
- patch: upgrade node version

### 0.21.5-dev
- patch: add error handling for tournament detail processing
- Resolves: ARMS-259
- Approved-by: Aleksei Klimakov

### 0.21.4
- docs: Update helm chart and changelog

### 0.21.4-stg
- docs: Update helm chart and changelog

### 0.21.4-dev
- patch: fix starting time parsing

### 0.21.3
- docs: Update helm chart and changelog

### 0.21.3-stg
- docs: Update helm chart and changelog

### 0.21.3-dev
- patch: update libs

### 0.20.8-dev
- patch: Merged in feat/pkw-logging-improvements (pull request #248)
- Approved-by: Alexandr Maximov
- Merged in feat/pkw-logging-improvements (pull request #245)
- Feat/pkw logging improvements
- init
- last event triggered added
- Merged main into feat/pkw-logging-improvements
- cleanup
- Merged main into feat/pkw-logging-improvements
- Approved-by: Aleksei Klimakov
- Approved-by: Alexandr Maximov

### 0.20.7
- docs: Update helm chart and changelog

### 0.20.7-stg
- docs: Update helm chart and changelog

### 0.20.7-dev
- patch: Transfer params moved to signed query params

### 0.20.6-stg
- docs: remove obsolete config values
- docs: Update helm chart and changelog

### 0.20.5-dev
- patch: extend pkw connection timeout, add auto reconnection

### 0.20.4
- docs: Update helm chart and changelog

### 0.20.4-stg
- docs: Update helm chart and changelog

### 0.20.4-dev
- patch: improve error handling and connection management
- check mtt token on login
- close world websocket connection after joining the game
- log instead of throwing error on enter game
- docs: Update helm chart and changelog

### 0.20.3-dev
- patch: keep the promise alive
- TOP-318 Switching to another secret for elasticache endpoint
- docs: Update helm chart and changelog

### 0.20.2-dev
- patch: Merged in feat/zoom-play (pull request #235)
- Approved-by: Alexandr Maximov
- patch: split managing and gaming platforms

### 0.20.0
- docs: add bitbucket pipeline
- docs: Update helm chart and changelog

### 0.20.1-dev
- Merged in feat/zoom-play (pull request #229)
- Zoom-play - new updates
- patch: fix zoom room mismatch
- 1260 error join room workaround
- dummy threshold added
- minor logging
- some more logging
- _getAvailableSeatId adjusted
- logging
- Merge branch 'main' into feat/zoom-play
- debug_threshold added
- get seat logging
- remarks fixed
- Merge branch 'main' into feat/zoom-play
- Merged main into feat/zoom-play
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.20.0-stg
- docs: Update helm chart and changelog

### 0.20.0-dev
- minor: transfer bot
- docs: Update helm chart and changelog

### 0.19.2-dev
- Merge branch 'feature/transfer_bot' of https://bitbucket.org/aceguardian/worker into feature/transfer_bot
- TransferJobData and transfer job type
- Transfer bot moved to a separate type
- Some PR fixtures
- Add paramas to logs
- Balance bot extended to transfer bot
- TransferJobData and transfer job type
- Transfer bot moved to a separate type
- Some PR fixtures
- Add paramas to logs
- Balance bot extended to transfer bot

### 0.18.0-dev
- minor: Platform Id introduced
- docs: Update helm chart and changelog

### 0.17.15-dev
- patch: fix zoom room mismatch
- docs: Update helm chart and changelog

### 0.17.14-dev
- patch: fix action handling for check and call scenarios
- docs: Update helm chart and changelog

### 0.17.13-dev
- patch: implement limited game mode
- Limited game mode is activated only for the first hand if some actions are missing.
- Resolves: ARMS-242
- docs: Update helm chart and changelog

### 0.17.11-dev
- patch: add optional chaining for room state ante check

### 0.17.10-dev
- patch: improve job failed logs
- TOP-294 TerminationPeriod for prod set
- docs: Update helm chart and changelog

### 0.17.9
- docs: Update helm chart and changelog

### 0.17.9-stg
- docs: Update helm chart and changelog

### 0.17.9-dev
- patch: handle missing room/seat states
- docs: Update helm chart and changelog

### 0.17.8-stg
- docs: Update helm chart and changelog

### 0.17.8-dev
- patch: improve error handling
- docs: Update helm chart and changelog

### 0.17.7-stg
- docs: Update helm chart and changelog

### 0.17.7-dev
- patch: adjust request timeout handling
- docs: Update helm chart and changelog

### 0.17.6
- docs: Update helm chart and changelog

### 0.17.6-stg
- docs: change wpk prod url
- docs: Update helm chart and changelog

### 0.17.6-dev
- patch: add http request timeout
- docs: Update helm chart and changelog

### 0.17.5-dev
- patch: reset seat number on room change
- docs: Update helm chart and changelog

### 0.17.4-stg
- docs: Update helm chart and changelog

### 0.17.4-dev
- TOP-294 Trying out terminationgraceperiod
- Merged in fix/proper-mtt-logging-for-monitoring (pull request #211)
- Approved-by: Milos Stojanovic
- Approved-by: Alexandr Maximov
- TOP-294 Trying out terminationgraceperiod
- TOP-294 Trying out terminationgraceperiod
- docs: Update helm chart and changelog

### 0.17.3
- docs: Update helm chart and changelog

### 0.17.3-stg
- docs: Update helm chart and changelog

### 0.17.3-dev
- patch: do not add sb/bb allin action
- docs: Update helm chart and changelog

### 0.17.2-dev
- patch: log strategy request duration
- docs: Update helm chart and changelog

### 0.17.1-dev
- patch: fix check game type is zoom
- docs: TOP-287 Updated production image release pipeline with  authorised manual approval
- zoom adapter id changed
- docs: Update helm chart and changelog

### 0.17.0-dev
- minor: prepare for zero interrupt job completion
- docs: Update helm chart and changelog

### 0.15.4-dev
- minor: handle error on game enter

### 0.15.3-dev
- patch: bugfix/missing stake ante fields
- docs: Update helm chart and changelog

### 0.15.2-dev
- patch: set hole cards on room snapshot
- Merged in feature/zomm-adapter-introduced (pull request #198)
- Approved-by: Alexandr Maximov
- Merged in fix/libs-mtt-imports-fix-2 (pull request #199)
- Approved-by: Alexandr Maximov
- Merged in fix/libs-mtt-imports-fix (pull request #197
- Approved-by: Alexandr Maximov
- TOP-276 docs: Setting requests/limits
- docs: add LAUNCHDARKLY_SDK_KEY for prod
- TOP-276 docs: Setting requests/limits
- TOP-276 docs: Setting requests/limits
- docs: Update helm chart and changelog

### 0.15.1-dev
- patch: remove LAUNCHDARKLY_SDK_KEY from charts
- docs: Update helm chart and changelog

### 0.15.0-dev
- minor: add immediate actions support in non-mtt games
- docs: Update helm chart and changelog
- patch: Merged in fix/mtt-envs-minor-fixes (pull request #195)
- Mtt environment minor fixes
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: adjust delays for immediate actions
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: Percentage thresholds replaced with BB multipliers
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: handle undefined fields for mtt strategy

### 0.14.5-dev
- patch: Merged in fix/mtt-envs-minor-fixes (pull request #195)
- Mtt environment minor fixes
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.14.4
- docs: Update helm chart and changelog

### 0.14.4-stg
- docs: Update helm chart and changelog

### 0.14.4-dev
- patch: adjust delays for immediate actions
- docs: Update helm chart and changelog

### 0.14.2
- docs: Update helm chart and changelog

### 0.14.3-dev
- patch: Percentage thresholds replaced with BB multipliers
- docs: Update helm chart and changelog

### 0.14.2-stg
- docs: Update helm chart and changelog

### 0.14.2-dev
- patch: handle undefined fields for mtt strategy

### 0.14.1
- docs: Update helm chart and changelog

### 0.14.1-dev
- patch: adjust delays for certain actions
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- minor: calculate check probability and adjust delay for certain actions

### 0.14.0
- docs: Update helm chart and changelog

### 0.14.0-stg
- docs: Update helm chart and changelog

### 0.14.0-dev
- minor: calculate check probability and adjust delay for certain actions

### 0.13.1-dev
- patch: add mystery bounty support
- docs: Update helm chart and changelog

### 0.13.0-stg
- docs: Update helm chart and changelog

### 0.13.0-dev
- minor: Withdraw
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.12.7
- docs: Update helm chart and changelog

### 0.12.7-dev
- patch: Merged in feature/mtt-weboscket-connection-loss-fix (pull request #180)
- Approved-by: Alexandr Maximov

### 0.12.6
- docs: Update helm chart and changelog

### 0.12.6-stg
- docs: Update helm chart and changelog

### 0.12.6-dev
- patch: add missing import

### 0.12.4-stg
- docs: Update helm chart and changelog

### 0.12.4-dev
- TOP-250 patch: Switching to the different nodepool
- docs: Update helm chart and changelog

### 0.12.3
- docs: Update helm chart and changelog

### 0.12.3-stg
- docs: Update helm chart and changelog

### 0.12.3-dev
- patch: select correct room based on ID
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: add more pkw logging
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: Merged in fix/mtt-config-in-charts-for-fungamer (pull request #183)
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- minor: add bounty full support
- patch: skip pre-action check for bomp pot hand

### 0.12.2
- docs: Update helm chart and changelog

### 0.12.2-stg
- docs: Update helm chart and changelog

### 0.12.2-dev
- patch: add more pkw logging
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.12.1
- docs: Update helm chart and changelog

### 0.12.1-dev
- patch: Merged in fix/mtt-config-in-charts-for-fungamer (pull request #183)
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.12.0
- docs: Update helm chart and changelog

### 0.12.0-stg
- docs: Update helm chart and changelog

### 0.12.0-dev
- minor: add bounty full support
- patch: skip pre-action check for bomp pot hand

### 0.11.5
- docs: Update helm chart and changelog

### 0.11.7-dev
- patch: Merged in feature/mtt-signupandplay-module-converted-to-TS (pull request #177)
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.11.6-dev
- patch: Merged in feature/node-20-docker-image (pull request #178)
- Docker image now uses Node 20
- Approved-by: Milos Stojanovic

### 0.11.5-stg
- docs: Update helm chart and changelog

### 0.11.5-dev
- patch: feat/websocket_loss_of_connection_updates (pull request #175)
- Feat/websocket loss of connection updates
- This PR is made to fix the issue with websocket connection loss and SCAN job inactivity due to it
- 1. Introduced reconnectAgent which is a scheduled job (recurrent timeout function) that is invoked every RECONNECT_AGENT_TIMEOUT ms. 
- 2. It is invoked in connectServer function which forcefully shuts down existing connection and creates a new one 
- 3. reconnectAgent code checks the current state of websocket connection, and if it’s considered faulty - reestablishes it by calling connectServer
- 4. Slightly refactored existing interval _onOpenConnectionTimeout , which checks if the response for open connection request does not exceed timeout. All logic persisted
- 5. added testing task (src/tasks/add_job_scan_81.ts) which adds a new job to bull mq with described data. This file now runs with npm run test:scan:81
- Approved-by: Aleksei Klimakov
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: Total buy in recalculated because of not full buy in

### 0.11.4-stg
- docs: Update helm chart and changelog

### 0.11.4-dev
- patch: Total buy in recalculated because of not full buy in
- docs: Update helm chart and changelog

### 0.11.3-dev
- patch: rebuy after initial buy in fixed
- docs: Update helm chart and changelog

### 0.11.2-stg
- docs: Update helm chart and changelog

### 0.11.2-dev
- patch: version bumped
- initial update
- docs: Update helm chart and changelog

### 0.11.1-dev
- patch: R5 rebuy stats fixed
- docs: Update helm chart and changelog

### 0.11.0-dev
- minor: use feature flags for more strategy calls
- docs: TOP-211 Fix ArgoCD warning for duplicate secret
- docs: TOP-211 Fix ArgoCD warning for dublicate secret.
- docs: TOP-211 Add launchdarkly sdk key to prod. Improve unified name.
- docs: Update helm chart and changelog

### 0.10.6
- docs: Update helm chart and changelog

### 0.10.6-stg
- docs: Update helm chart and changelog

### 0.10.6-dev
- patch: revert strategy fot pkw
- docs: Update helm chart and changelog

### 0.10.5
- docs: Update helm chart and changelog

### 0.10.5-stg
- docs: TOP-211 Add SDK key as ESO Secret to helm chart
- docs: Update helm chart and changelog

### 0.10.5-dev
- patch: set hole cards before the action if missing
- docs: TOP-193 Remove deployment for tag release pipelines to avoid halted status
- docs: Update helm chart and changelog

### 0.10.4-dev
- patch: version bumped
- logging cleanup
- docs: Update helm chart and changelog

### 0.10.3
- docs: Update helm chart and changelog

### 0.10.3-stg
- docs: Update helm chart and changelog

### 0.10.3-dev
- Merge branch 'main' of https://bitbucket.org/aceguardian/worker
- patch: new release
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.10.2-dev
- Merge branch 'main' of https://bitbucket.org/aceguardian/worker
- patch: new release

### 0.10.1-dev
- patch: new release

### 0.10.0-dev
- minor: check if in the same room and reconnect

### 0.7.10-dev
- patch: debugging seat_down issue: logging and formatting added to pkwRoom
- docs: Update helm chart and changelog

### 0.7.9-dev
- patch: predefinedExecutionPercentChance usage is fixed
- docs: Update helm chart and changelog

### 0.7.8
- docs: Update helm chart and changelog

### 0.7.8-stg
- docs: Update helm chart and changelog

### 0.7.8-dev
- patch: r5 missing cards logs
- docs: Update helm chart and changelog

### 0.7.7-dev
- patch: fix error message logging
- docs: Update helm chart and changelog

### 0.7.6-dev
- patch: Proper rebuy and ignoreing useless errors fix
- docs: Update helm chart and changelog

### 0.7.5
- docs: Update helm chart and changelog

### 0.7.5-stg
- docs: Update helm chart and changelog

### 0.7.5-dev
- patch: chance calculation is fixed, version bumped
- docs: Update helm chart and changelog

### 0.7.4-dev
- docs: Update helm chart and changelog
- patch: version bumped

### 0.7.3-dev
- Merged in feature/pre-fold-pkw-march-adjustments-new (pull request #154)
- Feature/pre fold pkw march adjustments new
- version back
- patch: pre-fold execution updated
- docs: Update helm chart and changelog

### 0.7.2-dev
- patch: version bumped
- merge branch 'staging'
- fix: attempt to resolve sit is occupied error
- seat occupied logging 1
- seat occupied logging
- docs: Update helm chart and changelog
- Merge branch 'refs/heads/main' into staging
- Merge branch 'main' into staging
- Merged in feat/proxyUrl-introduced (pull request #128)
- Feature - IP Pool  introduced
- Merge branch 'main' into staging
- feat: add feature flag support
- Merged in main (pull request #133)
- Merged in feature/buy_in_with_big_blind_multiplier (pull request #84)
- Merged in feature/buy_in_with_big_blind_multiplier (pull request #84)
- Buy In boundaries replaced with a big blind miltiplier
- Approved-by: Alexandr Maximov
- Merged in main (pull request #131)
- Main
- Bullmq added to some libs
- Merged in bugfix/skip_attempts (pull request #129)
- UnrecoverableError, synchronized updateTournamentDetails and sign in, more logs on a prizeMoney
- UnrecoverableError, synchronized updateTournamentDetails and sign in, more logs on a prizeMoney
- RMTT
- bugfix on not catching the error
- Last tested fixes for signUp
- UnrecoverableError, synchronized updateTournamentDetails and sign in, more logs on a prizeMoney
- RMTT
- bugfix on not catching the error
- Last tested fixes for signUp
- Merge branch 'bugfix/skip_attempts' of https://bitbucket.org/aceguardian/worker into bugfix/skip_attempts
- fix(mtt): filter out players without chips
- Merge branch 'main' into staging
- delay service base values adjusted + minor gitignore patch
- Merged in main (pull request #126)
- Main
- Fix possible nulls and undefind in TournamentDetails response
- JSON.stringify for data object
- Fix import
- Logs for updateTournamentDetails
- Merged in feature/fetch_tournament_details (pull request #123)
- updateTournamentDetails replaced with a redis cached result
- feat: pre-fetch and cache mtt details
- Merged in main (pull request #120)
- Merged in bugfix/periodic_tournament_details_turn_off (pull request #118)
- Merged in bugfix/periodic_tournament_details_turn_off (pull request #118)
- Comment refresh of the tournament details
- Comment refresh of the tournament details
- Merge branch 'refs/heads/main' into staging
- feat: add lock for check job
- Merged in main (pull request #117)
- Main
- feat(rmtt): switch API for tournament check
- feat(mtt): switch API for tournament check
- feat(mtt): switch API for tournament check
- Merged in bugfix/proper_proto_version (pull request #116)
- rmtt downgraded
- rmtt downgraded
- Return encode in websocket.js
- Merged in feature/r3_r4_separate (pull request #115)
- Feature/r3 r4 separate
- Remove useless methods from strategies
- R3 and R4 splitted
- Old mtt lib added as rmtt
- fix(mtt): message encode
- fix(mtt): message encode
- Merge branch 'main' into staging
- fix(mtt): starting time
- Merge branch 'main' into staging
- fix(mtt): update protobuf definition
- fix(check): finish job after the check
- Merge branch 'refs/heads/main' into staging
- feat(mtt): add check jobs support
- Merged in main (pull request #113)
- Main
- Merged in refactor/remove_extensive_logs (pull request #112)
- Revert "Merged in bugfix/logs_on_websocket_connection (pull request #109)"
- Revert "Merged in bugfix/logs_on_websocket_connection (pull request #109)"
- This reverts commit d06c44f683a4eb491db1222b5a6e9001d5e70319.
- Additional header for wpk requests
- Merged in feat/metrics-client-refactoring (pull request #110)
- main refactoring finished - now possible to stop sending metrics
- container_id env fixed
- Merged in bugfix/logs_on_websocket_connection (pull request #109)
- Bugfix/logs on websocket connection
- More temporary logs on websocket connection
- And more logs
- Redeploy
- redeploy
- Merged in feature-staging/ticket_id_for_mtt (pull request #108)
- Ticket ID for sign up for tournament
- Ticket ID for sign up for tournament
- Merged in feature/ticket_id_for_mtt (pull request #107)
- Ticket ID for sign up for tournament
- Ticket ID for sign up for tournament
- Merged in staging (pull request #106)
- Staging
- Merged in bugfix/tournament_detail_request (pull request #105)
- MOre logging for Detail request
- MOre logging for Detail request
- Merged in bugfix/tournament_detail_request (pull request #102)
- Tournament detail moved
- Tournament detail moved
- Merged in bugfix/tournament_detail_request (pull request #100)
- Logs on every step og the mtt run and onErrorFunc fix
- Logs on every step og the mtt run and onErrorFunc fix
- Merged in bugfix/tournament_detail_request (pull request #98)
- Bugfix/tournament detail request
- updateTournamentDetails fix, skip error if status update
- Merged in refactor/shared-interfaces (pull request #88)
- Refactor/shared interfaces
- Approved-by: Alexandr Maximov
- Redeploy
- settings naming adjusted
- Redeploy
- Redeploy
- post-merge cleanup
- Merge branch 'main' into refactor/shared-interfaces
- Merged in main (pull request #95)
- Main
- Merged in feature/r4_balance_fix (pull request #96)
- strategy.ts fixes
- strategy.ts fixes
- Merged in feature/r4_balance_fix (pull request #94)
- balance function moved to strategy
- balance function moved to strategy
- fetch Raw str logging + cleanup
- Merge branch 'main' into refactor/shared-interfaces
- remove Gamepleyr from imports
- worker settings pkw now injected into libs + cleanup
- Merge branch 'main' of https://bitbucket.org/aceguardian/worker
- Merged in feature/r4_mtt_from_staging (pull request #93)
- Feature/r4 mtt from staging
- Redeploy
- MOre logs on parsed configs
- Remove randombytes
- Try catch JSON errors
- config types fixes
- Merge branch 'staging' of https://bitbucket.org/aceguardian/worker into staging
- Logging for R4 configs
- accidential removal of predefined vars reverted
- Merge branch 'main' into staging
- Merged in feat/pre-fold-calculation-simplfied (pull request #90)
- PRE-FOLD action calculation simplified
- Merged in feature/r4_mtt (pull request #91)
- Feature/r4 mtt
- New Mtt adapter login strategy
- feat(pkw): change strategy
- WIP: countyry code and phoen number for login
- Login, second login and scanning is working
- Strategies refactor and balance bot
- Remove debug logs
- MttStrategy renamed to AppStrategy
- Some refactoring
- Merge branch 'main' of https://bitbucket.org/aceguardian/worker
- New Mtt adapter login strategy
- WIP: countyry code and phoen number for login
- Login, second login and scanning is working
- Strategies refactor and balance bot
- Remove debug logs
- MttStrategy renamed to AppStrategy
- Some refactoring
- Merge branch 'feature/r4_mtt' of https://bitbucket.org/aceguardian/worker into feature/r4_mtt
- R4 mtt support
- Merge branch 'main' of https://bitbucket.org/aceguardian/worker
- Merged in feat/pre-fold-calculation-simplfied (pull request #90)
- PRE-FOLD action calculation simplified
- Merge branch 'main' of https://bitbucket.org/aceguardian/worker
- minimum probability injection is still not finished...
- shared interfaces added
- typo fixed
- Merge branch 'main' into refactor/shared-interfaces
- build error
- feat(pkw): change strategy
- init
- feat(pkw): change strategy
- Logging fixes+typo
- feat(mtt): handle players that were forced allin
- Merged in feat/pre-fold-check-actions (pull request #87)
- Feat/pre fold check actions
- merge
- env var typo fixed
- Merged in feat/pre-fold-check-actions (pull request #86)
- version bumped
- env var added
- logging improvements
- 1.6.16
- version bumped
- logging improvements
- probability minimum to env
- version bumped
- Merged in feat/pre-fold-check-actions (pull request #85)
- Feat/pre-fold,check actions
- Precalculating FOLD or CHECK action before our turn has come. If probability is high enough - we set this predefined action and call it immediately when our turn comes
- Merged main into feat/pre-fold-check-actions
- feat: switch to internal worker threads usage
- fix: make force exit return a 0 code
- fix(mtt): do not retry on signup limit reached
- fix(mtt): handle too frequent operate error
- feat: apply start job lock to mtt only
- (cherry picked from commit 33e67dde46c7d5070409532bf1c8c3e4366522b3)
- logging cleaned up
- final remarks updated
- players sorting adjusted
- function _fetchStrategyData added
- straddle seat check updated
- Merged in feat/logging-with-category (pull request #83)
- Logging with tags with ability to disable some tags
- Approved-by: Alexandr Maximov
- feat: add start job lock
- fix(mtt): fixing seats for two players
- debug - fetching metadata

### 0.7.1-dev
- patch: handle mtt check error
- docs: Update helm chart and changelog

### 0.7.0-dev
- minor: add bomb pot support
- docs: Update helm chart and changelog

### 0.6.0-dev
- minor: add normal bounty support
- docs: Update helm chart and changelog

### 0.5.1
- docs: Update helm chart and changelog

### 0.5.1-stg
- docs: Update helm chart and changelog

### 0.5.1-dev
- patch: Verification fix and proper room connect for R5

### 0.4.0-dev
- Merged in feat/mtt-reconnect (pull request #142)
- minor: reconnect on websocket close
- feat(mtt): reconnect on websocket close
- Fixing STRATEGY_SERVICE_URL on dev
- Fixing STRATEGY_SERVICE_URL on prod
- docs: Update helm chart and changelog

### 0.3.0-dev
- minor: R5 support for the worker
- docs: Update helm chart and changelog

### 0.2.3-dev
- patch: https-proxy-agent missing error fixed

### 0.2.1-dev
- patch: bullmq added to the pkw lib

### 0.1.16
- values.yaml edited online with Bitbucket
- docs: Update helm chart and changelog

### 0.1.15
- values.yaml edited online with Bitbucket
- configmap.yaml edited online with Bitbucket
- docs: Update helm chart and changelog

### 0.1.14
- docs: Update helm chart and changelog

### 0.1.14-stg
- docs: Update helm chart and changelog

### 0.1.14-dev
- Merge branch 'main' of https://bitbucket.org/aceguardian/worker
- patch: new release

### 0.1.13-dev
- patch: dev release

### 0.1.12-dev
- patch: version bumped
- docs: Update helm chart and changelog

### 0.1.11
- docs: Update helm chart and changelog

### 0.1.11-stg
- TOP-189 Renaming scripts
- docs: Update helm chart and changelog

### 0.1.11-dev
- patch: version bumped
- enable predefined action = 1
- docs: Update helm chart and changelog

### 0.1.10-dev
- patch: version bumped

### 0.1.7-dev
- patch: version bumped

### 0.1.4-dev
- patch:a bit more logging
- docs: Update helm chart and changelog

### 0.1.3-dev
- patch:version bumped
- libs/pkw - a bit more logs
- docs: Update helm chart and changelog

### 0.1.2-dev
- patch:version bumped
- docs: Update helm chart and changelog

### 0.1.1-dev
- TOP-178 patch: configmap.yaml edited online with Bitbucket
- TOP-178 path: configmap.yaml edited online with Bitbucket
- docs: Update helm chart and changelog

### 0.1.0-dev
- TOP-178 docs: Pipeline init on botworker
