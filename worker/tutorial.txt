# Platforms
WPK = 98
WPTGO = 101

how to sit cloud bot on the table.
1. switch vpn fungamer.
2. go to https://backoffice.dev.fungamer.io/
3. swith to R1
4. choose table
5. click + to run the bot on the table.


dev test client
http://18.167.209.149:82/test/index.html
http://18.167.209.149:82/test/


manager frontend:
name backoffice
repo global-botmanager-frontend
only via vpn.fungmer.io


VPN app OpenVPN
# connection:
backoffice
botmanager
grafana

only via VPN fungamer

testclient
worker - когда локально

only via VPN A5


name jjkj00sssssww
id 31068574
pwd a@111111

## PKW no mtt user
name Jdjfjfjdj
id 34642439
pwd a@111111


# WPK users mtt
id 34579567
pkw id 45763
name a5team005
pwd a5team@005

id 34579568
pkw id 45660
name a5team006
pwd a5team@006

id 34579569
pkw id 45679
name a5team007
pwd a5team@007


# backoffice

send message to redis


# currencies
USD
GOLD == yuan == 1/8 USD
DIAMOND


/libs
	/mtt
	/pkw

/src

WPK - платформа



MTT - игра в турниры на WPK
PKW - кэш ига на WPK
FRIENDS - домашние столы

----
свой API
свои вебосокеты
своя бизнес логика

```
function start(userId, passw, game_mode, buyin, ...settings, onMessage) {
	return new Promise((resolve) => {
		ws = new Websocket(url....)
		ws.onmessage = () => {
			// business logic
			ws.sendMessage('abc')

			onMessage({ status: 'playing', hands_played: 20 })

			if (message == 'finish_game') {
				ws.close()
				resolve()
			}
		}
	})
}
```

1 nodejs instance
subscribe bullmq

up to 64 threads with game logic