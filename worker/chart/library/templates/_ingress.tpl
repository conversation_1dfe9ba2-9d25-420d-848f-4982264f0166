{{- define "library-ingress" }}
{{- $root := .root }}
{{- $context := .context }}
apiVersion: networking.k8s.io/v1
kind: Ingress
{{- include "library-metadata" (dict "root" $root "context" $context) }}
spec:
  ingressClassName: {{ required "Missing ingressClassName" $context.ingressClassName }}
  rules:
  {{- if not $context.rules }}
  {{ fail "Ingress rules are required but missing." }}
  {{- end }}
  {{- range $rule := $context.rules }}
  - host: {{ $rule.host | default "" }}
    http:
      paths:
      {{- if not $rule.paths }}
      {{ fail "Paths are required for each host rule but missing." }}
      {{- end }}
      {{- range $path := $rule.paths }}
      - path: {{ $path.path | default "/" }}
        pathType: {{ $path.pathType | default "Prefix" }}
        backend:
          service:
            name: {{ default $root.Chart.Name $context.name }}
            port:
              number: {{ required "Port number is required" $path.number }}
      {{- end }}
  {{- end }}
{{- end }}
