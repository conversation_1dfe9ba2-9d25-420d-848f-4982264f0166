{{- define "library-secrets" }}
{{- $root := .root }}
{{- $context := .context }}
{{- if eq $context.type "local" }}
apiVersion: v1
kind: Secret
{{- include "library-metadata" (dict "root" $root "context" $context) }}
type: Opaque
stringData:
  {{- range $key, $val := $context.secrets }}
  {{ $key }}: {{ $val | quote }}
  {{- end }}
{{- else if (eq $context.type "ESO") }}
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
{{- include "library-metadata" (dict "root" $root "context" $context) }}
spec:
  refreshInterval: {{ default "60s" $context.refreshInterval | quote }}
  secretStoreRef:
    name: {{ default "clustersecretstore" $context.clusterSecretStore | quote }}
    kind: ClusterSecretStore
  target:
    name: {{ $context.name | quote }}
    creationPolicy: Owner
    {{- if hasKey $context "template" }}
    template:
      engineVersion: {{ default "v2" $context.template.engineVersion | quote }}
      metadata:
        labels:
          {{- range $key, $val := $context.template.metadata.labels }}
          {{ $key }}: {{ $val | quote }}
          {{- end }}
      data:
        {{- range $key, $val := $context.template.data }}
        {{ $key }}: {{ $val | quote }}
        {{- end }}
        {{- if hasKey $context.secrets "sshPrivateKey" }}
        sshPrivateKey: "{{"{{"}} .sshPrivateKey {{"}}"}}"
        {{- end }}
    {{- end }}
  data:
  {{- range $key, $val := $context.secrets }}
    - secretKey: {{ $key | quote }}
      remoteRef:
        key: {{ if (typeIs "string" $val) }}{{ $val | quote }}{{ else }}{{ $val.key | quote }}{{ end }}
        {{- if (and (not (typeIs "string" $val)) (hasKey $val "property")) }}
        property: {{ $val.property | quote }}
        {{- end }}
  {{- end }}
{{- else }}
{{- end }}
---
{{- end }}
