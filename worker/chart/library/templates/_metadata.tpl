{{- define "library-metadata" }}
{{- $root := .root }}
{{- $context := .context }}
metadata:
  name: {{ required "Missing name" (default (include "name" $root) $context.name) }}
  namespace: {{ required "Missing namespace" (default $root.Values.namespace $context.namespace) }}
  labels:
    environment: {{ required "Missing environment" $root.Values.environment }}
    app: {{ include "name" $root }}
    {{- with $context.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with $context.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
