environment: &environment prod

service:
  - type: ClusterIP
    port: 3000
    targetPort: 3000
    ports:
      - port: 3000
        targetPort: 3000

configmap:
  STRATEGY_SERVICE_URL: http://gtoglue.aceguardian.io
  WPK_URL: "https://game.wpk000.com:11111/wepoker"
  RWPK_URL: URL_NOT_SET
  WPTGO_URL: URL_NOT_SET
  WPTGO_WS_URL: URL_NOT_SET

deployment:
  image:
    repository: 340752809081.dkr.ecr.ap-southeast-1.amazonaws.com/acg/botworker
    tag: 0.31.44
  terminationGracePeriodSeconds: "28800"
  revisionHistoryLimitCount: 5
  nodePoolTolerationValue: resource-intensive
  resources:
    limits:
      cpu: 16
      memory: "32768Mi"
    requests:
      cpu: 13
      memory: "26696Mi"
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "3000"
    prometheus.io/path: "/metrics"

secrets:
  - type: ESO
    name: documentdb-endpoint
    secrets:
      MONGO_HOST: data_storage/documentdb.bots.endpoint
  - type: ESO
    name: redis-endpoint
    secrets:
      REDIS_HOST: data_storage/elasticache.manager.endpoint
  - type: ESO
    name: mongo-user
    secrets:
      MONGO_USER:
        key: data_storage/documentdb.bots.credentials
        property: username
  - type: ESO
    name: mongo-password
    secrets:
      MONGO_PASSWORD:
        key: data_storage/documentdb.bots.credentials
        property: password
  - type: ESO
    name: strategy-service-token
    secrets:
      STRATEGY_SERVICE_TOKEN: bots/svc.botworker.strategy_service_token
  - type: ESO
    name: secret-key
    secrets:
      SECRET_KEY: bots/svc.botworker.secret_key
  - type: ESO
    name: unleash-api-token
    secrets:
      UNLEASH_API_TOKEN: management/unleash.edge.api_token

ingress:
  - ingressClassName: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
    rules:
      - host: botworker.aceguardian.io
        paths:
          - path: /dashboard
            pathType: Prefix
            number: 3000
          - path: /arena
            pathType: Prefix
            number: 3000

hpa:
  - name: botworker
    targetDeployment:
      apiVersion: apps/v1
      kind: Deployment
      name: botworker
    minReplicas: 5
    maxReplicas: 50
    metrics:
      - type: Pods
        name: bullmq_node_utilization_active
        averageValue: "0.7"
    behavior:
      scaleUp:
        stabilizationWindowSeconds: 60
        selectPolicy: Max
        policies:
          - type: Pods
            value: 3
            periodSeconds: 15
      scaleDown:
        stabilizationWindowSeconds: 600
        selectPolicy: Min
        policies:
          - type: Pods
            value: 1
            periodSeconds: 60
