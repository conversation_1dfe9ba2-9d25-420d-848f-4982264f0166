{{- $deployment := .Values.deployment | default dict }}
{{- $annotations := $deployment.annotations | default dict }}
{{- $labels := $deployment.labels | default dict }}
{{- $context := dict "name" $deployment.name "annotations" $annotations "labels" $labels }}
apiVersion: apps/v1
kind: Deployment
{{- include "library-metadata" (dict "root" $ "context" $context ) }}
spec:
{{- if not .Values.autoscaling.enabled }}
  replicas: {{ required "Missing deployment.replicaCount" .Values.deployment.replicaCount }}
{{- end }}
  revisionHistoryLimit: {{ default 10 .Values.deployment.revisionHistoryLimitCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 0 
  selector:
    matchLabels:
      app: {{ include "name" . }}
  template:
    {{ include "library-metadata" (dict "root" $ "context" $context ) | nindent 4}}
    spec:
      {{- if .Values.deployment.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.deployment.terminationGracePeriodSeconds }}
      {{- end }}
      {{- if .Values.serviceAccount.enabled }}
      serviceAccountName: {{ include "name" . }}
      {{- end }}
      tolerations:
        - key: "karpenter.sh/nodepool"
          operator: "Equal"
          value: {{ default "default" .Values.deployment.nodePoolTolerationValue | quote }}
          effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: karpenter.sh/nodepool
                    operator: In
                    values:
                      - {{ default "default" .Values.deployment.nodePoolTolerationValue }}
      containers:
        - name: {{ include "name" . }}
          image: "{{ .Values.deployment.image.repository }}:{{ .Values.deployment.image.tag }}"
          imagePullPolicy: {{ .Values.deployment.image.pullPolicy }}
          ports:
            {{- range .Values.service }}
            - containerPort: {{ .port }}
              protocol: TCP
            {{- end }}
          envFrom:
            - secretRef:
                name: redis-endpoint
            - secretRef:
                name: documentdb-endpoint
            - secretRef:
                name: strategy-service-token
            - secretRef:
                name: secret-key
            - secretRef:
                name: unleash-api-token
            - configMapRef:
                name: {{ include "name" . }}
          env:
            - name: MONGO_USER
              valueFrom:
                secretKeyRef:
                  name: mongo-user
                  key: MONGO_USER
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: mongo-password
                  key: MONGO_PASSWORD
          resources:
            {{- toYaml .Values.deployment.resources | nindent 12 }}
{{- if .Values.fluentbit.enabled }}
        - name: fluentbit
          image: fluent/fluent-bit:4.0.3-amd64
          resources:
            limits:
              memory: 256Mi
              cpu: 100m
            requests:
              memory: 128Mi
              cpu: 50m
          volumeMounts:
            - name: varlog
              mountPath: /var/log/containers
            - name: fluentbit
              mountPath: /fluent-bit/etc/
            - name: log-pods
              mountPath: /var/log/pods
          env:
            - name: AWS_REGION
              value: eu-west-1
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: a5labs-aws-access-key
                  key: AWS_ACCESS_KEY_ID
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: a5labs-aws-secret-access-key
                  key: AWS_SECRET_ACCESS_KEY
          args:
            - -c
            - /fluent-bit/etc/fluent-bit.conf
            - -R
            - /fluent-bit/etc/parsers.conf
        - name: debug
          image: busybox
          command: ["sleep", "3600"]
          volumeMounts:
            - name: varlog
              mountPath: /var/log/containers
            - name: log-pods
              mountPath: /var/log/pods
      volumes:
        - name: varlog
          hostPath:
            path: /var/log/containers
        - name: fluentbit
          configMap:
            name: fluentbit
        - name: log-pods
          hostPath:
            path: /var/log/pods
{{- end }}
