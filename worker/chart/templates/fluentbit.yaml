{{- if .Values.fluentbit.enabled }}
{{- $configmap := .Values.fluentbit.configmap | default dict }}
{{- $annotations := $configmap.annotations | default dict }}
{{- $labels := $configmap.labels | default dict }}
{{- $context := dict "name" $configmap.name "annotations" $annotations "labels" $labels }}
apiVersion: v1
kind: ConfigMap
{{- include "library-metadata" (dict "root" $ "context" $context ) }}
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush        5
        Daemon       Off
        Log_Level    debug
        Parsers_File /fluent-bit/etc/parsers.conf

    [INPUT]
        Name              tail
        Path              /var/log/containers/*botworker*botworker*.log
        Parser            json
        Tag               kube.*
        Read_from_head    true
        Path_Key          filename
        Refresh_Interval  5

    [OUTPUT]
        Name              s3
        Match             *
        bucket            wptg-prod-databricks-analysis-data
        region            eu-west-1
        total_file_size   50M
        upload_timeout    10m
        store_dir         /tmp/fluent-bit/s3
        s3_key_format     /logs/$TAG/%Y/%m/%d/%H/%M/%S-$UUID.gz
        compression       gzip
        json_date_key     time

  parsers.conf: |
    [PARSER]
        Name        cri
        Format      regex
        Regex       ^(?<time>[^ ]+) (?<stream>stdout|stderr) (?<logtag>[^ ]+) (?<log>.*)$
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S.%L%z

    [PARSER]
        Name   json
        Format json
        Time_Key time
        Time_Format %d/%b/%Y:%H:%M:%S %z

    [PARSER]
        Name        docker
        Format      json
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S.%L
        Time_Keep   On
{{- end }}
