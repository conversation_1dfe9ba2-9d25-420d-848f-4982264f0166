PORT=3000
WORKER_CONCURRENCY=64

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_CLUSTER_MODE=false

MONGO_HOST=localhost
MONGO_USER=test
MONGO_PASSWORD=test

WPK_URL=http://**************/wepoker

RWPK_URL=http://*************/wepoker

WPTGO_URL=https://api.stg.wptg.a5-labs-cloud.com
WPTGO_WS_URL=wss://wptg-gate-stg.a5labsapp.co
WPTGO_APP_BUNDLE_ID=com.wptasia.wpt

# If you don't want to output some logging tags - enumerate them separated by ,
# NO_LOG_TAGS="NO_LOG_TAGS=JOB_PROGRESS, STRATEGY_SERVICE_CALL"

STRATEGY_SERVICE_URL=http://gtoglue.dev.aceguardian.io # DEV
# STRATEGY_SERVICE_URL=http://gtoglue.stg.aceguardian.io # STAGING

MTT_CONFIG_MTTWORLD=ws://*************:3001
MTT_CONFIG_MTTGAME=ws://*************:4001
MTT_CONFIG_MTTAPI=http://*************:22001
MTT_PROTOBUF_VERSION=v2

RMTT_CONFIG_MTTWORLD=ws://************:3001
RMTT_CONFIG_MTTGAME=ws://************:4001
RMTT_CONFIG_MTTAPI=http://************:29002

# UNLEASH_API_URL=https://unleash.dev.fungamer.io/api
# UNLEASH_API_TOKEN=default:development.6d95572fa147c0ae168c4d02e9f15fd96477150f5e5af3c15e362ee0
