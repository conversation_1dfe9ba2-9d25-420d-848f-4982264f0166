{"version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "tsx --test", "start": "npm run build && node --enable-source-maps ./dist/local.js", "build": "tsc && npm run cp_other_files", "cp_other_files": "cpx 'src/**/*.{js,d.ts,proto,json,csv}' dist"}, "devDependencies": {"@types/node": "^22.15.3", "cpx2": "^8.0.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "dependencies": {"crypto-js": "^4.2.0", "protobufjs": "^7.5.2", "ws": "^8.18.1"}}