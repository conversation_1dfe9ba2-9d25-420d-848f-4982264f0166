import protobuf from "protobufjs";

const protoFiles = [
    "/protobuf/MsgDeliverReqProto.proto",
    "/protobuf/MsgDeliverRespProto.proto"
];
const protobufCache = {};

export const ProtoUtil = {
    async loadProtobuf() {
        for (const filePath of protoFiles) {
            const root = await protobuf.load(__dirname + filePath);
            Object.entries((root.nested as any)).forEach(
                ([k, v]) => protobufCache[k] = v
            );
        }
    },

    encode(type: string, data: any): Uint8Array | null {
        let pType = protobufCache[type]
        if (!pType) {
            console.error(`Protobuf definitions for type ${type} not found`);
            return null;
        }
        return pType.encode(pType.create(data)).finish();
    },

    decode(type: string, buffer: Uint8Array): any | null {
        let pType = protobufCache[type]
        const DECODE_CONFIG = {
            enums: String,
            longs: String,
            defaults: true,
            arrays: true,
            objects: true,
            oneofs: true
        };
        if (!pType) {
            return null;
        }
        let obj = pType.toObject(pType.decode(buffer), DECODE_CONFIG);
        return obj ? valObjConvert(obj) : obj;
    }
}

/**
 * converts { x: { value: 1 } } to { x: 1 }
 */
function valObjConvert(obj: any) {
    if (obj instanceof Object || Array.isArray(obj)) {
        Object.entries(obj).forEach(([k, v]) => {
            let newValue = v;
            if (v && v instanceof Object && Object.keys(v).length == 1 && "value" in v) {
                newValue = v.value;
            }
            obj[k] = valObjConvert(newValue);
        })
    }
    return obj
}
