import {
    BattleActionMsg,
    BattleAction<PERSON>,
    BattleBankChangeMsg,
    BattleCSDResultMsg,
    BattleDealCardPOList,
    BattleRoomDetailResp,
    BattleRoomUserMsg,
    BattleRoundChangeMsg,
    BattleThanResp,
    BattleUserOptMsg,
    BattleUserProfileResp,
    DeskSportNoticeMsg,
    IBattleActionPO,
    IBattleRoomMsg,
    IBattleUserOptMsg,
    RoomPlayRecordOneHand,
    UpdateUserDiamondBalance,
    WaitHandResp
} from './protobuf/MsgDeliverRespProto';

import * as GameWebsocket from './custom_websocket';
import { GameAction, GameMode, GameType, JobDataHandler, PlayerStatus, PlayerStats, StrategyResponseAction, UserStatus, GameState } from 'shared';
import { MESSAGE_TYPES, POKER_ACTIONS } from './types';
import { createLogger } from './logging';

const console = createLogger("FriendsGame");

let updateJobDataHandler: JobDataH<PERSON><PERSON> | undefined;

const playerStats: PlayerStats = {
    handsPlayed: 0,
    totalBuyIn: 0,
    lastBuyIn: 0,
    rebuyCount: 0,
    stack: 0,
};
export function setJobDataHandler(handler: JobDataHandler) {
    updateJobDataHandler = handler;
}

export function setCurrentUserId(userId: number) {
    currentUserId = userId;
}
export function processGameMessage(resp) {
    if (resp.msgType === null && resp.errorCode) {
        console.error(`msgReceive error`, resp.errorCode, resp);
        return;
    }
    if (resp.errorCode) {
        console.error(`msgReceive error`, resp.errorCode, resp);
    }
    try {
        let callback = MESSAGE_HANDLERS[resp.msgType]
        if (callback) {
            callback.call(this, resp.msgBody);
        } else {
            console.log(`WS <- ${resp.msgType}`);
            if (!resp.callbackId) { // maybe already handled by a `sendWebsocketMessage` callback
                console.warn(`WS <- ${resp.msgType} no handler found`, resp);
            }
        }
    } catch (e) {
        console.error('msgReceive', resp, e);
    }
}

let gameState: GameState = null;

let currentUserId: number = -1;
let currentUserStatus: UserStatus | PlayerStatus = UserStatus.default;

export function setUserStatus(status: UserStatus | PlayerStatus) {
    currentUserStatus = status;
    updateJobDataHandler?.({ status, stats: playerStats });
}

let IS_DOING_ACTION = false;

const GAME_TO_STRATEGY_ACTION_MAP = {
    [POKER_ACTIONS.FOLD]: StrategyResponseAction.FOLD,
    [POKER_ACTIONS.CALL]: StrategyResponseAction.CALL,
    [POKER_ACTIONS.CHECK]: StrategyResponseAction.CHECK,
    [POKER_ACTIONS.RAISE]: StrategyResponseAction.RAISE,
    [POKER_ACTIONS.ALL_IN]: StrategyResponseAction.ALLIN,
    "BET": StrategyResponseAction.BET,
}

/**
 * typos like `userAciton` are on the server side :(
 */

type MessageTypeName = keyof typeof MESSAGE_TYPES;

const MESSAGE_HANDLERS: Partial<Record<MessageTypeName, Function>> = {
    'C_updateRoomNotify': updateRoomNotify,
    'C_updateRoomUserNotify': updateRoomUserNotify,
    'WP_bankerChangeNotify': updateSeats,
    'WP_userOptNotify': onActionRequested,
    'WP_dealNotify': onDealNotify,
    'C_closeRoomNotify': onCloseRoomNotify,

    'WP_roundChangeNotify': (body: BattleRoundChangeMsg | BattleUserOptMsg) => {
        if ('round' in body) { // we received BattleRoundChangeMsg
            const round = body.round as string;
            if (['FLOP', 'TURN', 'RIVER'].includes(round)) {
                const action = cardsToString(body.dealPublicCards as number[]);
                gameState.addCardsAction(action);

                console.log(`Round changed to ${round}, public cards dealt: ${action}`);
            }
        }

        const actionRequest = (body as BattleRoundChangeMsg).userAciton || body;
        onActionRequested(actionRequest);
    },

    'WP_actionNotify': (body: IBattleActionPO | BattleActionMsg) => {
        if ('actionList' in body) {
            body.actionList.forEach(onUserAction);
        } else {
            onUserAction(body);
        }
    },
    'WP_updateDiamondBalanceNotify': (body: UpdateUserDiamondBalance) => {
        console.log('Diamond balance updated', { userId: body.userId, balance: body.diamondBalance });
    },
    'C_joinRoomNotify': (body: IBattleRoomMsg) => {
        console.log('C_joinRoomNotify', body);
    },
    'WP_deskSportNotify': (body: DeskSportNoticeMsg) => {
        console.log('WP_deskSportNotify', body);
    },
    'WP_beghintMsgUpdate': (body: any) => {
        console.log('WP_beghintMsgUpdate', body);
    },
    'WP_updateUserProfileNotify': (body: BattleUserProfileResp) => {
        console.log('WP_updateUserProfileNotify', body.listData);
    },
    'WP_lookUserJoinExitNotify': (body: BattleCSDResultMsg) => {
        console.log('WP_lookUserJoinExitNotify', body);
    },
    'WP_waitHandsNotify': (body: WaitHandResp) => {
        console.log('WP_waitHandsNotify', body);
    },
    'C_playLogNotify': (body: RoomPlayRecordOneHand) => {
        console.log('C_playLogNotify', false && body);
    },
    'WP_guessHandBetNotify': (body: any) => {
        console.log('WP_guessHandBetNotify', body);
    },
    'WP_playResultNotify': (body: BattleThanResp) => {
        // End of hand
        console.log('WP_playResultNotify', false && body);
    },
    'C_updateRoomDetail': (body: BattleRoomDetailResp) => {
        console.log('C_updateRoomDetail', body);
        if (body.lookList && body.lookList.length > 0) {
            body.lookList.forEach(async u => {
                console.log(`User ${u.userId} ${u.userId == currentUserId ? '(hero)' : ''} is looking at the room`);
                if (u.userId == currentUserId && u.currentScore as number == 0) {
                    console.warn(`User ${u.userId} has no chips, rebuying...`);

                    const BUY_IN = 200;

                    const room = await GameWebsocket.buyIn(BUY_IN);
                    playerStats.rebuyCount++;
                    playerStats.lastBuyIn = BUY_IN;
                    setUserStatus(UserStatus.boughtIn);
                    console.log(`Rebuy successful`, room);
                }
            });
        }

        const playerData = body.buyScoreList.find(u => u.userId == currentUserId);
        if (playerData) {
            playerStats.stack = playerData.currentScore as number;
            playerStats.handsPlayed = playerData.playBoutNum as number;
            playerStats.totalBuyIn = playerData.buyScore as number;

            updateJobDataHandler?.({ status: currentUserStatus, stats: playerStats });
        }
    },
    'C_cleanNotify': (body: null) => {
        console.log('C_cleanNotify', body);
    },
    'C_updateUserInfoNotify': (body: BattleRoomUserMsg) => {
        if (body.userId == currentUserId) {
            console.warn('C_updateUserInfoNotify Waiting for action from user', body);
            if (+body.currentScore == 0) {
                console.warn('User has no chips, should call rebuy');
            }
        }
        console.log('C_updateUserInfoNotify', body);
    },
}

function updateGameState(body: IBattleRoomMsg) {
    console.log('Updating game state')
    gameState = new GameState(
        body.roomId.toString() + '_' + body.currentBoutNum + '_' + currentUserId + '_' + new Date().getTime(),
        body.roomId.toString(),
        +body.gradeCfg.bigBlind,
        +body.gradeCfg.ante,
        GameMode.NORMAL,
        GameType.NLHE,
    );
    for (const player of body.sitUserList) {
        if (+player.gameStatus < 100 || +player.gameStatus == 103) {
            gameState.addPlayer(player.userId.toString(), +player.seatNum - 1, +player.currentScore);
        } else if (+player.gameStatus == 101) {
            // maybe buyin?
        } else {
            console.warn('player might not be in game', player);
        }
    }

    gameState.players.sort((a, b) => a.seat_no - b.seat_no);
}

function updateSeats(body: BattleBankChangeMsg | IBattleRoomMsg) { // types are compatible for this function
    gameState.setGameParams({
        dealer_seat: +body.bankerSeatNum - 1,
        sb_seat: +body.sBSeatNum - 1,
        bb_seat: +body.bBSeatNum - 1,
        straddle_seat: 'tBSeatNum' in body ? +body.tBSeatNum - 1 : -1,
    })
    console.log('seats updated', false && gameState);
}

function onUserAction(action: IBattleActionPO) {
    console.log(
        `ActionNotify: user ${action.userId}${action.userId == currentUserId ? ' (hero)' : ''}` +
        `(seat ${action.seatNum}) did ${action.actionType} ${action.actionScore || ''} with stack ${action.currentScore}`,
        { action }
    );

    const actionType = action.actionType.toString();
    const seat_no = +action.seatNum - 1;

    if (actionType in GAME_TO_STRATEGY_ACTION_MAP) {
        const actionData: GameAction = {
            action: GAME_TO_STRATEGY_ACTION_MAP[actionType],
        };
        if (['RAISE', 'ALL_IN', 'BET'].includes(actionType)) {
            actionData.amount = +action.actionScore;
        }
        gameState.addAction(actionData.action, actionData.amount, seat_no);
    } else {
        gameState.setGameParams({
            sb_seat: (actionType === 'SMALL_BLIND') ? seat_no : undefined,
            bb_seat: (actionType === 'BIG_BLIND') ? seat_no : undefined,
            straddle_seat: (actionType === 'THIRD_BLIND') ? seat_no : undefined,
        })
    }
}

async function updateRoomNotify(body: IBattleRoomMsg) {
    let roomUser: any = body.sitUserList.find(u => u.userId == currentUserId);

    if (roomUser && roomUser.gameStatus == 103) {
        setUserStatus(UserStatus.satOut);
        await GameWebsocket.returnToSeat();
        setUserStatus(UserStatus.satDown);
    }

    if (roomUser && roomUser.gameStatus == 101) {
        const response = await GameWebsocket.buyIn(+body.minBuyInScore);
        playerStats.lastBuyIn = +body.minBuyInScore;
        if (playerStats.handsPlayed > 0) {
            playerStats.rebuyCount++;
        }
        setUserStatus(UserStatus.boughtIn);
    }

    updateGameState(body);
    updateSeats(body);
}

function updateRoomUserNotify(body) {
    const canStartGame = body.isAdmin == 1 && body.roomStatus == 3 && body.sitUserList.length > 1;
    if (canStartGame) {
        GameWebsocket.sendStartGame();
    }
}

function onDealNotify(body: BattleDealCardPOList) {
    // filter out players that are not in the game
    let userIds = body.list.map(user => user.userId.toString());
    gameState.players = gameState.players.filter(player => userIds.includes(player.uid));

    const currentPlayer = gameState.getPlayerById(currentUserId.toString());
    let playerData = body.list.find(c => c.userId === currentUserId);
    if (currentPlayer && playerData) {
        currentPlayer.hole_cards = cardsToString(playerData.handCards as number[]);
        console.log(`onDealNotify: Player was dealt cards ${currentPlayer.hole_cards}`, { body });
    }
}

async function onActionRequested(userAction: IBattleUserOptMsg) {
    if (userAction.userId == undefined) {
        return;
    }
    if (userAction.userId != currentUserId) {
        // it might ask for rebuy here
        console.log('Waiting for action from user', userAction.userId || { userAction });
        return;
    }

    setUserStatus(UserStatus.inGamePlay);
    if (IS_DOING_ACTION) {
        return;
    }
    IS_DOING_ACTION = true;
    let availableActions = userAction.canActionList || []
    if (availableActions.length === 0) {
        console.error('No available actions, this should not happen', { userAction });
        return;
    }

    const currentPlayer = gameState.getPlayerById(currentUserId.toString());
    if (currentPlayer && userAction.handCards) {
        currentPlayer.hole_cards = cardsToString(userAction.handCards.map(c => +c));
        console.log(`Player was dealt cards ${currentPlayer.hole_cards}`);
    }

    try {
        await makeAction(userAction);
    } catch (e) {
        console.warn('Could not make action, doing fallback', e);
        await makeFallbackAction(userAction);
    }
    IS_DOING_ACTION = false;
}

async function fetchStrategyAction(): Promise<{ action: POKER_ACTIONS, amount?: number }> {
    if (!gameState.players) {
        console.log('fetchStrategyAction error, no room state', { gameState });
        throw new Error('joined after the game began, actions data may be incomplete');
    }

    const gameAction = await gameState.fetchStrategy({
        // hints: undefined,
        // exploiting_info: TMP_STRATEGY_SERVICE === 'SUBGAME_EXPLOITER' ? { exploits_player_group: 'PokerX' } : undefined,
    });

    const STRATEGY_ACTION_TO_POKER_ACTIONS: Record<StrategyResponseAction, POKER_ACTIONS> = {
        [StrategyResponseAction.CHECK]: POKER_ACTIONS.CHECK,
        [StrategyResponseAction.CALL]: POKER_ACTIONS.CALL,
        [StrategyResponseAction.RAISE]: POKER_ACTIONS.RAISE,
        [StrategyResponseAction.FOLD]: POKER_ACTIONS.FOLD,
        [StrategyResponseAction.ALLIN]: POKER_ACTIONS.ALL_IN,
        [StrategyResponseAction.BET]: POKER_ACTIONS.RAISE,
    };

    const { action, amount } = gameAction;

    return { action: STRATEGY_ACTION_TO_POKER_ACTIONS[action], amount };
}

async function makeAction(body: IBattleUserOptMsg) {
    console.log(`Making action for user ${body.userId}`, body);
    let { action, amount } = await fetchStrategyAction();

    let userAction = body;
    const possibleActions: POKER_ACTIONS[] = userAction.canActionList.map(a => POKER_ACTIONS[a.toString()]);

    if (!possibleActions.includes(action)) {
        const compatibleActions = {
            [POKER_ACTIONS.RAISE]: POKER_ACTIONS.CALL,
            [POKER_ACTIONS.CALL]: POKER_ACTIONS.CHECK
        }
        const compatibleAction = compatibleActions[action];

        if (compatibleAction && possibleActions.includes(compatibleAction)) {
            console.warn(`Strategy action ${action} not in canActionList ${possibleActions}, using ${compatibleAction} instead`);
            action = compatibleAction;

        } else if ([POKER_ACTIONS.RAISE, POKER_ACTIONS.CALL].includes(action) && possibleActions.includes(POKER_ACTIONS.ALL_IN)) {
            console.info(`Strategy action ${action} not in canActionList ${possibleActions}, using ALL_IN instead`);
            action = POKER_ACTIONS.ALL_IN;

        } else {
            throw new Error(`Strategy action is not allowed, got: ${action}, allowed actions: ${possibleActions}`)
        }
    }

    if (!possibleActions.includes(action)) {
        throw new Error(`Action ${action} not in canActionList ${possibleActions}`);
    }

    if (action == POKER_ACTIONS.RAISE) {
        amount = Math.min(Math.max(amount, +body.minRaiseScore), +body.maxRaiseScore);
        if (amount < +body.minRaiseScore || amount > +body.maxRaiseScore) {
            console.warn(
                `Raise amount out of bounds, adjusting`,
                { amount, body }
            );
        }
    }

    if (action == POKER_ACTIONS.FOLD && possibleActions.includes(POKER_ACTIONS.CHECK)) {
        action = POKER_ACTIONS.CHECK;
    }

    sendAction(action, amount);
}

function makeFallbackAction(userAction: IBattleUserOptMsg) {
    let availableActions = userAction.canActionList.map(a => a.toString()) || [];

    const action = availableActions.includes(POKER_ACTIONS.CHECK) ? POKER_ACTIONS.CHECK : POKER_ACTIONS.FOLD;
    console.log('fallback action', { action });

    sendAction(action, undefined);
}

function sendAction(action: POKER_ACTIONS, raiseScore: number | undefined) {
    const delay = Math.random() * 300 + 500; // random delay between 200 and 500 ms
    console.log(`Sending action ${action} ${raiseScore || ""} after ${Math.floor(delay)}ms`);
    setTimeout(() => {
        GameWebsocket.sendHandAction(action, raiseScore);
    }, delay);
}

async function onCloseRoomNotify(body) {
    console.info('Room closed, disconnecting', { body });
    await GameWebsocket.exitRoom();
    setUserStatus(PlayerStatus.STOPPED);
}

function cardsToString(card: number | number[]): string {
    /**
     * 101 = As
     * 401 = Ad
     * 112 = Qs
     * 313 = Kc
     */
    let convert = (c: number) => ({ '1': 'A', '10': 'T', '11': 'J', '12': 'Q', '13': 'K' }[c % 100] || c % 100) + ['s', 'h', 'c', 'd'][Math.floor(c / 100) - 1];
    return Array.isArray(card) ? card.map(c => convert(c)).join('') : convert(card);
}
