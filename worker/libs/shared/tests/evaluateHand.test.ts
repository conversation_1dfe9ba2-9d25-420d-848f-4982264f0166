import { test } from 'node:test';
import assert from 'node:assert';
import { evaluateHand, HandName } from '../src/evaluateHand';

test('Royal Flush', async () => {
    const result = evaluateHand(['Ah', 'Kh'], ['Qh', 'Jh', 'Th', '9h', '8h']);
    assert.strictEqual(result.handName, HandName.STRAIGHT_FLUSH);
});

test('Straight Flush', async () => {
    const result = evaluateHand(['Qh', 'Kh'], ['Jh', 'Th', '9h', '2c', '3d']);
    assert.strictEqual(result.handName, HandName.STRAIGHT_FLUSH);
});

test('Four of a Kind', async () => {
    const result = evaluateHand(['Ah', 'Ac'], ['Ad', 'As', 'Th', '9h', '8h']);
    assert.strictEqual(result.handName, HandName.FOUR_OF_A_KIND);
});