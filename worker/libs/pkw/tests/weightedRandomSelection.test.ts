import { test } from 'node:test';
import assert from 'node:assert';
import { weightedRandomSelection } from 'shared/src/strategy';

// Tests for weightedRandomSelection function from shared/src/strategy
test('weightedRandomSelection - basic functionality', async () => {
    const originalRandom = Math.random;

    const items = [
        { name: 'A', weight: 10 },
        { name: 'B', weight: 20 },
        { name: 'C', weight: 30 },
    ];

    // Test selection with controlled random values
    Math.random = () => 0.1; // Should select first item (weight 10, cumulative 10/60 = 0.167)
    let result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'A', 'Should select item A with low random value');

    Math.random = () => 0.3; // Should select second item (weight 20, cumulative 30/60 = 0.5)
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'B', 'Should select item B with medium random value');

    Math.random = () => 0.8; // Should select third item (weight 30, cumulative 60/60 = 1.0)
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'C', 'Should select item C with high random value');

    Math.random = originalRandom;
});

test('weightedRandomSelection - edge cases', async () => {
    const originalRandom = Math.random;

    // Test with zero total weight
    const zeroWeightItems = [
        { name: 'A', weight: 0 },
        { name: 'B', weight: 0 },
    ];
    Math.random = () => 0.5;
    let result = weightedRandomSelection(zeroWeightItems, (item) => item.weight);
    assert.strictEqual(result.name, 'A', 'Should return first item when total weight is zero');

    // Test with single item
    const singleItem = [{ name: 'Only', weight: 100 }];
    result = weightedRandomSelection(singleItem, (item) => item.weight);
    assert.strictEqual(result.name, 'Only', 'Should return the only item');

    // Test with very high random value (edge of fallback)
    const items = [
        { name: 'A', weight: 10 },
        { name: 'B', weight: 20 },
    ];
    Math.random = () => 0.999999; // Very close to 1
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'B', 'Should return last item with very high random value');

    Math.random = originalRandom;
});

test('weightedRandomSelection - statistical distribution', async () => {
    const originalRandom = Math.random;
    Math.random = originalRandom; // Use real random

    const items = [
        { name: 'Low', weight: 10 }, // 10/60 = ~16.7%
        { name: 'Medium', weight: 20 }, // 20/60 = ~33.3%
        { name: 'High', weight: 30 }, // 30/60 = ~50%
    ];

    const counts = { Low: 0, Medium: 0, High: 0 };
    const runs = 3000;

    for (let i = 0; i < runs; i++) {
        const result = weightedRandomSelection(items, (item) => item.weight);
        counts[result.name as keyof typeof counts]++;
    }

    const lowRate = counts.Low / runs;
    const mediumRate = counts.Medium / runs;
    const highRate = counts.High / runs;

    // Allow some variance but should be roughly proportional to weights
    assert.ok(
        lowRate >= 0.12 && lowRate <= 0.22,
        `Low weight item should be selected ~16.7% of time, got ${(lowRate * 100).toFixed(1)}%`,
    );
    assert.ok(
        mediumRate >= 0.28 && mediumRate <= 0.38,
        `Medium weight item should be selected ~33.3% of time, got ${(mediumRate * 100).toFixed(1)}%`,
    );
    assert.ok(
        highRate >= 0.45 && highRate <= 0.55,
        `High weight item should be selected ~50% of time, got ${(highRate * 100).toFixed(1)}%`,
    );

    // Verify ordering: high > medium > low
    assert.ok(
        highRate > mediumRate && mediumRate > lowRate,
        'Selection rates should be proportional to weights',
    );
});

test('weightedRandomSelection - boundary conditions', async () => {
    const originalRandom = Math.random;

    const items = [
        { name: 'A', weight: 1 },
        { name: 'B', weight: 1 },
        { name: 'C', weight: 1 },
    ];

    // Test exact boundary values
    Math.random = () => 0; // Minimum random value
    let result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'A', 'Should select first item with random = 0');

    Math.random = () => 1 / 3; // Exact boundary between A and B
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'A', 'Should select first item at exact boundary');

    Math.random = () => 2 / 3; // Exact boundary between B and C
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'B', 'Should select second item at exact boundary');

    Math.random = () => 0.999; // Just below 1
    result = weightedRandomSelection(items, (item) => item.weight);
    assert.strictEqual(result.name, 'C', 'Should select last item with high random value');

    Math.random = originalRandom;
});

test('weightedRandomSelection - real data example', async () => {
    const data = {
        f: {
            hand_strategy: 8.348716717954119e-10,
            amount: null,
            pot_size: null,
        },
        c: {
            hand_strategy: 0.6791607141494751,
            amount: null,
            pot_size: null,
        },
        allin2132: {
            hand_strategy: 0.00002322552063560579,
            amount: 480,
            pot_size: 21.318181818181817,
        },
        r50: {
            hand_strategy: 0.13917391002178192,
            amount: 22,
            pot_size: 0.5454545454545454,
        },
        r100: {
            hand_strategy: 0.13755325973033905,
            amount: 28,
            pot_size: 0.8181818181818182,
        },
        r150: {
            hand_strategy: 0.03911614790558815,
            amount: 44,
            pot_size: 1.5,
        },
        r250: {
            hand_strategy: 0.004972789902240038,
            amount: 62,
            pot_size: 2.3181818181818183,
        },
    };
    const actions = Object.values(data);

    const runs = 1000;
    let expectedResultCount1 = 0;
    const expectedHandStrategy1 = 0.6791607141494751;

    let expectedResultCount2 = 0;
    const expectedHandStrategy2 = 0.13917391002178192;

    for (let i = 0; i < runs; i++) {
        const result = weightedRandomSelection(actions, (action) => action.hand_strategy);
        if (result.hand_strategy === expectedHandStrategy1) {
            expectedResultCount1++;
        }
        if (result.hand_strategy === expectedHandStrategy2) {
            expectedResultCount2++;
        }
    }

    const percentage1 = (expectedResultCount1 / runs) * 100;
    const percentage2 = (expectedResultCount2 / runs) * 100;

    // Assert that approximately 60% of results match the expected hand_strategy
    // Allow some variance (±10%) due to randomness
    assert.ok(
        percentage1 >= 60 && percentage1 <= 80,
        `Expected ~60% of results to match hand_strategy ${expectedHandStrategy1}, got ${percentage1.toFixed(1)}% (${expectedResultCount1}/${runs})`,
    );
    assert.ok(
        percentage2 >= 10 && percentage2 <= 20,
        `Expected ~10% of results to match hand_strategy ${expectedHandStrategy2}, got ${percentage2.toFixed(1)}% (${expectedResultCount2}/${runs})`,
    );

    console.log(
        `Results: ${expectedResultCount1}/${runs} (${percentage1.toFixed(1)}%) matched expected hand_strategy`,
    );
    console.log(
        `Results: ${expectedResultCount2}/${runs} (${percentage2.toFixed(1)}%) matched expected hand_strategy`,
    );
});
