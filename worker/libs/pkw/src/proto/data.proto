syntax="proto3";
package data_proto;

enum CMD {
    CMD_DUMMY = 0;

    GET_DATA_REQ = 60001;  // /getdata
    GET_DATA_RESP = 60002; //
    GET_PUBLIC_DATA_REQ = 60004;  // /get_public_data
    GET_PUBLIC_DATA_RESP = 60005;
    HOME_REQ = 60007;              //  /
    HOME_RESP = 60008;             //
    ROOM_RECORDS_LIST_REQ = 60011; // /room_records_list
    ROOM_RECORDS_LIST_RESP = 60012;
    ROOM_RECORD_REQ = 60014;        // /room_record
    ROOM_RECORD_RESP = 60015;
    GAME_HAND_REQ = 60017;        // /game_hand
    GAME_HAND_RESP = 60018;
    GAME_HAND_TEST_REQ = 60021;    // /game_hand_test
    GAME_HAND_TEST_RESP = 60022;
    DO_FAVORITE_REQ=60024;         // /do_favorite
    DO_FAVORITE_RESP = 60025;
    FAVORITE_HAND_REQ = 60027;     // /favorite_hand
    FAVORITE_HAND_RESP = 60028;
    FAVORITE_LIST_NEW_REQ = 60031;  // /favorite_list_new
    FAVORITE_LIST_NEW_RESP=60032;
    GET_BIG_BLIND_REQ = 60034;      // /get_big_blind
    GET_BIG_BLIND_RESP = 60035;
    GET_HAS_BUYIN_REQ = 60037;      // /get_has_buyin
    GET_HAS_BUYIN_RESP = 60038;
    GET_ROUND_INFO_REQ=60041;       //  /get_round_info
    GET_ROUND_INFO_RESP=60042;
    GET_UID_HAND_COUNT_REQ= 60044;  // /getUidhandcount
    GET_UID_HAND_COUNT_RESP= 60045;
    GET_HAND_COUNT_REQ = 60047;     // /gethandcount
    GET_HAND_COUNT_RESP= 60048;
    GET_PLAYER_LATEST_REQ = 60051;  // /getplayerlatest
    GET_PLAYER_LATEST_RESP = 60052;
    JF_GAME_HAND_REQ = 60055;      //getJFGameHand
    JF_GAME_HAND_RESP = 60056;
    JF_ROOM_LIST_REQ = 60057; //   getJFRoomList
    JF_ROOM_LIST_RESP = 60058;
    JF_GAME_UUIDS_REQ = 60060; // getGameuuidList
    JF_GAME_UUIDS_RESP = 60061;
    JF_DATA_REQ = 60062; //
    JF_DATA_RESP = 60063;
	GAME_REVIEW_LIST_REQ = 60064;  // /game_review_list
    GAME_REVIEW_LIST_RESP=60065;
	DELETE_FAVORITE_LIST_REQ = 60068;  // /delete_favorite_list
    DELETE_FAVORITE_LIST_RESP=60069;
    FORCE_SHOW_CARD_REQ = 60071; //强制亮牌
    FORCE_SHOW_CARD_RSP = 60072;
    SEND_CARD_FUN_REQ = 60074; //发发看
    SEND_CARD_FUN_RSP = 60075;
    GAME_UUIDS_REQ = 60077; //gameUuidsJs
    GAME_UUIDS_RESP = 60078;
    GAME_BIG_POT_LIST_REQ = 60080; //请求大底池列表
    GAME_BIG_POT_LIST_RSP = 60081;
    SUBMIT_HAND_RECORD_REQ = 60082; // request message id for submit user hand history record to review 精選牌局提交請求
    SUBMIT_HAND_RECORD_RSP = 60083; // response message id for submit user hand history record to review 精選牌局提交回應
    SUBMIT_HAND_RECORD_MATCHED_RULE_REQ = 60088;
    SUBMIT_HAND_RECORD_MATCHED_RULE_RSP = 60089;
}

message DataMessage {
    string message = 1;  // json结构
}