syntax = "proto3";

package gate_proto;

enum CMD {
    CMD_DUMMY = 0;
    CONNECT_SERVER_FAILED_NOTIFY = 1003;

    // 服务器关闭通知
    SERVER_CLOSE_NOTIFY = 1006;

    // keepSocket连接异常 (serverType>100的服务器会下发)
    SERVER_EXCEPT_NOTIFY = 1007;
}

enum ConnectServerFailedReason {
    Null = 0;
    // 未发现服务器
    NotFound = 1;
    // 连接游戏服失败
    DialFailed = 2;
}

enum ErrorCode {
    ErrorCode_DUMMY = 0;
    OK = 1;
}
// 服务器连接失败通知
message ConnectServerFailedNotify {
    // serverType 和 serverId 也可使用包头中的数据
    uint32 ServerType = 1;
    uint32 ServerId = 2;
    //连接失败原因
    ConnectServerFailedReason Reason = 3;
}

// 服务器关闭通知
message ServerCloseNotify {
    // serverType 和 serverId 也可使用包头中的数据
    uint32 ServerType = 1;
    uint32 ServerId = 2;

    // 对应socket创建时间
    int64 CreateTime = 3;
}

// keepSocket连接异常
message ServerExceptNotify {

}