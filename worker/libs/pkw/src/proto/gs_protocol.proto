syntax = "proto3";

package protocol;

option go_package="common/protocol";
//message header definition
//uint16 		| uint16 | int32  | int32   |  package_len - 12 |
//package_len | msgid  | seq     | playerid |  payload          |

//package_len : 一个消息包的长度
//msgid: 消息的id
//seq:  请求消息的编号（同一个tcp链接建立之后，消息id从0开始，每次发送一个请求编号+1）
//playerid: 玩家的uid
//paylaod:  消息内容（protobuf打包序列化之后的二进制内容放到这里）


//消息id枚举定义
enum MSGID {
	MsgID_Min = 0;
	//server inner proto msgid
	MsgID_ConnClose_Notice = 10;
    MsgId_SysDestroyRoom_Request = 20;
  MsgId_SysRoom_Notice = 30; //内部交互通知

	//server inner proto end
	MsgID_Logon_Request = 10000;
	MsgID_Logon_Response = 10001;
	MsgID_CreateRoom_Request = 10002;
	MsgID_CreateRoom_Response = 10003;
	MsgID_CreateRoom_Notice = 10004;
	MsgID_DestroyRoom_Request = 10005;
	MsgID_DestroyRoom_Response = 10006;
	MsgID_DestroyRoom_Notice = 10007;
	MsgID_JoinRoom_Request = 10008;
	MsgID_JoinRoom_Response = 10009;
	MsgID_LeaveRoom_Request = 10010;
	MsgID_LeaveRoom_Response = 10011;
	MsgID_SitDown_Request = 10012;
	MsgID_SitDown_Response = 10013;
	MsgID_SitDown_Notice = 10014;
	MsgID_Standup_Request = 10015;
	MsgID_Standup_Response = 10016;
	MsgID_Standup_Notice = 10017;
	MsgID_Buyin_Request = 10018;
	MsgID_Buyin_Response = 10019;
	MsgID_StartGame_Request = 10024;
	MsgID_StartGame_Response = 10025;
	MsgID_StartGame_Notice = 10026;
	MsgID_ResetGame_Notice = 10027;
	MsgID_Game_Post_Notice = 10028;
	MsgID_Game_Ante_Notice = 10029;
	MsgID_Game_ElectDealer_Notice = 10030;
	MsgID_Game_Blind_Notice = 10031;
	MsgID_Game_HoleCard_Notice = 10032;
	MsgID_PlayerActionTurn_Notice = 10033;
	MsgID_Action_Request = 10034;
	MsgID_Action_Response = 10035;
	MsgID_PlayerAction_Notice = 10036;
	MsgID_Game_RoundEnd_Notice = 10037;
	MsgID_CommunityCards_Notice = 10038;
	MsgID_Game_ShowCard_Notice = 10039;
	MsgID_Game_Insurance_Notice = 10040;
	MsgID_BuyInsurance_Request = 10041;
	MsgID_BuyInsurance_Response = 10042;
	MsgID_Game_Settlement_Notice = 10043;
	MsgID_Game_Snapshot_Notice = 10044;
	MsgID_Random_Seat_Notice = 10045;
	MsgID_Game_ShowDown_Notice = 10046;
	MsgID_Room_Situation_Request = 10047;
	MsgID_Room_Situation_Response = 10048;
	MsgID_Room_Situation_Notice = 10049;
	MsgID_SendCard_Fun_Request = 10050;
	MsgID_SendCard_Fun_Response = 10051;
	MsgID_SendCard_Fun_Notice = 10052;
	MsgID_SendChat_Request = 10053;
	MsgID_SendChat_Response = 10054;
	MsgID_SendChat_Notice = 10055;
	MsgID_StayPosition_Request = 10056;
	MsgID_StayPosition_Response = 10057;
	MsgID_StayPosition_Notice = 10058;
	MsgID_BackPosition_Request = 10059;
	MsgID_BackPosition_Response = 10060;
	MsgID_BackPosition_Notice = 10061;
	MsgID_ShowCard_Request = 10062;
	MsgID_ShowCard_Response = 10063;
	MsgID_ShowCard_Notice = 10064;
	MsgID_Login_Player_JoinRoom_Notice = 10065;
	MsgID_Waiting_OtherPlayer_Notice = 10066;
	MsgID_UpdateMoney_Request = 10067;
	MsgID_UpdateMoney_Response = 10068;
	MsgID_Buyin_Notice = 10069;
	MsgID_GameRecords_Notice = 10070;

	MsgID_BuyInsuranceResult_Notice = 10074;
	MsgID_InsuranceToomanyLeader_Notice = 10075;
	MsgID_InsuranceHitOuts_Notice = 10076;
	MsgID_InsuranceMissOuts_Notice = 10077;
	MsgID_NoNeedInsurance_Notice = 10078;

	MsgID_Snapshot_Request = 10079;
	MsgID_Snapshot_Response = 10080;

	MsgID_Buyout_Request = 10081;
	MsgID_Buyout_Response = 10082;
	MsgID_Buyout_Notice = 10083;
	MsgID_RealStart_Notice = 10084;
	MsgID_AddActionTime_Notice = 10085;
	MsgID_NotSupport_Insurance_Notice = 10086;

	MsgID_HeartBeat_Request = 10087;
	MsgID_HeartBeat_Response = 10088;

	MsgID_InteractiveExpression_Request = 10089;
	MsgID_InteractiveExpression_Response = 10090;
	MsgID_InteractiveExpression_Notice = 10091;

	MsgID_AddInsuranceTime_Request = 10092;
	MsgID_AddInsuranceTime_Response = 10093;
	MsgID_AddInsuranceTime_Notice = 10094;

	MsgID_AddRoomTime_Request = 10095;
	MsgID_AddRoomTime_Response = 10096;
	MsgID_AddRoomTime_Notice = 10097;

	MsgID_ProhibitSitdown_Request = 10098;
	MsgID_ProhibitSitdown_Response = 10099;
	MsgID_ProhibitSitdown_Notice = 10100;

	MsgID_ForceStandup_Request = 10101;
	MsgID_ForceStandup_Response = 10102;
	MsgID_ForceStandup_Notice = 10103;

	MsgID_PauseGame_Request = 10104;
	MsgID_PauseGame_Response = 10105;
	MsgID_PauseGame_Notice = 10106;

	MsgID_InitiativeDestroyRoom_Notice = 10107;

	MsgID_CheckOutAndLeave_Request = 10108;
	MsgID_CheckOutAndLeave_Response = 10109;
	MsgID_CheckOutAndLeave_Notice = 10110;

	MsgID_DefaultFold_Request = 10111;
	MsgID_DefaultFold_Response = 10112;

	MsgID_OwnerSetBuyinLimit_Request = 10113;
	MsgID_OwnerSetBuyinLimit_Response = 10114;
	MsgID_OwnerSetBuyinLimit_Notice = 10115;

	MsgID_PlayerBuyinsInfo_Request = 10116;
	MsgID_PlayerBuyinsInfo_Response = 10117;
	MsgID_PlayerBuyinsInfo_Notice = 10118;

	MsgID_GameActionTurn_Request = 10119;
	MsgID_GameActionTurn_Response = 10120;

	MsgID_PhotoVerify_Request = 10121;
	MsgID_PhotoVerify_Response = 10122;
	MsgID_PhotoVerify_Notice = 10123;

	MsgID_UploadVerifyPhotoSucc_Request = 10124;
	MsgID_UploadVerifyPhotoSucc_Response = 10125;
	MsgID_UploadVerifyPhotoSucc_Notice = 10126;

	MsgID_GlobalMessage_Notice = 10127;
	MsgID_FairGame_Notice = 10128;

	MsgID_CheckAllianceRoomPriviledge_Request = 10129;
	MsgID_CheckAllianceRoomPriviledge_Response = 10130;

	MsgID_ForceShowCard_Request = 10131;
	MsgID_ForceShowCard_Response = 10132;
	MsgID_ForceShowCard_Notice = 10133;

	MsgID_AddRommExTimeLeft_Notice = 10134;		//房间剩余延时次数通知
	MsgID_AddRoomTimeCount_Request = 10135;     //请求当前房间剩余延时次数
	MsgID_AddRoomTimeCount_response = 10136;    //请求当前房间剩余延时次数响应

	MsgID_RoomDisMiss_Notice = 10137;		//通知当前房间剩余解散时间

	MsgID_RequestJoinRoomWithPassword = 10138;		//有加入房间密码的创建房间申请
	MsgID_ResponseJoinRoomWithPassword = 10139;		//有加入房间密码的创建房间申请响应
	MsgID_RequestCheckBuyinPassword = 10140;		//检查是否添加带入密码
	MsgID_ResponseCheckBuyinPassword = 10141;		//检查是否添加带入密码响应
	MsgID_RequestCheckFirstTimeJoinRoomWithPassword = 10142;		//玩家加入带密码的房间时检查是否是第一次加入
	MsgID_ResponseCheckFirstTimeJoinRoomWithPassword = 10143;		//玩家加入带密码的房间时检查是否是第一次加入响应
	MsgID_RequestCheckFirstTimeBuyinWithPassword = 10144;			//玩家在设置有带入密码的房间带入时检查是否是第一次带入
	MsgID_ResponseCheckFirstTimeBuyinWithPassword = 10145;			//玩家在设置有带入密码的房间带入时检查是否是第一次带入响应
	MsgID_UpdateMoney_Notice = 10146; // 更改玩家筹码

	MsgID_AutoCompleteChips_request = 10147;   // AOF自动补充BB
	MsgID_AutoCompleteChips_response = 10148;

    MsgID_NotiPlayerHoleCard_Notice = 10150;
	MsgID_CheckVpn_Notice = 10151;

    // 10152 - 10300 极速桌占用 begin
	MsgID_QuickLeave_Request = 10152; // 德州极速快速离开
	MsgID_QuickLeave_Response = 10153;
	MsgID_QuickLeave_Notice = 10154;

	MsgID_QuickFold_Request = 10155;
    MsgID_QuickFold_Response = 10156;
    MsgID_TimeOut_QuickFold = 10157; // 超时默认快速弃牌
    MsgID_LastRound_Win = 10158;    // 上局赢的金额提示

    MsgId_KickOff_Request = 10159; // 踢人逻辑
    // 10152 - 10300 极速桌占用 end

	MsgId_GetGameUUIdsJs_Request = 10301;     // 获取GameUUIds
	MsgId_GetGameUUIdsJs_Response = 10302;    // 只是对应一个MsgId_H5_GetGameUUIdsJs_Request的回包
	MsgId_GetGameUUIdsJs_Notice = 10303;      // 获取到的GameUUIds的数据包，分包发送

    MsgId_GuessHandCard_BeginBet_Notice = 10304;
	MsgId_GuessHandCard_Bet_Request = 10305;
	MsgId_GuessHandCard_Bet_Response = 10306;
	MsgId_GuessHandCard_SetBetOpt_Request = 10307;
	MsgId_GuessHandCard_SetBetOpt_Response = 10308;
	MsgId_GuessHandCard_Settle_Notice = 10309;

	MsgId_GetRoomLimit_ID_Request =  10401;   //新手限制桌的房间id请求
	MsgId_GetRoomLimit_ID_Response = 10402;   //新手限制桌的房间id回复

	MsgId_CriticismStart_Notice = 10403;   // 暴击场开始
	MsgId_NotEnoughMoney2Crit_Notice = 10404; // 参与暴击场金币不足

    MsgId_AutoWithdraw_Request =  10405;   //自动撤码小开关
    MsgId_AutoWithdraw_Response = 10406;   //自动撤码小开关


	MsgID_UploadGuessState_Request = 10407;	//是否打开猜手牌上报

	MsgID_ShowCritPrompt_Notice = 10408;

	MsgId_SendBarrage_Request = 10421; //发送弹幕请求
	MsgId_SendBarrage_Response = 10422; //发弹送幕回复
	MsgId_SendBarrage_Notice = 10423; // 通知播放送幕

	MsgId_BarrageCount_Request = 10424; // 弹幕使用数据请求
	MsgId_BarrageCount_Response = 10425;

    MsgID_ReplayForceShowCard_Request = 10426;
    MsgID_ReplayForceShowCard_Response = 10427;
    MsgID_ReplayForceShowCard_Notice = 10428;

    MsgID_ReplaySendCard_Request = 10429;
    MsgID_ReplaySendCard_Response = 10430;
    MsgID_ReplaySendCard_Notice = 10431;

	MsgID_NotiGameUpdateThumb_Request = 10432;
    MsgID_NotiGameUpdateThumb_Response = 10433;

	MsgID_ChangeTable_Request = 10434;       //换桌
	MsgID_ChangeTable_Response = 10435;

	MsgId_NotDisturb_Request = 10528; // 免打扰功能
	MsgId_NotDisturb_Response = 10529;

	MsgId_OpenLive_Request = 10530; // 明星开启直播
	MsgId_OpenLive_Response = 10531;

	MsgId_OpenMike_Request = 10532; // 解说员开关麦克
	MsgId_OpenMike_Response = 10533;

	MsgId_CloseStar_Notice = 10544; // 关闭明星桌通知

    MsgID_FavoriteHand_Request = 10545; //收藏牌谱(区分来源:牌局中或战绩中)
    MsgID_FavoriteHand_Response = 10546;
	MsgId_Like_Request  = 10551; // 点赞
	MsgId_Like_Response = 10552;
	MsgId_Like_Notice = 10553;   // 被赞通知。

	MsgId_GoodFriendJoinTable_Notice = 10556; // 好友加入牌桌通知

    MsgId_IsEmojiFree_Request = 10557; // 表情是否免费
    MsgId_IsEmojiFree_Response = 10558;
    MsgId_IsEmojiFree_Notice = 10559;

	MsgId_IntimacyUp_Notice = 10563; // 亲密关系升级

	MsgId_MikeMode_Request = 10564; // 明星语音模式
	MsgId_MikeMode_Response = 10565;

	MsgId_VoicePrivate_Notice = 10566; // 明星mic控制

	MsgId_CanSpeak_Notice = 10567; // 明星桌普通玩家mic

	MsgId_InviterSeatFreed_Notice =  10568; // 清桌时刷新邀请座位ui

	MsgId_StarCache_Notice = 10569; // 邀请人变换时需要通知客户端刷新列表

	MsgId_StarWillClose_Notice = 10570; // 明星桌即将关闭通知

	MsgId_Tip_Request = 10571;         // 打赏
	MsgId_Tip_Response = 10572;

	MsgId_Luck_StarSeat_Countdown_Notice = 10581; //明星座抽奖倒计时通知

	MsgId_Luck_StarSeat_CloseActive_Notice = 10582; //明星座关闭抽奖活动通知

    MsgId_Luck_StarSeat_DrawResult_Notice = 10583;  //明星座抽奖结果通知

	MsgId_GetLuck_StarSeat_DrawList_Request = 10584;//获取明星座抽奖列表请求
	MsgId_GetLuck_StarSeat_DrawList_Response = 10585;//获取明星座抽奖列表回复

	MsgId_GetSelf_LuckStarSeat_ResultList_Request=10586;//获取自已中奖列表
	MsgId_GetSelf_LuckStarSeat_ResultList_Response=10587;//获取自已中奖列表

	MsgId_RoomNews_Notice = 10576;      // 房间交互消息(礼物，公告)
	MsgId_TipRank_Request = 10577;      // 查看送礼排行榜
	MsgId_TipRank_Response = 10578;
	MsgId_TipRecord_Request = 10591;    // 送/收打赏 记录
	MsgId_TipRecord_Response = 10592;

	MsgId_SendBarrageForbidden_Request = 10594;
	MsgId_SendBarrageForbidden_Response = 10595;
	MsgId_SendBarrageForbidden_Notice = 10596;  // 被禁弹幕

	MsgId_SendBarrageForbiddenConfChange_Notice = 10597;// 禁弹幕的配置更新。
	MsgId_TipForbidden_Notice = 10603;                  // 禁打赏通知 有可能禁 或 解禁
	MsgId_C2CPayment_Block_Notice = 10604;

	MsgId_MagicEmoji_Request   = 10605;
	MsgId_MagicEmoji_Response  = 10606;
	MsgId_MagicEmoji_Notice	   = 10607;
	MsgId_DynamicConfig_Notice	= 10608;

    // Squid Game
	MsgID_RegisterSquidHuntGame_Notice = 10609;
	MsgID_JoinSquidHuntGame_Request = 10610;
	MsgID_JoinSquidHuntGame_Response = 10611;
	MsgID_JoinSquidHuntGame_Notice = 10612;
	MsgID_SquidHuntRefund_Notice = 10613;
	MsgID_StartSquidHuntGame_Notice = 10614;
	MsgID_StartSquidHuntGameFailed_Notice= 10615;
	MsgID_FinalSquidHuntGame_Notice = 10616;
	MsgID_Waiting_OtherPlayer_RebuyIn = 10617;
	MsgID_StartSquidHuntGameGracePeriod_Notice = 10618;
	MsgID_BarrageList_Request = 10619; // 请求弹幕列表
  	MsgID_BarrageList_Response = 10620; // 返回弹幕列表
	MsgID_GeoComply_Generate_Token_Notice = 34005;

	MsgID_PeekCard_Notice = 10621;
	MsgID_PeekCard_Request = 10622;
	MsgID_PeekCard_Finish_Notice = 10624;
	MsgID_PeekCard_Response = 10623;
}


 // 客户端类型
 enum ClientType {
	Dummy = 0;
	Normal = 1; // c++
	OverSeas = 2; // 海外版
	H5 = 3; // h5多语言版app
    H5OverSeas = 4; // h5海外缩减版app
    H5Web = 5; // h5多语言网页版
    H5WebOverSeas = 6; // h5海外缩减版网页版
    H5VietnamLasted = 7; // h5越南版
    H5WebVietnamLasted = 8; // h5越南网页版
    H5CowboyWeb = 9;//牛仔独立网页版
    H5Thailand = 10; // 泰文版
    H5WebThailand = 11; // 泰文版网页版
    H5Arab=12;//阿拉伯版
    H5Hindi=13;//印度语版
    H5Mempoker=14;//印度语版
    PC =15;// PC版
}

 enum Guess_Bet_Type {
    None_Bet = 0;
		User_Bet = 1;
		Auto_Bet = 2;
}

// 服务器用的。
message NoticeSysRoom {
	int32 roomid = 1;
}

// 解说员信息
message CommentatorInfo {
	uint32 uid = 1; // 解说员ID
	uint32 mikeStatus = 2; // 开关麦状态 0. 未开麦 1. 开麦
	uint32 television = 3; // 频道 1. 中文频道 2. 英文频道 3. 越南文频道 4.泰语频道 5.阿拉伯频道
}

message PositionInfo{
	float longtitude = 1; //经度
	float latitude  = 2; //纬度
	string ip = 3; //ip addr
}

message PlayerInfo{
	uint32 playerid = 1;
	int32 seatid = 2;
	string name = 3;
	string headurl = 4;
	string marks = 5;
	int32  gender = 6;
	int64 stake = 7;
	string last_voice = 8; //总是保存玩家发过的最后一条chat消息voice类型的内容
	ActionType last_action = 9;
	bool in_game = 10;
	bool inStay = 12; //是否处于保位离桌
	int32 left_stay_time = 13; //如果是保位离桌状态，就是剩余的保位离桌时间
	int64 round_bet = 14; //玩家的本回合下注总数; 比如当前是turn 我下了2次注 一次200 一次raise到800 这个值就是1000 也就是还在桌面上 没有进入pot的金额
	repeated CardItem cards = 15;  //如果入局了 并且是发送目标 就有这个数据  就是说 如果我是断线重连进来的 我已经入局了并且有手牌 那么就有这个值 或者是针对showdown阶段已经show牌的玩家，包括主动show牌的玩家，就是说底牌可见的就有这个数据
	PositionInfo position = 16; //位置信息
	bool aofShow = 17;// 针对aof 通知客户端展示aof手动补码界面
	bool is_auto_withdraw = 18; //老德州短牌自动撤码开关 true:打开 false:关闭
	bool isGameEnd = 19; // 是否是在游戏结束-游戏开始
	uint32 plat = 20; // 平台玩家 0.pkw玩家 1. wpk玩家
	bool is_online = 21; //true-玩家在线 false-玩家不在线
  int64 user_join_room_time=22; //用户加入房间时间
	uint32 identity = 23; // 明星身份 1. 明星 2. 解说员 3 管理员
	repeated uint32 NotDisturbUids = 24; // 把我屏蔽的玩家
	uint32 liveStatus = 25; //直播状态 0. 未开播  1. 正在直播 2. 已下播
	uint32 mikeStatus = 26; // 开关麦状态 0. 未开麦 1. 开麦
	int32 last_change_voice = 27; //
	uint32 mikeMode = 28; // 明星语音模式 0. 按键  1. 开放麦
	bool canSpeak = 29; // 能否发言 只针对普通玩家
  string wpkSysAvatar = 30; // wpk 廣告取代頭像
  bool isAdUser = 31; // 標註是否為廣告用戶
}


//消息体定义

//logon
message RequestLogon {
  string version = 1;
  string token = 2;
  PositionInfo position = 3;
  string device_info = 4;
  bool is_ban_area = 5;
  bool is_vpn = 6;
  ClientType client_type = 7; // 客户端类型
}

message ResponseLogon {
  int32 error = 1; // 0: success; 1:version not match; 2:uid not-exist; 3:token-failure
  int32 roomid = 2;
  bool anti_simulator = 3;
  uint32 anti_simulator_ignore_cond  = 4; //当级别手数大于设定值是忽略模拟器限制
}

//create room

message RoomParams{
	int32 owner_type 					= 1; //区分普通牌具/俱乐部牌局/定制俱乐部牌局
	int32 game_mode 					= 2; //区分普通牌局/比赛/其它游戏类型
	int32 player_count_max 			= 3; //牌桌最大人数
	int32 rule_blind_enum 				= 4; //牌局限定大小盲注对应id
	int32 rule_buyin_min_enum 			= 5; //局限定最小带入对应id
	int32 rule_buyin_fold 				= 6; //牌局限定最大带入对于最小带入的倍数
	int32 rule_time_limit 				= 7; //牌局时长对应8个时长类型
	int32 rule_switch_insurance 		= 9; //保险功能开关
	int32 rule_switch_anti_cheat 		= 10;//防作弊相关功能开关
	int32 rule_switch_force_straddle 	= 11;//强制straddle功能开关
	int32 rule_switch_random_seat 		= 12;//随机入座功能开关
	int64 rule_ante_amount 			= 13;//ante金额
	string game_name 					= 14;//房间名字
	uint32 club_id 						= 15; //创建房间所属的俱乐部id
	bool is_associated_jackpot          = 16; //是否关联jackpot
	bool is_allin_allfold               = 17; //是否是allin allfold
	repeated uint32 alliance_ids         = 18; //联盟id集合
	string owner_clubname 				= 19;//房间所属俱乐部
	uint32 CreaterId = 20;				     //创建房间的玩家ID
	bool short_game_double_ante			= 21; //短牌模式是否开启Dealer2倍Ante
	bool short_fullhouse_flush_straight_three = 22; //短牌模式开启和长牌同样比牌顺序
	bool is_opened_drawback             = 23; //是否打开撤码功能
	int32 drawback_hold_times 			= 24; //撤出记分牌保留的倍数（为保证整数客户端*10，所以服务器计算时/10）
	int32 drawback_times 				= 25; //撤出记分牌倍数（为保证整数客户端*10，所以服务器计算时/10）
	bool choose_outs 				    = 26; //是否可选outs
	bool muck_switch 				    = 27; //埋牌开关
	bool anti_simulator 			    = 28; //禁止模拟器开关
	bool force_showcard 			    = 29; //强制秀牌开关
	bool unlimit_force_showcard 	    = 30; //无限强制秀牌开关
    string club_head                    = 31; //俱乐部头像
    int32 auto_start_num                = 32; //自动开局的人数 0 代表不是自动开桌
	string join_password				= 33; //进入房间密码 为空表示不设密码
	string buyin_password				= 34; //buyin密码 为空表示不设密码
	bool IscalcIncomePerhand			= 35; //是否每手都计算抽水(只针对长牌除微局) 既把抽
	int32 is_mirco						= 36; //是否微盲局0否1是
	bool is_private						= 37; //房间是否对全体玩家开放,默认0, 0:表示对全体玩家开放 1：表示只对本俱乐部推送的联盟开放
	uint32 force_takeout_amount         = 38; //强制带出限额:为零表示未开启,非零表示每手结束将超过此值的筹码带出
	repeated uint32 bet_multi			= 39; // 跑马可以下注的倍数, 至少两个, 比如可以下5倍和10倍
	uint32 BB                           = 40; // AOF BB倍数
	bool is_ban_vpn						= 41; // 是否禁用vpn
	uint32 anti_multiple               = 42; // AOF 短牌带入倍数
	uint32 BBLimit                     = 43; // zoom根据N倍BB手上筹码限制带入
	repeated StartByOnline startParam  = 44; // 极速开桌用
	uint32 gameid                      = 45; // 极速开房用

	repeated NewPlayerLimit  limitnums = 50; // 老德州新用户入桌限制
	bool   openRoomLimit               = 51; //是否开启新手入桌限制
	repeated uint32  showForClients    = 52; // 此房间对哪些客户端类型可见
	bool   isCriticismField            = 53; // 是否暴击场(只针对德州)
    uint32 minCritProb                 = 54; // 最小暴击概率
    uint32 maxCritProb                 = 55; // 最大暴击概率
    uint32 critNeedMoney               = 56; // 暴击比下金额
	bool   is_open_guess               = 57; //是否开启猜手牌游戏
	string guess_betamout_cfg          = 58; //猜手牌可以下注的金额 例如"500,50000"
	int32 insuranceMode                = 59;    // 保险模式 0 旧 1 新

	bool auto_withdraw                 = 60; //自动撤码开关 true:打开自动撤码开关 false:关闭自动撤码开关
    int32 auto_buyin_val               = 61; //自动补码值
    int32 auto_buyout_val              = 62; //自动撤码值
    int32 bottom_limit                 = 63; //游戏底限值

	uint32 anti_simulator_ignore_cond  = 64; //当级别手数大于设定值是忽略模拟器限制

	bool is_alternate_tables           = 65; //是否交替开桌
	uint32 teaxs_proportion            = 66; //德州扑克占比
	uint32 crit_proportion             = 67; //暴击德州占比

	int32 manual_created = 68;               // 是否为手动创建的牌局 0:模板创建 1:手动创建

	repeated uint32 plats = 69; // 第三方平台

	uint32 reserveSeat = 72; // 明星预留座位
	repeated StarData starData = 73; // 将要来的明星信息
	repeated CommentatorInfo commentators = 74; // 将要来的解说
	bool canSpeak = 75; // 普通玩家是否能发言
	string tableTitle = 76; // 桌布标题
	int64 formalBegin = 77; // 正式开始时间
	repeated uint32 starInviter = 78; // 明星桌特邀玩家
	repeated ProDatas proDatas = 79; // 多级别参数, 共6个级别
	int64 interEmotionCoin = 80; //互动表情需要的金币T
	int64 closeStarTime = 81; // 关闭明星桌时间
	bool showAllHole = 82; // 是否亮开所有底牌
	int32 currencyType = 83; // 桌子货币类型  0:金币 2:金币 101:usd

	bool loose_mode =86; // 松浪桌模式 : 德州扑克长牌 ante设置为大盲的1.25倍 无straddle 最小带入为200bb
	int32 stick_on_level_tab =87; // 強制將牌局使前端顯示在某級別   None = 0,Micro = 1,Small = 2,Medium = 3, High = 4

//	string announcement = 83;       // 初始公告
//	int64 customBarrageFee = 84;  // 自定义弹幕需要的价格配置
//	int32 customBarrageFreeTimes = 84; // 自定义弹幕的免费次数
}

// 礼物价格信息
message TipFeeInfo {
	int32 tipId = 1;  // id
	int64 fee = 2;    // 单价
	string desc = 3;
}

// 房间多级别参数
message ProDatas {
  int32 levelLimit = 1;  // 当前级别对应限制的数量 -1 代表不限制 0 代表不让进 N代表这个级别最多玩家数量
  uint32		nowNum =2; // 当前这个级别房间里面有多少人
	uint32 tableLevel = 3; // 牌桌优先级别
}

message StarData {
	uint32 uid = 1; // 用户ID
	string nickName  = 2; // 昵称
	string thumb = 3; // 头像
	uint32  status = 4; // 0 未上线 1 在线 2 已下播
}


// 明星座开启通知相关信息
message BeginStarNotify {
	int64 notifyTime = 1; // 通知时间(玩家开放时间)
	repeated BeginStarInfo starInfo = 2; // 一张头像对应标题
	repeated string notifyText = 3; // 通知正文(支持多语言)
}

message BeginStarInfo {
	string starPic = 1; // 明星头像图片
	repeated string notifyTopic = 2; // 通知标题(支持多语言)
}


// 老德州州新用户入桌限制单个区间结构
message NewPlayerLimit {
	int32 min =1;     //最小手数
	int32 max =2;	  //最大手数
	int32 num =3;     //可允许的人数
	int32 CurNum = 4; //当前该区间存在的人数
}

// 极速根据在线人数开桌人数动态指定
message StartByOnline {
    int32 onlineWater = 1;    // 低于在线人数值
    int32 member = 2;         // 开桌人数
}

message RequestCreateRoom{
	RoomParams param = 1;
}

message ResponseCreateRoom{
	int32 error = 1; //todo:
}

message NoticeCreateRoom{
	int32 roomid = 1;
}

message RequestDestroyRoom{
	int32 roomid = 1;
}

message ResponseDestroyRoom{
	int32 error = 1;
}

message PlayerSettlement{
	string player_name = 1;
	string player_head = 2;
	int64 player_buyin = 3; //玩家总带入
	int32 player_hand_count = 4; //玩家总共玩了几手牌
	int64 player_settle = 5; //玩家总输赢（当前stake-之前stake)
	uint32 playerid = 6;
}

message CardItem{
	int32 number = 1;
	int32 suit = 2;
}

message HandRecord{
	string player_name 	= 1;
	string player_head = 2;
	int64 player_betting_round_bet = 3; //本局下注的所有筹码总数
	repeated CardItem cards = 4; //手牌
	int64 win_bet = 5; //输赢的筹码数
	bool bought_insurance = 6; //本局是否买过保险
	bool is_fold = 7; //是否弃牌
	bool is_show_card = 8;
	int32 show_card_ids = 9;
	uint32 playerid = 10;
	int64 insurance_bet_amount = 11; //一手牌的投保金额
	int64 insurance_winbet = 12; //一手牌赢的筹码数
	int64 jack_winbet = 13; //一手牌赢的jackpot筹码数
	int64 drawin = 14; //一手牌赢的drawin
	int64 award2_club_fund = 15;
	uint32 last_buyin_clubid = 16;
	bool is_muck = 17; //是否自动埋牌
	bool shot_force = 18; //强制带回击中保险
	bool is_active_show = 19; //主动show
	bool is_force_show = 20; //强制show
	bool IsShowDown = 21; // 是否ShowDown
	int32 LastRoundType = 22; // 玩家坚持到哪一阶段
	int64 cur_drawin = 23; //把抽局当前这一手抽水
	int64 hand_total_valid_bet = 24; //一首牌的总共有效下注
	uint32 plat = 25; // 0.pkw玩家 1.wpk玩家
	uint32 send_card_len = 26; //玩家发发看的长度
	int32 force_show_cnt = 27; //强制亮牌的次数
	enum SeatStatus {
    	Playing = 0;
    	IsWaitingNoCard = 1;
    	IsAwayHasNoCard = 2;
    	IsWaitingHasCard = 3;
    	IsAwayHasCard = 4;
  	}
 	SeatStatus seatStatus = 28;
  string wpk_sys_avatar = 29; // wpk 廣告取代頭像
  bool is_ad_user = 30; // 標註是否為廣告用戶
}

message GameRecord{
	repeated CardItem public_cards = 1;
	repeated HandRecord records = 2;
	int64 total_pot = 3; //本局总的pot金额
	int64 insurance_winbet = 4; //本局保险输赢（系统角度，系统赔付1000，即-1000）
	int64 insurance_bet = 5; //表示这一手的投保额
	int64 insurance_amount = 6; //表示这一手的保险赔付额
	repeated CardItem unsend_public_cards = 7; //未发出的公共牌
}

message NoticeDestroyRoom{
	int32 roomid = 1;
	uint32 room_create_time = 2; //房间创建时间戳
	int32 room_time_limit = 3;  //设定的游戏时长（秒数）
	string room_owner_name = 4;
	int32 room_rule_blind_enum = 5; //小盲的筹码数
	int32 room_game_hand = 6; 	//本房间总共进行了多少游戏手数
	int64 room_max_pot = 7;    //本房间牌局里面，最大一个pot的金额
	int64 room_total_buyin = 8; //所有人的有效带入总和
	repeated PlayerSettlement settlements = 9;
	uint64 room_uuid = 10;
	string room_name = 11;
	string room_uuid_js = 12;
}

message RequestJoinRoom{
	int32 roomid = 1;
	bool is_quick_sit = 2;		//是否快速入座
	string geoComplyToken = 3;  // token for geocomply validation
	bool isForceJoinRoom = 4; // 不管有沒有在其他魷魚房間內都強制取消站位
}

message ResponseJoinRoom{
	enum Error {
    	// The new error code is to check the different situations of leaving in the game.
    	KeepSquidRoom_Won_Least_One = 31123; // user wants to leave and win at least one squid.
    	KeepSquidRoom_Won_None = 31124; // user wants to leave and there is no squid.
		Punished_not_To_Join_Squid_for_given_times = 31125;
  	}
	int32 error = 1;
	int32 roomid = 2;
	int32 form = 3;             // 备用，现在不用。
	uint32 authVerifyCD = 5; // 验证失败重新验证cd
	uint32 left_join_time =6; //玩家剩余可以加入时间(单位：秒)
	string failedReasons = 7; // geocomply check JSON string
	bool spectatorRevealEnabled = 8; // is spectator able to reveal cards
	int32 squidRoomId = 9;
}

message RequestLeaveRoom{
	int32 roomid = 1;
	string svr_jstr =2; //服务器使用的json串,客户端不用传这个值
}

message ResponseLeaveRoom{
	int32 error = 1;
	int32  user_leave_type =2;//0-正常退出 1-系统强制离开 2-客服操作强制离开
	string reason = 3;//退出原因,多语言(中文#英文#越南语) 当 error为0且user_leave_type为2时才有值
}

message RequestSitDown{
	int32 roomid = 1;
	int32 seatid = 2;
	PositionInfo position = 3; //位置信息
	bool isSure = 4; // 明星桌开始前10分钟之前会为true
	string geoComplyToken = 5; // token for geocomply validation
}

message ResponseSitDown{
	int32 error = 1;
	uint32 playerid = 2;  //和谁冲突了
	string playername = 3;
	NewPlayerLimit limit = 4; //新手限制入桌错误原因
	uint32 authVerifyCD = 5; // 验证失败重新验证cd
	int32 roomId = 6;
	int32 seatId = 7;
	uint32 left_sitdown_time =8; //玩家剩余可以坐下时间(单位：秒)
	uint32 starCd = 9; // 明星桌相关提示倒计时CD
	string failedReasons = 10; // geocomply check JSON string
  SquidHuntGameInfo squidHuntGameInfo = 11;
  int64 serverTime = 12;
}

message NoticeSitDown{
	int32 roomid = 1;
	PlayerInfo player = 2;
}

message RequestStandup{
	int32 roomid = 1;
}

message ResponseStandup{
	int32 error = 1;
	uint32 starCD = 2; // 明星桌提示倒计时
}

message NoticeStandup{
	int32 roomid = 1;
	uint32 target_uid = 2;
	int32 seatid = 3;
}

message RequestBuyin{
  int32 roomid = 1;
  int64 amount = 2; //带入金额
  string geoComplyToken = 3; // token for geocomply validation
  uint32 IsAuto = 4; // 针对AOF 是否自动 1、自动 默认0
  bool withLessChip = 5; // will skip buyin under buy mini limit
}

message ResponseBuyin{
	int32 error = 1;
	uint32 playerid = 2;
	string playername = 3;
	NewPlayerLimit limit = 4; //新手限制入桌错误原因
	int32 min_limit = 5; // 账户需要达到的金额
	string billno = 6;
	int64 time = 7;
	string failedReasons = 8; // geocomply check JSON string
}

message BuyinPlayerInfo{
	uint32 playerid = 1;
	int64 amount = 2; //带入金额上限
	string playername = 3;
	string playerhead = 4;
	int32 requestid = 5;
	uint32 timestamp = 6;
	uint32 last_clubid = 8;
	repeated AllianceInfo allianceInfos = 9;
}

message AllianceInfo{
	uint32 alliance_id = 1;
	string alliance_name = 2;
}

message NoticeBuyinToOwner{
	int32 roomid = 1;
	repeated BuyinPlayerInfo buyins = 2;
	string room_name = 3;
	uint32 last_club2allianceid = 4; //联盟(最近一次俱乐部成员向牌局买入的联盟id)
}

message NoticeBuyinToApplicant{
	int32 roomid  = 1;
	int64 amount_limit = 2; //还可以带入的金额
	bool result = 3; //true : ok-to-buyin; false--reject(timeout/reject..etc)
	int64 self_buyin_limit = 4;
	int64 self_buyin = 5;
	int64 self_stake = 6;
	uint64 bank_chips = 7; //身上剩余总筹码
	string room_name = 8;
	uint32 allianceid = 9; //联盟id 联盟牌局：联盟id 非联盟牌局：-1
}

message RequestAnswerBuyin{
	int32 roomid = 1;
	int32 requestid = 2;
	int64 amount = 3;
	bool  result = 4; //true : ok-to-buyin; false--reject
	uint32 alliance_id = 5;
	uint32 club_id = 6;
}

message ResponseAnswerBuyin{
	int32 error = 1;
	int32 requestid = 2;
	uint32 playerid = 3;
	int64 amount = 4;
	int32 roomid = 5;
	bool  result = 6; //true : ok-to-buyin; false--reject
}

message RequestStartGame{
	int32 roomid = 1;
}

message ResponseStartGame{
	int32 error = 1;
}

message NoticeStartGame{
	int32 roomid = 1;
	int64 texasTotalHands = 2; // 系统头像弹窗换自定义头像的总手数同步包含:长牌、短牌、极速、必下、plo
	SquidHuntGameInfo squidHuntGameInfo = 3;
}

//重置牌桌
message NoticeResetGame{
	int32 roomid = 1;
	string gameid = 2; //uuid from CF
	repeated PlayerInfo players = 3;//table player
}

message NoticeRandomSeat{
	int32 roomid = 1;
	repeated PlayerInfo players = 2;
}

message PotInfo{
	int32 potid = 1;
	int64 amount = 2;
}

//post
message NoticeGamePost{
	int32 roomid = 1;
	int32 seatid = 2;  	//post玩家座位号
	int64 amount = 3;   //post金额
}

//ante
message NoticeGameAnte{
	int32 roomid = 1;
	repeated int32 seat_list = 2;
	repeated int32 amount_list = 3;
}

//elect dealer
message NoticeGameElectDealer{
	int32 roomid = 1;
	int32 dealer_seatid = 2;
	int32 sb_seateid = 3;
	int32 bb_seatid = 4;
}

//下盲注
message NoticeGameBlind{
	int32 roomid = 1;
	int64 sb_amount = 2;
	int64 bb_amount = 3;
	repeated int32 straddle_seat_list = 5;   //straddle座位列表
	repeated int32 straddle_amount_list = 6; //straddle金额列表
	repeated int32 post_seat_list = 7; //Post座位列表
	int32 sb_seatid = 8;
	int32 bb_seatid = 9;
	int32 dealer_seatid = 10;
}

//发底牌
message NoticeGameHolecard{
	int32 roomid = 1;
	repeated int32 seat_list = 2;
	repeated CardItem holdcards = 3;
}

enum ActionType {
	Enum_Action_Null 			= 0;
	Enum_Action_Check 			= 1;
	Enum_Action_Fold  			= 2;
	Enum_Action_Call  			= 3;
	Enum_Action_Bet   			= 4;
	Enum_Action_Raise 			= 5;
	Enum_Action_Allin 			= 6;
	Enum_Action_CallMuck 		= 7;
	Enum_Action_AddActionTime 	= 8;
	Enum_Action_SendCard_Common = 9;
	Enum_Action_Send_HoleCards 	= 10;
	Enum_Action_Straddle 		= 11;
	Enum_Action_Post 			= 12;
}

//通知房间内所有人轮到某个玩家进行操作
message NoticePlayerActionTurn{
	int32 roomid = 1;
	int32 curr_action_seat_id = 2;
	int32 curr_action_uid = 3;
	int32 action_time  = 4; //本次操作允许时间
	int32 minimum_bet = 5; //最小下注额度
	int32 last_action_seatid = 6; //上个动作人的座位号
	int32 last_action_uid = 7;  //上个动作人的uid
	bool is_greatest_bet  = 8; //代表上一个玩家是否时有效加注，每个新回合初始为true
	int32 ActionSeq = 9;
	repeated PlayerInfo players = 10;//当前入座了的完整的玩家结构最多9个
	repeated PotInfo pots = 11; //当前牌局的Pot信息结构
	int64 last_bet_amount = 12;
	int64 carr_action_seat_roundbet = 13;
	bool default_fold = 14; //是否选择了自动看或弃牌
	int32 call_amount = 15;
	int64 max_round_bet = 16;
	int64 last_valid_raise_amount = 17; //上一次有效加注差额
	int64 minimum_bet_i64 = 18; //最小下注额度  解决溢出
	repeated CardItem holdcards = 19; // 行动玩家的手牌(其它玩家不会发)
}

message RequestAction{
	int32 roomid = 1;
	ActionType action = 2;
	int64 amount = 3; //bet & raise
	int32 ActionSeq = 4;
	string token = 5;
	int32 keepEnd = 6;  // 弃牌时是否保留到牌局结束 1 是的
	bool  checkBet = 7; // 是否开启大牌检测
}

message ResponseAction{
	int32 error = 1;
	int32 keepEnd = 2;  // 弃牌时是否保留到牌局结束 1 是的
}

//广播玩家做出的动作
message NoticePlayerAction{
	int32 roomid = 1;
	int32 last_action_seat_id = 2;
	ActionType action_type = 3;
	int64 amount = 4;
	repeated PotInfo pots = 5;
	bool default_fold = 6;
	int32 ActionSeq = 7;
}

//通知房间内所有人该轮下注回合结束
message NoticeGameRoundEnd{
	int32 roomid = 1;
	repeated PotInfo pots = 2;
	repeated CardItem public_card = 3; //当前牌局的公共牌信息，有几张发几张
}

enum BettingRoundType {
	Enum_BettingRound_Preflop 	= 0;
	Enum_BettingRound_Flop 		= 1;
	Enum_BettingRound_Turn 		= 2;
	Enum_BettingRound_River 	= 3;
}

//发公共牌
message NoticeCommunityCards{
	int32 roomid = 1;
	repeated CardItem cards = 2; //3张;1张; 1张;
	BettingRoundType betting_round = 3;
}

message ShowCardsSeatInfo {
	int32 show_seat_id = 1;
	repeated CardItem cards = 2; //手牌
}

message ShowCardsPlayerInfo {
	uint32 uid = 1;               //玩家Id
	repeated CardItem cards = 2; //手牌
}

//show牌阶段
message NoticeGameShowCard{
	int32 roomid = 1;
	repeated ShowCardsSeatInfo show_seats = 2;
}

message OutItem{
	int32 outs_id  = 1;
	CardItem card = 2;
	bool is_tie = 3;
}

message FoldItem {
    CardItem card = 1;   // 牌值
    bool inOuts = 2;    // 是否在outs中
}

message PlayerSeatInfo{
	int32 seatid = 1;
	string playername = 2;
	int32 outs_count = 3;
	repeated CardItem holecards = 4;
	int64 total_investment = 5;//该玩家在本手游戏中总共下注的金额 相当于是每一轮round_bet的总和
    uint32  playerid = 6;        //该玩家id
}

//需要购买保险通知
message NoticeGameInsurance{
	int32 roomid = 1;
	repeated PlayerSeatInfo player_seats = 2;
	repeated OutItem outs  = 3;
	int64 pot_amount = 4;
	int64 buy_amount = 5; //上一轮buyer_uid投保额
	int32 buyer_seatid = 6;
	int32 buyer_uid = 7;
	int64 total_pot_amount = 8;// 所有底池金额的总和 不管有没有参与或领先的
	int32 action_seq = 9; //server下发actionseq
	int64 inv_amount = 10; //数据是购买保险者在当前所购买保险的这个底池中所投入的金额
	int64 total_inv_amount = 11; //数据是购买保险者在本手游戏中所投入的金额总数

	int64 total_pot = 12; //数据是本手游戏所有pot的总额

	int32 count_time = 13; //数据是倒计时剩余时间
	int64 limit_amount = 14; //至少需要买多少钱的保险
	repeated CardItem public_cards = 15; //公共牌

    // 强制购买的筹码
    //   此字段前端无需关心
    //   添加此字段的原因是因为后端房间数据中会缓存数据，后面保存到数据库中作为牌局回放记录使用
	int64 force_amount = 16;

	repeated FoldItem foldCards = 17; // 充牌玩家的手牌
	int32 leftCards = 18;                 // 剩余牌张数量
	int32 error = 19; // 处理购买保险时outs>0 但是赔率小于0.1的情况
	int32 NoOutsTimeOut = 20; //购买保险没有Outs时自动关闭弹窗的时间
}

//购买保险请求
message RequestBuyInsurance{
	int32 roomid = 1;
	int64 amount = 2; //购买保险具体金额, 当amount字段为0时则是客户端选择不买保险
	repeated int32 outs_id = 3; //选中的outs id列表
	int32 action_seq = 4; //购买时，从下发通知中原值返回
	bool is_buy = 5; //买保险=true；不买=false
	int32 option = 6; // 客户端选中的区域
}

message ResponseBuyInsurance{
	int32 error = 1; //成功发1，不管够不够买保险都要广播房间里所有人NoticeBuyInsuranceResult
}

message WinPotInfo{
	int32 potid = 1; //赢的potid
	int64 amount = 2; //这个pot里面赢了多少钱
}

message PlayerSettleInfo{
	int32 seatid = 1;
	uint32 playerid = 2;
	int64 amount = 3;
	bool  is_steal_blind = 4;
	repeated WinPotInfo pots = 5;
	int64 total_investment = 6;//该玩家在本手游戏中总共下注的金额 相当于是每一轮round_bet的总和 ResetGame时重置为0
}

message JackPotWinInfo{
	uint32 win_jackpot_id = 1;		//赢得jackpot的人的id
	uint32 win_jackpot_num = 2;		//赢得的jackpot数量
}

//结算信息通知
message NoticeGameSettlement {
	int32 roomid = 1;
	repeated PlayerSettleInfo winners = 2;
	repeated PotInfo pots = 3;
	uint64 gameuuid = 4;
	repeated JackPotWinInfo jinfo = 5;	//中了jackpot的人
    string gameuuid_js = 6; //gameuuid js版本
    int32 noUseGameuuid = 7; // 1 不用  0 用
    int32 hisHands = 8; // 牌谱回放记录条数
	repeated ShowCardsSeatInfo seatInfo = 9; // 展示所有人手牌
	bool is_spectator_enabled = 10;
	SquidHuntGameSettlement squidHuntGameSettlement = 11;
}

message RoomStates {
	bool isBegin = 1; //是否已经开始游戏
	bool isWaiting = 2; //是否在由于人数不足在等待开局
	bool isPause = 3; //是否暂停了游戏
	bool isMute = 4; //是否静音 如果静音 chat消息中voice类型的消息会发送失败
	bool paused = 5; // 房间是否已经暂停状态了
}
message TableStates{
	repeated PlayerInfo players = 1;//当前入座了的完整的玩家结构最多9个
	repeated PotInfo pots = 2; //当前牌局的Pot信息结构 如果是结算阶段已经结算过的pot不发所以最好结算完就删掉该pot
	repeated CardItem public_card = 3; //当前牌局的公共牌信息，有几张发几张
	int32 curr_action_player_seatid = 4;  //当前操作者的座位号 没有就-1
	int32 curr_action_left_time = 5;  //当前操作者剩余的操作时间 没有就-1
	int32 curr_dealer_seatid = 6;  //当前dealer位置
	int32 curr_bb_seatid = 7;  //当前小盲位置
	int32 curr_sb_seatid = 8;  //当前大盲位置
	int32 curr_straddle_seatid = 9;  //当前straddle位置如果有的话
	int64 bb_amount = 10; //大盲注金额
	int64 sb_amount = 11; //小盲注金额
}

message clubInfo{
	uint32 club_id = 1;
	uint32 creater_id = 2;
	string club_name = 3;
}
message JsStringGameUUid {
	string game_uuid_js = 1;
}

message NoticeGameSnapshot{
	int32 roomid = 1;
	uint32 room_owner_id = 2; //创建者id
	RoomParams params = 3; //房间参数
	int64 self_buyin = 5; //我在本房间的总共已经带入过的金额 不是stake 初始为0
	int64 self_stake = 6; //我我在该房间中剩余的stake
	RoomStates rstate = 7; //
	repeated BuyinPlayerInfo buyins = 8; //房间消息结构体 针对那些还未超时并且房主未处理的申请，在进入房间的时候发一遍
	TableStates tstate = 9;
	repeated uint64 game_uuids = 12;
	repeated uint32 prohibit_sitdown_list = 13;
	repeated PlayerBuyinInfo buyin_infos = 17;
	int32 autoaddactiontime_count = 18; //自动补时次数
	int32 actiontime_count = 19; //自己当前的add action time次数
	repeated uint32 club_createrids = 20;
	int64 total_buyout = 21; //自己的总带出
	PayMoneyItems allFeeItems = 23;
	bool is_quick_sit = 24;		//是否快速入座
	repeated JsStringGameUUid game_uuids_js = 25;
	uint32 gameid = 26;	//玩家所属的游戏类型 2德州 5跑码
	bool isvirtual = 27; // 是否虚拟房间
	bool isNowCritTime = 28; // 当前是否暴击局
	bool anyoneAllin = 29; // 有人allin
	repeated int32 starSeats = 30; // 明星座位
	uint32 identity = 31; // 明星身份 1. 明星 2. 解说员 3 管理员
	QuickRaise quickRaise = 32; // 快捷加注信息
	repeated VoicePrivateNotice voicePrivate = 33;  // 明星mic信息
	repeated  uint32 inviterSeats = 34; // 特邀玩家座位
	repeated RoomNews news = 35;        // 最近的送礼缓存消息，只是礼物数据
	int64 nextCustomBarrageFee = 36;       // 下一次发自定义弹幕需要的价格
	string announcement = 37;           // 当前公告。
	string banner = 38;                 // banner
	int32  auth = 39;                   // 权限，目前只有超级管理员， 1是超级管理员 可以封弹幕
	repeated uint32 forbidden = 40;     // 如果玩家权限是超级管理员，这里是封禁时间配置[30分钟，2小时，24小时。]
	bool openCustomBarrage = 41;          // 是否打开自定义弹幕的开关，
	bool openTablePlayerCustomBarrage = 42; // 桌上玩家是否可以发送弹幕
	int64 muteCustomBarrageSeconds = 43;     // 禁止使用自定义弹幕到什么时候。多少秒后，-1表示永久
	repeated TipUserContr rankPlayers = 44;  //前3名排行榜数据
	int32 allPlayersCount = 45; // 所有玩家的数量（包含旁观者）
	string roomUuidJs = 46; //房间唯一id(字符串)
	repeated TipFeeInfo tipFees = 47 ; // 明星桌礼物价格，
	int64 muteTipSeconds = 48;          // 禁止打赏功能到什么时候。多少秒后 -1 表示永久 （现在只会传-1 或者 0）
	bool forceWithdrawMode = 49;		//短牌优化, 若為True: 默认的的最大带入倍数为最小带入的1倍; 默认打开所有人的自动撤码功能，且不可关闭。
	DynamicConfig dynamicConfig = 50;
	uint32 mvpid = 51;
    SquidHuntGameParams squidHuntGameParams = 52;
    SquidHuntGameInfo squidHuntGameInfo = 53;
    SquidHuntGameSettlement squidHuntGameSettlement = 54;
    int64 serverTime = 55;
}

message QuickRaise {
	QuickRaiseInfo preFlopQuickRaise = 1; // 翻前
	QuickRaiseInfo postFlopQuickRaise = 2; // 翻后
}

message QuickRaiseInfo {
  repeated  string raise3 = 1; // 3个按钮选择(快捷加注)
  repeated  string raise5 = 2; // 5个按钮选择(快捷加注)
  uint32 selected = 3; // 被选中按钮
  repeated  string raise7 = 4; // 7个按钮选择(快捷加注)
}

message PayMoneyItems {
    uint32 playWay = 1; // 0 普通玩法 1 短牌玩法
    int32 actionCount = 2; // 操作延迟次数
    int32 showCardCount = 3; // 强制亮牌的次数
    int32 insuranceCount = 4; // 延迟购买保险的次数
    repeated FeeItem actionDelayCountsFee = 5; // 操作延迟思考次数对应的费用
    repeated FeeItem showCardCountsFee = 6; // 强制亮牌次数对应的费用
    repeated FeeItem insuranceCountsFee = 7; // 购买延迟保险时限次数对应的费用
    repeated FeeItem showLeftCardFee = 8; // 发发看对应的费用
    FeeItem emotionFee = 9; // 互动表情对应的费用
    FeeItem emotionFee2 = 10; // 普通表情对应的费用
    int32 actionMoney = 11; // 延迟思考需要的金额(之前的逻辑应该用这种方式，旧代码没去做修改)
	FeeItem magicEmojiFee = 12; // 酷炫(魔法)表情对应的费用
	repeated FeeItem showCardCountsSpectatorFee = 13; // 强制亮牌次数对应的费用
	int32 spectatorShowCardCount = 14; // amount of times spectator call to show cards
}

message FeeItem {
    uint32 startCount = 1; // 起始的次数 举例:[1-1] [2-5]
    uint32 endCount = 2; // 结束的次数
    uint32 needCoin = 3; // 需要的钱(已经*100) 比方说 0.5 值为 50
    string state = 4; // flop turn river(发发看不同阶段收钱不同)
	uint32 needUsd = 5; // 需要的美金(已经*100) 比方说 0.5 值为 50
	uint32 needDiamond = 6; // 需要的鑽石(已经*100) 比方说 0.5 值为 50
}

message PlayerShowDownInfo{
	int32 seatid = 1;
	uint32 playerid = 2;
	repeated CardItem cards = 3; //手牌
}

message NoticeGameShowDown{
	int32 roomid = 1;
	repeated PlayerShowDownInfo shows = 2;
	repeated uint32 muck_list = 3;
}

message RequestRoomSituation{
	int32 roomid = 1;
}
message ResponseRoomSituation{
	int32 error = 1;
}
message PlayerBuyinInfo{
	string playername = 1;
	uint32 playerid =  2;
	int64 total_buyin = 3;
	int64 curr_record = 4;
	int64 buyin_limit = 5; //已批准的带入上限
	int64 total_buyout = 6; // 玩家总带出
	int64 HandCount =7; // 玩家手数
	uint32 Identity =8; // 玩家身份（1.明星 2.解说员 3.管理员）
	bool jpHit = 9; // 是否击中jp
}
message NoticeRoomSituation{
	int32 roomid = 1;
	repeated PlayerBuyinInfo buyin_player_list = 2;
	repeated PlayerInfo observer_list = 3;
	int64 insurance_winbet = 4; //本局保险输赢（系统角度，系统赔付1000，即-1000）
	int32 left_time = 5; //房间剩余秒数
	int32 room_start_time = 6; //点击开始时间
	repeated uint32 bystander_list = 7; //旁观者列表（不包含已退出房间的）
	int64 jackpot_winbet = 8; //本局jackpot输赢（系统角度，系统赔付1000，即-1000）
	repeated uint32 check_out_list = 9; //结算离桌的玩家列表
	uint32  gameid = 10;
	ObserverDetails observer_info = 11;//旁观者数据明细
	uint32 mvpid  = 12; //MVP id
}

message ObserverDetails {
   int32 online_count = 1;//旁观玩家在线人数
   int32 total_count =2;//旁观玩家总人数
}

message RequestSendCardFun{
	int32 roomid = 1;
}
message ResponseSendCardFun{
	int32 error = 1;
}
message NoticeRoomCardFun{
	int32 roomid = 1;
	int32 round_state = 2;
	string player_name = 3;
	repeated CardItem cards = 4;
	int32 next_price = 5;
	uint32 player_id = 6;
}

enum ChatType {
	Enum_Emoji 			= 0;
	Enum_Voice 			= 1;
}

message RequestSendChat{
	int32 roomid = 1;
	ChatType ctype = 2;
	string content = 3;
	int32 change_voice = 4;
}

message ResponseSendChat{
	int32 error = 1;
	int32 next_fee = 2;
}

message NoticeSendChat{
	int32 roomid = 1;
	ChatType ctype = 2;
	string content = 3;
	uint32 playerid = 4;
	int32 seatid   = 5;
	int32 change_voice = 6;
}

message RequestStayPosition{
	int32 roomid = 1;
}

message ResponseStayPosition{
	int32 error = 1;
}

message NoticePlayerStay{
	int32 roomid = 1;
	repeated PlayerInfo players = 2;
	SquidHuntGameInfo squidHuntGameInfo = 3;
}

message RequestBackPosition{
	int32 roomid = 1;
	string geoComplyToken = 2;
}

message ResponseBackPosition{
	int32 error = 1;
	string failedReasons = 2; // geocomply check JSON string
}

message NoticeBackPosition{
	int32 roomid = 1;
	PlayerInfo player = 2;
	SquidHuntGameInfo squidHuntGameInfo = 3;
}

message RequestShowCard{
	int32 roomid = 1;
	int32 cards = 2; //0，1表示第一张和第二张
	bool is_show = 3; //表示show或者不show
	repeated int32 cardList = 4; // 只是plo使用，plo时不使用cards
}

message ResponseShowCard{
	int32 error = 1;
}

message PlayerShowInfo{
	int32 playerid = 1;
	int32 seatid =  2;
	repeated CardItem cards = 3;
}

message NoticePlayerShow{
	int32 roomid = 1;
	repeated int32 show_card_id = 2;
	repeated PlayerShowInfo players = 3;
}

message NoticeLoginPlayerJoinRoom{
	int32 roomid = 1;
	bool anti_simulator = 2;
}

message NoticeWaitingOtherPlayer{
	int32 roomid = 1;
}

message RequestUpdateMoney{
	int64 money = 1;
}
message ResponseUpdateMoney{
	int32 error = 1;
}
message NoticeUpdateMoney {
   uint32 plyer_id = 1;
   uint32 seat_id = 2;
   uint32 room_id = 3;
   int64  stake = 4;
}


//处理完带入请求后（无论成功失败），发送给请求者
message NoticeBuyin{
	int64 self_buyin = 2; //我在本房间的总共已经带入过的金额 不是stake 初始为0
	int64 self_stake = 3; //我在本房间剩余的stake
	int64 bank_chips = 4; //我剩余的筹码（总共的）
	int64 self_buyout =5; //剩余buyout总数
	int32 roomid = 6;
	int64 buyin_amount = 7; //买入金额
	bool next_hand = 8; // 下一手生效
	uint32 playerid = 9;
	bool is_auto = 10; // true:自动买入 false:手动买入
	int64   usdt_subtract=11;//usdt数量减少了多少  如果为0-没有发生Usdt自动转换
    int64   gold_add =12;// 金币增加多少  如果为0-没有发生Usdt自动转换
}

message NoticeGameRecords{
	int32 roomid = 1;
	repeated GameRecord records = 2; // 返回所请求的手牌纪录
}

//请求增加带入上限
message RequestModifyBuyinLimit{
	int64 buyin_limit = 1; //单次增加的上限值
	uint32 last_buyin_clubid = 2;
	uint32 last_buyin_ownerid = 3;
	string last_buyin_clubname = 4;
}

message ResponseModifyBuyinLimit{
	int32 error = 1;
	int32 roomid = 2;
	uint32 playerid = 3;  //和谁冲突了
	string playername = 4; //和谁冲突了
}

message NoticeModifyBuyinLimit{
	int64 buyin_limit = 1; //修改后的带入上限
	int64 buyin_now = 2; //当前已经带入的筹码
	int32 roomid = 3;
}

//通知保险购买结果
message NoticeBuyInsuranceResult{
	int32 room_id = 1;
	string player_name = 2; //购买保险者的名称
	bool result = 3; //是否购买了保险
	int64 insure_amount = 4; //投保额
}

//最大池多个领先者，无法购买保险
message NoticeInsuranceToomanyLeader{
	int32 room_id = 1;
}

//击中outs赔付
message NoticeInsuranceHitOuts{
	int32 room_id = 1;
	string player_name = 2; //购买保险者的名称
	uint32 playerid = 3;
	int64 insure_amount = 4; //投保额
	CardItem card = 5; //击中的outs 结构 card_suit  card_num
	int64 payment = 6; //赔付额  需要立刻加到stake里
}

//未击中outs
message NoticeInsuranceMissOuts{
	int32 room_id = 1;
	uint32 playerid = 2;
	int64 insure_amount = 3; //投保额
}

message NoticeNoNeedInsurance{
	int32 room_id = 1;
}

//
message RequestSnapshot{
	int32 roomid = 1;
}

message ResponseSnapshot{
	int32 error = 1;
}

message RequestBuyout{
	int32 roomid = 1;
	int64 buyout_gold = 2;
}

message ResponseBuyout{
	int32 error = 1;
}

message NoticeBuyout{
	int32 roomid = 1;
	int64 buyout_gold = 2;
	int64 remain_gold = 3;
	uint32 seat_no = 4;
	int64 total_buyout = 5;
	bool is_auto = 6; // 是否自动撤出 true:自动撤出 false:手动撤出
}

message NoticeRealStart{
	int32 roomid = 1;
	int32 starttime = 2;
}

message NoticeAddActionTime{
	int32 roomid = 1;
	int32 action_seatid = 2;
	int32 rest_action_time = 3;
	int32 count = 4; // 这人延迟时间用掉了多少次
	int32 money = 5;   // 下次使用需要多少钱
}

message NoticeNotSupportInsurance{
	int32 roomid = 1;
}

message RequestHeartBeat{
	uint32 uid = 1;
	PositionInfo position = 2; //位置信息
}

message ResponseHeartBeat{
	uint32 uid = 1;
	int64 timestamp = 2; // 服务器当前时间戳
}

message RequestInteractiveExpression{
	int32 roomid = 1;
	string content = 2;
	EmojiType type = 3;
}

message ResponseInteractiveExpression{
	int32 error = 1;
	int32 left_duration = 2;//可以发互动表情的等待时间，必须1249错误码才有值，其它错误都是0值
}

message NoticeInteractiveExpression{
	int32 roomid = 1;
	string content = 2;
	uint32 playerid = 3;
	int32 seatid   = 4;
	EmojiType type = 5;
}

message RequestAddInsuranceTime{
	int32 roomid = 1;
	int32 action_seq = 2;
}

message ResponseAddInsuranceTime{
	int32 error = 1;
}

message NoticeAddInsuranceTime{
	int32 roomid = 1;
	uint32 playerid = 2;
	int32 action_seatid = 3;
	int32 rest_insurance_time = 4;
	int32 count = 5; // 这人保险延迟时间用了多少次
}

message RequestAddRoomTime{
	int32 roomid = 1;
}

message ResponseAddRoomTime{
	int32 error = 1;
}

message NoticeAddRoomTime{
	int32 roomid = 1;
	uint32 playerid = 2;
}

message RequestProhibitSitdown{
	int32 roomid = 1;
	uint32 targetid = 2;
	bool isProhibitSitdown = 3;
}

message ResponseProhibitSitdown{
	int32 error = 1;
}

message NoticeProhibitSitdown{
	int32 roomid = 1;
	uint32 playerid = 2;
	string playername = 3;
	bool isProhibitSitdown = 4;
	repeated uint32 prohibit_sitdown_list = 5;
}

message RequestForceStandup{
	int32 roomid = 1;
	uint32 targetid = 2;
	string svr_jstr =3; //服务器使用的json串,客户端不用传这个值
}

message ResponseForceStandup{
	int32 error = 1;
	int32  user_standup_type =2;//0-正常创建者操站出 1-系统强制站起
	string reason = 3;//退出原因 当 error为0且user_standup_type不为0时才有值
	uint32 starCD = 4; // 明星桌提示倒计时
}

message NoticeForceStandup{
	int32 roomid = 1;
	uint32 playerid = 2;
	string playername = 3;
}

message RequestPauseGame{
	int32 roomid = 1;
	bool isPause = 2;
}

message ResponsePauseGame{
	int32 error = 1;
}

message NoticePauseGame{
	int32 roomid = 1;
	bool isPause = 2; // 客户端请求的暂停/取消按钮
	bool paused = 3; // 房间是否已经暂停状态了
}

message NoticeInitiativeDestroyRoom{
	int32 roomid = 1;
	string roomname = 2;
}

message RequestCheckOutAndLeave{
	int32 roomid = 1;
	bool  is_svr_req=2; //是否是服务端发送的请求，服务器专用
}

message ResponseCheckOutAndLeave{
	int32 error = 1;
}

message NoticeCheckOutAndLeave{
	int32 roomid = 1;
	int32 targetid = 2;
	string name = 3;
}

message RequestDefaultFold{
	int32 roomid = 1;
	int32 type   = 2;
}

message ResponseDefaultFold{
	int32 error = 1;
}

message RequestOwnerSetBuyinLimit{
	int32 roomid = 1;
	uint32 targetid = 2;
	int64 limit_amount = 3;
}

message ResponseOwnerSetBuyinLimit{
	int32 error = 1;
}

message NoticeOwnerSetBuyinLimit{
	int32 roomid = 1;
	int32 targetid = 2;
	int64 limit_amount = 3;
}

message RequestPlayerBuyinsInfo{
	int32 roomid = 1;
}

message ResponsePlayerBuyinsInfo{
	int32 error = 1;
}

message NoticePlayerBuyinsInfo{
	int32 roomid = 1;
	repeated PlayerBuyinInfo buyin_infos = 2;
}

message RequestGameActionTurn{
	int32 roomid = 1;
	string token = 2;
}

message ResponseGameActionTurn{
	int32 error = 1;
}

message RequestPhotoVerify{
	int32 roomid = 1;
	int32 targetid = 2;
}

message ResponsePhotoVerify{
	int32 error = 1;
}

message NoticePhotoVerify{
	int32 roomid = 1;
	uint32 ownerid = 2;
	int32 targetid = 3;
}

message RequestUploadVerifyPhotoSucc{
	int32 roomid = 1;
	string url = 2;
}

message ResponseUploadVerifyPhotoSucc{
	int32 error = 1;
}

message NoticeUploadVerifyPhotoSucc{
	int32 roomid = 1;
	int32 targetid = 2;
	string url = 3;
}

// 跑马灯类型
enum CastMsgType {
	CastMsgTypeNone = 0 ;
	CastMsgTypeCloseStar = 1; // 明星桌关闭跑马灯
}

//游戏内跑马灯通知
message NoticeGlobalMessage{
	int32 repeat_count = 1; //重复播放次数
	string msg = 2;
	int32 cast_msg_type = 3; // 跑马灯类型
}

message NoticeFairGame{
	uint32 playerid = 1;   //被站起玩家id
	string playername = 2; //被站起玩家昵称
	uint32 playerid2 = 3;  //playerid和谁冲突了
	string playername2 = 4;  //
	bool ip = 5;  //ip问题
	bool gps = 6; //gps问题
	int32 roomid = 7;  //
	string roomname = 8;
}

message NoticeCheckVpn{
	uint32 playerid = 1;   //被站起玩家id
	string playername = 2; //被站起玩家昵称
}

message RequestCheckAllianceRoomPriviledge{
	uint32 playerid = 1;   //被站起玩家id
}

message ResponseCheckAllianceRoomPriviledge{
	int32 error = 1;
}

message RequestForceShowCard{
	int32 roomid = 1;
}

message ResponseForceShowCard{
	int32 error = 1;
}

message NoticeForceShowCard{
	int32 roomid = 1;
	uint32 playerid = 2; //请求者id
	string playername = 3; //请求者名字
	repeated ShowCardsSeatInfo show_seats = 4;
	int32 count = 5; // 这人强制亮牌用了多少次
	int32 spectatorCount = 6; // amount of times spectator has asked to show cards
	bool isFromSquid = 7; // squid : random show win's card
}

message NoticeAddRoomTimeLeft{
	int32 roomid = 1;
	int32 leftcount =2;		//剩余可以延时次数
}

message RequestAddRoomTimeCount{
    int32   roomid = 1;
}

message ResponseAddRoomTimeCount{
    int32 error = 1;
}


//通知房间即将解散消息
message NoticeRoomDisMiss{
	uint32 uid = 1;
	uint32 club_id = 2;	//俱乐部id
	string club_name = 3;	//俱乐部名称
	uint64 room_id = 4;	//房间id
	string room_name = 5;	//房间名称
	uint32 left_time = 6;	//剩余时间
	uint32 cur_time = 7;	//当前时间
	uint32 msg_type = 8;
}

message RequestJoinRoomWithPassword{
	int32 roomid = 1;
	string join_password = 2; //加入房间密码
}

message ResponseJoinRoomWithPassword{
	int32 error = 1;
	int32 roomid = 2;
}

message RequestCheckBuyinPassword {
	int32 roomid = 1;
	string buyin_password = 2; //带入密码
}

message ResponseCheckBuyinPassword {
	int32 error = 1;
	int32 roomid = 2;
}

message RequestCheckFirstTimeJoinRoomWithPassword{
	int32 roomid = 1;
}

message	ResponseCheckFirstTimeJoinRoomWithPassword{
	int32 error = 1;
	int32 roomid = 2;
	bool isfirst = 3;	//返回是否是第一次加入
}

message RequestCheckFirstTimeBuyinWithPassword{
	int32 roomid = 1;
}

message	ResponseCheckFirstTimeBuyinWithPassword{
	int32 error = 1;
	int32 roomid = 2;
	bool isfirst = 3;  //返回是否是第一次带入
}

message RequestAutoCompleteChips{
    uint32  PlayerID = 1; // 玩家ID
    uint32  RoomID  =2;  // 在房间内需要设置传房间ID
    uint32  Enable = 3; // 1 开启
}

message ResponseAutoCompleteChips{
    int32 error = 1;
    uint32 Enable = 2;
}

message NotiPlayerHoleCard {
   repeated CardItem holdcards = 3;
}

message RequestQuickLeave {
   int32  RoomID = 1;
}

message ResponseQuickLeave {
   int32 Error = 1;
}

message NotiQuickLeave {
   	int32 RoomID = 1;
   	string RoomName = 2;
   	int32 PlayerID = 3;
   	string Name = 4;
   	int64  CurrStake = 5;
   	int64  SettleStake = 6;// 结算金币
   	int64  InGameTime = 7; // 玩家游戏时间
   	int64  HandCount = 8; // 玩家手数
}

message RequestQuickFold{
	int32 RoomID = 1;
	bool  CheckBet  = 2; // true-检查是否下注如果下注就不自动弃牌并返回错误码，无下注自动弃牌 false-不检查是否下注，自动弃牌
	int32 keepEnd = 3;  // 弃牌时是否保留到牌局结束 1 是的
}

message ResponseQuickFold{
	int32 Error = 1;
	int32 keepEnd = 2;  // 弃牌时是否保留到牌局结束 1 是的
}

message NotifyLastRoundWin {
    int64 amount = 1; // 上一局赢的钱提示
}

message RequestGetGameUUIdsJs {

}


message ResponseGetGameUUIdsJs {
	int32 Error = 1;
}

message NoticeGetGameUUIdsJs{
	repeated JsStringGameUUid list= 1;
	int32 total = 2;                // 总数量
	int32 page = 3;					// 当前页
}


message RequestGetRoomLimitId {
	int32 roomid = 1;     //当前房间id
    bool  mo_player = 2;  //是否是模拟器玩家
}

message ResponseGetRoomLimitId {
	int32 Error = 1;      //错误码   (RoomLimit_No_Room:当前级别没有合适的桌子 OK:成功)
	int32 roomid = 2;	  //返回的新房间id
}

message NoticeCritisicmStart {
   uint32  roomid = 1; // 房间id
}

message NoticeNotEnougnMoney2Crit {
   uint32  roomid = 1;
   uint32  playerid = 2;
}

enum HandCardResult {
    Enum_None = 0 ;       //无效的下注选项
	Enum_PairAndAA = 1;  //手牌一对AA
	Enum_Pair 	= 2;     //手牌一对
	Enum_High_A = 3 ;    //手牌带A
	Enum_High_AorK = 4;  //手牌带A或K
	Enum_High_None_AorK = 5; //手牌没有A或K
}

message GuessOdds {
   int32 option = 1;  //对应HandCardResult其中之一
   int32 odds_percent = 2; //放大100倍 例 6.5 => 650
}

message NoticeGuessBeginBet {
   repeated GuessOdds list =1;
   int64 bet_seqno =2;
   repeated int64 betamout_opt =3;
}

message GuessBetReq {
 int32 option = 1;
 int64 amonut =  2;
 int64 bet_seqno =3;

}

message GuessBetRsp {
 int32 Error = 1;
 int64 amount = 2;
 GuessOdds odds = 3;
 bool repeat= 4;
 int64 change_points = 5; // 积分
 int32 left_time = 6;//距离下一局多长时间 -1 表示很长   其他正值实际时间 0马上开始(客户端自已决策)
}

message GuessSetBetOptReq {
 bool repeat = 1;

}

message GuessSetBetOptRsp {
 int32 Error = 1;
 bool repeat= 2;
}

message GuessSettleNotice {
  int64 win_amount =1;
  bool  is_return_back = 2;
}

message RequestAutoWithdraw {
	bool is_open = 1;     //玩家自定义自动撤码(默认打开) true:打开自动撤码 false：关闭自动撤码
}

message ResponseAutoWithdraw {
	int32 Error = 1;      //错误码
	bool is_open = 2;
}

message UploadGuessStateRequest {
     int32 room_id = 1;
     bool  is_open_guess = 2;
}

message ShowCritPromptNotice {
	int32 room_id = 1;
}

enum BarrageType {
	Enum_System		= 0; //系统弹幕
	Enum_Custom 	= 1; //用户自定义弹幕
	Enum_Liked      = 3; // 点赞的服务器通知弹幕
	Enum_Tip        = 4; // 打赏弹幕。
	Enum_PlayerEnters = 5; // 玩家进入玩家弹幕，只发给主播。
}

message RequestSendBarrage {
	int32 roomid = 1; //房间id
	BarrageType ctype = 2; //弹幕类型
	string content = 3; //弹幕内容
	repeated string at_list= 4; //@列表
	int32 thump_up_status =5; //点赞状态  1，未点赞 2，已点赞 3，点赞后复制的
	repeated uint32 at_uid_list = 6; // @点赞的用户id列表
}

message ResponseSendBarrage{
	int32 error = 1;  //错误码
	uint32 barrageId = 2 ; // 弹幕Id
	uint64 useCount = 3; // 使用次数
	//int64 nextCustomBarrageFee = 4;       // 下一次发自定义弹幕需要的价格,只有明星桌用
	int64 afterSecondsCanSend      = 5;       // 多少秒后可再次使用自定义弹幕，-1表示被永久封禁了。
}

message NoticeSendBarrage{
	int32 roomid = 1; //房间id
	BarrageType ctype = 2; //弹幕类型
	string content = 3; //弹幕内容
	uint32 playerid = 4; //发送者用户id
	string nickname  = 5; //发送者昵称
	string avatar = 6; //发送者头像
	int64 send_time = 7; //发送时间
	repeated string at_list= 8; //@列表
	int32 thump_up_status =9;   //点赞状态  1，未点赞 2，已点赞 3，点赞后复制的
	repeated uint32 at_uid_list = 10; // @点赞的用户id列表
	string liked_nickname = 11; // 点赞用，被点赞的人的昵称
	uint32 liked_playerid = 12; //
	string liked_avatar = 13;
	UserTipInfo userTipInfo = 14; // 弹幕类型是打赏时，这个有效
	PlayerEntersBarrage playerEntersBarrage = 15; //  玩家进入房间的通知弹幕，在弹幕类型时Enum_PlayerEnters有用
  uint32 plat = 16;
  string wpkSysAvatar = 17; // wpk 廣告取代頭像
  bool isAdUser = 18; // 標註是否為廣告用
}

message PlayerEntersBarrage {
	string nickname = 1;
}

message BarrageCountReq{}

message BarrageCountRsp {
	int32 error = 1;  //错误码
	repeated BarrageCount Infos = 2;
}

message BarrageCount {
	uint32 BarrageId = 1; // 弹幕Id
	uint64 UseCount = 2; // 使用次数
}

message RequestReplayForceShowCard{
	int32 roomid = 1;
	string game_uuid = 2;
}

message ResponseReplayForceShowCard{
	int32 error = 1;
}

message NoticeReplayForceShowCard{
	int32 roomid = 1;
	uint32 playerid = 2; //请求者id
	string playername = 3; //请求者名字
	repeated ShowCardsPlayerInfo show_seats = 4;
	int32 count = 5; // 这人强制亮牌用了多少次
	int32 spectatorCount = 6;
}

message RequestReplaySendCard{
	int32 roomid = 1;
	string game_uuid= 2;
}
message ResponseReplaySendCard{
	int32 error = 1;
}
message NoticeReplaySendCard{
	int32 roomid = 1;
	int32 round_state = 2;
	string player_name = 3;
	repeated CardItem cards = 4;
	int32 next_price = 5;
	uint32 player_id = 6;
}


message RequestNotiGameUpdateThumb {
}
message ResponseNotiGameUpdateThumb {
	int32 error = 1;
}

message RequestChangeTable {      // 换桌发起
	int32 roomid = 1;
	string geoComplyToken = 2; // token for geocomply validation
}

message ResponseChangeTable {    // 换桌返回
	int32 error = 1;
	int32 newroomid = 2;        // 新房间id,现在不用。
	string failedReasons = 3; // geocomply check JSON string
}

message NotDisturbReq {
	uint32 operate = 1; //1. 禁止  2， 解除限制
	uint32 whoId = 2; // 被禁止或解除的uid
}

message NotDisturbRsp {
	int32 error = 1;  //错误码
	uint32 operate = 2; //1. 禁止  2， 解除限制
	uint32 whoId = 3; //  被禁止或解除的uid
}

message OpenLiveReq {
	int32  liveOp = 1; // 开启或关闭直播
}

message OpenLiveRsp {
	int32 error = 1;  //错误码
	int32 liveStatus = 2; //直播开启状态 0. 直播未开启 1. 直播已开启 2. 已下播
	uint32 uid = 3;
}

message OpenMikeReq {
	int32 mikeOp = 1; // 开启或关闭麦克风
}

message OpenMikeRsp {
	int32 error = 1;  //错误码
	int32 mikeStatus = 2; // 开关麦状态 0. 未开麦 1. 开麦
	uint32 uid = 3;
}

message CloseStarNotice {
	bool isClosedStar = 1; // 明星座是否关闭
}

enum FavoriteHandType {
    gaming = 0; //游戏中
    history_record = 1; //战绩
}

//收藏牌谱
message RequestFavoriteHand {
    FavoriteHandType type = 1; //收藏来源
    string game_uuid = 2;
}

message ResponseFavoriteHand {
	int32 error = 1;
}
message LikeRequest {
	uint32 likeUid = 1; // 点赞玩家
}

message LikeResponse {
	int32 error = 1;        // 错误码
	uint32 likeUid = 2;     // 点赞的玩家id
	uint32 likedCount = 3;  // 点赞后的点赞数
}

message LikeNotice  {
	uint32 playerid = 4; // 点赞者用户id
	string nickname  = 5; //点赞者昵称
}

// 好友加入牌桌通知
message GoodFriendJoinTableNotify {
	uint32 playerid = 1;  // 加入牌桌的玩家id
	string nickname = 2;  // 加入牌桌的玩家昵称
	int32  intimacy = 3;  // 亲密度
	int32  seatid = 4;    // 加入牌桌的玩家的座位号
}

//互动表情类型(当前是否有免费机会)
enum EmojiType {
    Attack = 0;
    Welcome = 1;
    InterActiveNormal = 2; //互动表情中的普通表情
}
message RequestIsEmojiFree {
    EmojiType type = 1;
}

message ResponseIsEmojiFree {
	int32 error = 1;
}

message NoticeIsEmojiFree {
    EmojiType type = 1;
    bool is_free = 2;
}

// 亲密关系升级
message NoticeIntimacyUp {
	uint32 playerid = 1;
	string nickname = 2;
	int32 intimacy = 3; // 当前亲密度
	int32 way = 4;  	// 1 被升级(你已经成为xxxx的老铁), 2主动升级(xxxxx已经成为你的老铁)
}

message MikeModeReq {
	uint32 Mode = 1; // 0.按键 1.开放麦
}

message MikeModeRsp {
	int32 error = 1;        // 错误码
	uint32 Mode = 2; // 0.按键 1.开放麦
}

message VoicePrivateNotice {
	uint32 uid = 1;
	bool isVoicePublic = 2;  // false 关闭控制 仅其他明星和解说员能听到此明星发言
}

message CanSpeakNotice {
	uint32 uid = 1;
	bool CanSpeak = 2; // true 允许 false 关闭
}

message InviterSeatFreedNotice {
	repeated uint32 seatId = 1; // 哪些座位
	uint32 attr = 2; // 0.普通座位 1. 嘉宾位 2. 明星位
}

message StarCacheNotice {
	repeated uint32 playerId = 1; //玩家id
	uint32 attr = 2; // 0.普通玩家 1.特邀玩家 2.明星 3.解说员
	uint32 status = 3; // 解说员开闭麦或者明星的直播状态
	uint32 television = 4; // 解说员频道
}

message StarWillCloseNotice {
	uint32 noticeCd = 1; // 多少s之后关闭(单位s)
}

message RequestTip  {
	TipInfo tipInfo = 1;
	int32 playerId = 2;
}

message ResponseTip  {
	int32 error = 1;       // 错误码
	int64 costAmount = 2;  // 花费金额
	int64 time = 3;        // 生成时间
}

enum NewsType {  //消息
	NewsType_Default = 0 ;
	NewsType_System = 1;        // 系统消息
	NewsType_Tip  = 2; 	        // 礼物消息
	NewsType_PlayerDynamic = 3; // 玩家动态消息
	NewsType_Announcement = 4;  // 公告
	NewsType_Banner = 5;        // banner 变更通知
	NewsType_Backend = 6;       // 备用。
}

enum PlayerDynamicValue {
	PlayerDynamicValue_Default = 0;
	PlayerDynamicValue_JoinRoom = 1;
	PlayerDynamicValue_Sit = 2;
}

// 送礼物呀这些消息。
message NoticeRoomNews {
	repeated RoomNews news = 1; //
}


message PlayerAtomData {
	uint32 playerid = 1; // uid
	string avatar = 2;  // 头像地址
	string nickname = 3; //昵称
	uint32 plat = 4;
	uint32 identity = 5; // 玩家身份，0.普通 1.明星 2.解说员 3.特邀玩家
  bool isAdUser = 6; // 標註是否為廣告用
  string wpkSysAvatar = 7; // wpk 廣告取代頭像
}
message RoomNews {
	int32 roomid = 1; //
	NewsType newsType = 2;
	uint32  seq = 3;
	PlayerAtomData player = 4;
	PlayerAtomData toPlayer = 5;
	TipInfo  tip = 6;
	//	uint32 totalTip Count = 7;  // 该玩家一共打赏此物品的个数。
	repeated TipUserContr rankChangePlayers = 8;  //贡献有变更时会有值(玩家或者金额变更)
	string desc = 9;           // banner 或者 公告 或者其他
 	int64 time = 10;           // 生成时间
	PlayerDynamicValue dynamic = 11;        // 玩家动态，newsType为NewsType_PlayerDynamic时有效， 1进入房间，2入桌
//	string banner = 12; // banner 内容。
	int32 count5Second = 12;       // 5秒内的个数。5秒内连续发送+ 低级礼物不用这个字段
}

message UserTipInfo {
	PlayerAtomData player = 1;
	PlayerAtomData toPlayer = 2;
	TipInfo  tip = 3;
	int32 count5Second = 4;       // 5秒内的个数。5秒内连续发送+,高级礼物才有这个
}

// 玩家打赏贡献
message TipUserContr {
	PlayerAtomData player = 1;      // 玩家数据
	int64 contr = 3;                // 贡献金额
}

message RequestTipRank {
	uint32 topN = 1;     // 默认是10
	uint32 playerId = 2; // 看那个主播，默认是总计的，应该不用填。
}

message ResponseTipRank {
	int32 error = 1;       // 错误码
	repeated TipUserContr players = 2; // 降序排的。
}

enum TipRecordType {  //记录查询类型
	TipRecordType_Default = 0 ;
	TipRecordType_Send = 1;        // 1.发出
	TipRecordType_Recv  = 2; 	     // 2.收到
	TipRecordType_All = 3;				 // 3.全部
}


message RequestTipRecord {
	int32 skipId = 1; // 跳过id
	int32 size = 2;   // 查询多少条，
	TipRecordType tType = 3;  // 1 ：发出，2：收到，3 ：收到和发出都可以。
}

message ResponseTipRecord {
	int32 error = 1;        // 错误码
	int32 leftSize = 2;      // 剩余多少条。
	int32 skipId = 3;       // 查询时的跳过id
	int32 size = 4;         // 查询时的size
	repeated TipDetailInfo data = 5;
	TipRecordType tType = 6;
}

message TipDetailInfo {
	TipInfo  info = 1;
	int64 		   time = 2;  // 发送时间
	PlayerAtomData player = 3;
	PlayerAtomData toPlayer = 4;
	int32  id = 5;  //数据id
}

message TipInfo {
	int32 tipId = 1;
	int32 tipCount = 2;
}

message RequestSendBarrageForbidden {
	uint32 player = 1;
	int64 time = 2;     //禁多少时间。秒
	int32 tType = 3;    // 0:禁，1：解除禁止
}

message ResponseSendBarrageForbidden {
	int32 error = 1;
}

message NoticeSendBarrageForbidden {
	int64 time = 1;             // 禁止时间 秒
	int32 tType = 2;            // 0:禁，1：解除禁止
}

// 自定义弹幕配置更新通知
message NoticeSendBarrageConf {
	bool openCustomBarrage = 1;            // 是否打开自定义弹幕的开关，
	bool openTablePlayerCustomBarrage = 2; // 桌上玩家是否可以发送弹幕
	int64 nextCustomBarrageFee = 3;        // 自定义弹幕价格
}

message LuckStarSeatCountdownNotice  {
   uint32 left_time = 1; //抽奖剩余时间,单位秒
   int64 total_luck_amount = 2; //当前活动总抽奖金额
   string desc =3; //抽奖描述
   string title=4; //抽奖标题
   string share_image_url = 5;//红包分享图片
}

message LuckStarSeatCloseActiveNotice {
	uint32 room_id = 1;//房间id
}

message LuckStarSeatDrawResultNotice {
	uint32 user_id = 1;//中奖用户id
	uint32 amount = 2;//中奖金额
	bool is_help_wrap =3;//是否是助力包

}

message LuckStarSeatDrawResultItem {
    uint32 user_id	  = 1;//中奖用户id
	string nick_name  =2;//中奖都昵称
	uint32	amount    = 3;//中奖金额
	uint32   draw_time = 4;//抽奖时间
	bool is_high_light = 5;//是否需要高亮显示
	bool is_help_wrap = 6;//是助力红包
}

message GetLuckStarSeatDrawListRequest {
  uint32 room_id = 1;//房间id
}

message GetLuckStarSeatDrawListResponse {
   int32 error = 1;//错误码
   repeated LuckStarSeatDrawResultItem draw_result_list = 2;//中奖列表
   int32 total = 3;
   int32 page = 4;
}


message LuckSelfStarSeatResultItem {
	uint32	amount    = 1;//中奖金额
	uint32   draw_time = 2;//抽奖时间
	bool is_help_wrap = 3;//是助力红包
	int32  state =4;//0-待领取 1-已领取
}

message GetSelfLuckStarSeatResultListRequest {
   uint32 room_id = 1;//房间id
}

message GetSelfLuckStarSeatResultListResponse {
   int32 error = 1;//错误码
   repeated LuckSelfStarSeatResultItem self_result_list = 2;//自已中奖列表
}

message TipForbiddenNotice {
	int32 forbidden = 1; // 0: 禁 1:解除禁止
}

message C2CPaymentBetBlockNotice{
	string billno = 1;
	int64 time = 2;
}

message MagicEmojiRequest {
	int32 roomid = 1;
	int32 type = 2;
}

message MagicEmojiResponse {
	int32 error = 1; // Error_OK = 1, Error_Player_Not_Found = 3, Error_Short_Of_Money = 53 ,Error_Not_Allow = 113
}

message MagicEmojiNotice {
	int32 roomid = 1;
	uint32 playerid = 2;
	int32 type = 3;
}

message DynamicConfig {
	bool magicEmojiEnable = 1;
	int64 magicEmojiDuration = 2; // CD time
	int64 magicEmojiPotBB = 3;    // if 0 not limit, if pot > X pot_bb not allow to send magic emoji
}

// SQUID GAME
message SquidHuntGameParams {
    int32 minPlayersToStartSquidHunt = 1; // min amount of players required to start a squid hunt game
    int32 timeoutToRebuySec = 2; // seconds all players have to decide to either re buyin or not
    map<uint32,uint32> squidValue = 3; // key : Get the number of squids, value : Gold coins paid by a single player
    map<uint32,uint32> noOfPlayerMapNofSquid = 4; // key : The number of participants, value : Number of squid
    uint32 superSquidNum = 5; // super squid , random assign 1-N squid , never assign to first squid
    uint32 forceShowCardNum = 6; // random show  hand card ,which may win after secsond squid
	uint32 blockSitDownMins = 7; // the no. of minutes player need to wait before able to sit again after continously opting out
  	uint32 gracePeriodMins = 8; // the grace period for the player who are sitout (due to disconnection and others) to re-enter game
  	SquidHuntGameMode mode = 9; // squid hunt game mode
  	bool isFirstRoundDoubleSquid = 10; // whether double the first round winning
  	map<uint32,Multiplier> squidMultiplier = 11; // squid multiplier map
}

message SquidHuntGameInfo{
    repeated uint32 registeredPlayers = 1; // list of player uids that have bought in escrow
    repeated uint32 unRegisteredPlayers = 2; // list of player uids that have not buy in escrow
    bool isStarted = 3; // is the squid game started
    bool isReturningSquidGamePlayer = 4; //?(ask PM 重新入桌的條件)
    int64 rebuyInTimeoutSec = 5; //?(ask PM)
    repeated uint32 satOutUntilEndOfSquidGamePlayers = 6; // the players who joined mid squidgame and chose to sitout
    map<uint32, uint32> winSquidPlayer = 7; // key : user id, value : number of squid the user won
    map<uint32, uint32> winSuperSquidPlayer = 8; // key : user id, value : number of super squid the user won
    bool isSuperSquidRound = 9; // tirgger super squid anmtaion (true: wait 5 sec to play animation)
    uint32 remainingSquidCounts = 10; // the number of remaining squid counts that are available for taking
    uint32 totalSquidCounts = 11; // the number of total squid counts
    uint32 totalDeposit = 12;  // amount of total freeze deposit
    uint32 remainingDeposit = 13; // amount of remaining freeze deposit
    uint32 totalForceShowCardCounts = 14; // amount of total times player has asked to show cards
    uint32 remainingForceShowCardCounts = 15; // amount of remaining times player has asked to show cards
	int64 gameStartTime = 16; // unit time to game start
	repeated SquidHuntGamePlayer squidHuntGamePlayers = 17;
	bool isFirstRound = 18;
}

message SquidHuntGameSettlement{
    uint32 remainingSquidCounts = 1; // the number of remaining squid counts that are available for taking
    map<uint32, uint32> winSquidPlayer = 2; // the number of squid currently held by each player
    map<uint32, uint32> winSuperSquidPlayer = 3; // the number of super squid currently held by each player
    bool isSuperSquidRound = 4; // is this squid a super squid?
    uint32 forceShowCardCounts = 5; // next game show card counts
    uint32 whoGetSquid = 6; // which player get squid this round
	uint32 getNormalSquidsNo = 7; // the number of normal squids won by the player specified in whoGetSquid
    uint32 getSuperSquidsNo = 8;  // the number of super squids won by the player specified in whoGetSquid
    uint32 remainingNormalSquidsCount = 9;
    uint32 remainingSuperSquidsCount = 10;
    uint32 superSquidMultiple = 11;
    map<uint32,uint32> winNormalSquidPlayer = 12;
    uint32 getTotalSquidsNo = 13; // the number of normal squids + super squids * multiplier won by the player specified in whoGetSquid
}

enum SquidHuntRefundType {
    UserNumebrChanged = 0; // 0. If the user is less than the maximum number, part of the deposit will be refunded
    WinFirstSquid = 1; // 1. The winner will get all the deposit back when he wins for the first time
    SettleWin = 2; // 2. The winner of the settlement gets
    LoserRefund = 3; // 3. The loser returns the deposit
}

message SquidHuntSettlement{
    uint32 remainingSquidCounts = 1; // the number of remaining squid counts that are available for taking
    map<uint32, uint32> winSquidPlayer = 2; // the number of squid currently held by each player
    map<uint32, uint32> winSuperSquidPlayer = 3; // the number of super squid currently held by each player
    bool isSuperSquidRound = 4; // is this squid a super squid?
    uint32 forceShowCardCounts = 5; // next game show card counts
    uint32 whoGetSquid = 6; // which player get squid this round
}

message SquidLeaderBoard {
    uint32 userid = 1;
    string nickname = 2;
    string avatar = 3;
    uint32 squidNumber = 4;
    int64 winAmount = 5;
    int64 refund = 6;
    uint32 plat = 7;
    int64 winAmountFromEachLoser = 8;
    string wpkSysAvatar = 9; // wpk 廣告取代頭像
    bool isAdUser = 10; // 標註是否為廣告用
}

message NoticeRegisterSquidHuntGame{
  int32 roomid = 1;
  int32 needFreezeDeposit = 2; // Required deposit amount (cent)
  SquidHuntGameParams roomParams = 3; // Bloody Squid room parameters
  int64 gameStartTime = 4; // unix time to game start (server time)
  int64 serverTime = 5; // the unix current server time
}

message RequestJoinSquidHuntGame{
  int32 roomid = 1;
  bool isApply = 2; // true : join register list, false: leave squid hunt game
}

message ResponseJoinSquidHuntGame{
  int32 error = 1;
  int64 serverTime = 2;
  int64 gameStartTime = 3;
}

message NoticeJoinSquidHuntGame{
  int32 roomid = 1;
  uint32 whoSit = 2;
  SquidHuntGameInfo info = 3;
}

message NoticeSquidHuntRefund{
  int32 roomid = 1;
  uint64 amount = 2;
  SquidHuntRefundType refundType = 3;
}

message NoticeStartSquidHuntGame {
  int32 roomid = 1;
  SquidHuntGameInfo info = 2;
}

message NoticeStartSquidHuntGameFailed {
  int32 roomid = 1;
}

message SquidHuntGameFinalSettlement{
  int32 roomid = 1;
  repeated SquidLeaderBoard players = 2;
  uint32 noOfLosers = 3;
}

message NoticeWaitingOtherPlayerRebuyIn{
	int32 roomid = 1;
	repeated uint32 players = 2; // players who are currently rebuying in
	int64 rebuyInTimeoutTime = 4; // unix time to rebuy in by
    int64 serverTime = 5; // the unix current server time
}

message NoticeStartSquidHuntGameGracePeriod {
  int32 roomid = 1;
  int64 gracePeriodTimeoutTime = 2;
  int64 serverTime = 3;
  uint32 lastSitPlayerUid = 4;
  bool isLastPlayerWinSquid = 5;
}

message SquidHuntGamePlayer {
 	  uint32 uid = 1;
    uint32 totalSquidsWon = 2;
    string nickname = 3;
    string avatar = 4;
    uint32 platform = 5;
    bool isAway = 6;
    string wpkSysAvatar = 7; // wpk 廣告取代頭像
    bool isAdUser = 8; // 標註是否為廣告用
}

enum SquidHuntGameMode {
  	NORMAL_MODE = 0; // normal squidhunt game mode
  	MULTIPLIER_MODE = 1; // multiplier squidhunt game mode
}
message Multiplier{
  	uint32 multiplier = 1;
  	bool isDisplay = 2;
}

// End Squid region

// region: Barrage records region
message RequestBarrageHistories {
    int32 roomid = 1; // 房间ID
}


message ResponseBarrageHistories {
    int32 error = 1; // 错误码
    repeated Barrage barrages = 2; // <返回的彈幕歷史記錄></返回的彈幕歷史記錄>
    TableBarrageMessage tableBarrageMessage = 3; // 桌面弹幕
}

message TableBarrageMessage {
    int64 send_time = 1;
    LocalizedContent content = 2;
}

message LocalizedContent {
    string zh_CN = 1;  // 简体中文
    string en_US = 2;  // 英语
    string yn_TH = 3;  // 泰语
    string th_PH = 4;  // 菲律宾语
    string ar_SA = 5;  // 阿拉伯语
    string hi_IN = 6;  // 印地语
}

// 彈幕結構
message Barrage {
    int32 roomid = 1; // 房间ID
    uint32 playerid = 2; // 發送彈幕的玩家ID
    string nickname = 3; // 發送者的暱稱
    string avatar = 4; // 發送者的頭像
    string content = 5; // 彈幕的內容
    BarrageType ctype = 6; // 彈幕類型
    int64 send_time = 7; // 發送時間（Unix 時間戳）
    repeated uint32 at_uid_list = 8; // @ 的玩家ID列表
    int32 thump_up_status = 9; // 点赞状态
    repeated string at_list = 10; // @ 的玩家暱稱列表
    uint32 plat = 11; // 平台
    string wpkSysAvatar = 12; // wpk 廣告取代頭像
    bool isAdUser = 13; // 標註是否為廣告用
}
// endregion
message GeoComplyGenerateTokenNotification {
  bool generate_now = 1;
  bool auto_generate_by_client = 2;
  bool bypass = 3;
}


// peek card start

// a notice to notify all players that
// they can now peek card once game enters
// river
//可以開始眯牌 收到服務端通知
message PeekCardNotice{
	CardItem cardItem = 1;
	int64 timeout = 2;
	bool needPeek = 3;
  }

  // an empty request message when a
  // player decides to peek card
  //客戶端眯牌完成通知服務端
  message PeekCardRequest{
  }

  // a response message when a
  // player decides to peek card
  //眯牌完成返回, 回error 錯誤操作之類的, 可以不用做事
  message PeekCardResponse{
	int32 error = 1;
  }

  // a notice when all player has decided to
  // peek card or it has timeout
  //所有玩家眯牌完成, 服務端通知
  message PeekCardFinishNotice{
  }

  //peek card end
