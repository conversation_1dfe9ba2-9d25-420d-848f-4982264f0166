syntax = "proto3";

package pb;

//message header definition
//uint16 		| uint16 | int32  | int32   | int32   |  package_len - 16 |
//package_len   | msgid  | seq    | playerid | roomid | payload           |

//package_len : 一个消息包的长度
//msgid: 消息的id
//seq:  请求消息的编号（同一个tcp链接建立之后，消息id从0开始，每次发送一个请求编号+1）
//playerid: 玩家的uid
//roomid: 房间号的uid
//payload:  消息内容（protobuf打包序列化之后的二进制内容放到这里）

//消息id枚举定义

enum MSGID {
	MsgID_Min = 0;
	//server inner proto msgid
	MsgID_ConnClose_Notice = 10;

	MsgID_DupLogin_Notice = 99; // 被挤下线通知

	//server inner proto end
	MsgID_Logon_Request = 10000;
	MsgID_Logon_Response = 10001;
	
	MsgID_CreateClub_Request = 30010;
	MsgID_CreateClub_Response = 30011;
	MsgID_ClubSnapshotList_Request = 30012;
	MsgID_ClubSnapshotList_Response = 30013;
	MsgID_ClubSnapshotList_Notice = 30014;

	MsgID_JoinClub_Request = 30015;//玩家申请加入俱乐部（clientnotadmin->world）
	MsgID_JoinClub_Notice_To_Member = 30016;//请求批准俱乐部（反馈）
	MsgID_JoinClub_Notice = 30017; //world--> 管理员或创建者
	MsgID_JoinClub_Reply = 30018; //管理员或创建者户端-->world 请求批准/拒绝
	MsgID_JoinClub_Response_To_Member = 30019;//请求批准俱乐部（反馈）
	MsgID_JoinClub_Response_To_Admin = 30020;//请求批准俱乐部（反馈给管理员）

	MsgID_LeaveClub_Request = 30021;
	MsgID_LeaveClub_Response = 30022;
	
	MsgID_ClubCurrentBoard_Request = 30023;
	MsgID_ClubCurrentBoard_Response = 30024;
	MsgID_ClubCurrentBoard_Notice = 30025;
	
	MsgID_ClubMemberSnapshotList_Request = 30026;
	MsgID_ClubMemberSnapshotList_Response = 30027;
	MsgID_ClubMemberSnapshotList_Notice = 30028;
	
	MsgID_ModifyClubMember_Request = 30029;
	MsgID_ModifyClubMember_Response = 30030;
	MsgID_ModifyClubMember_Notice = 30031;
	
	MsgID_ModifyClubInfo_Request = 30032;
	MsgID_ModifyClubInfo_Response = 30033;

	MsgID_BoardVisibleSwitch_Request = 30034;
	MsgID_BoardVisibleSwitch_Response = 30035;
	
	MsgID_GrantClubFund_Request = 30036;
	MsgID_GrantClubFund_Response = 30037;
	
	MsgID_SearchClubInfo_Request = 30038;
	MsgID_SearchClubInfo_Response = 30039;
	MsgID_SearchClubInfo_Notice = 30040;
	
	MsgID_ClubCreaterInfo_Request = 30041;
	MsgID_ClubCreaterInfo_Response = 30042;
	MsgID_ClubCreaterInfo_Notice = 30043;
	
	MsgID_SendMsg_Request = 30047;
	MsgID_SendMsg_Response = 30048;
	MsgID_SendMsg_Notice = 30049;
	
	MsgID_GetUserData_Request = 30050;
	MsgID_GetUserData_Response = 30051;
	MsgID_GetUserData_Notice = 30052;
	
	MsgID_HeartBeat_Request = 30053;
	MsgID_HeartBeat_Response = 30054;
	
	

	MsgID_GetJackpotData_Request = 30055;//请求彩池基础信息
	MsgID_GetJackpotData_Response = 30056;
	MsgID_GetJackpotData_Notice = 30057;
	
	MsgID_JackpotSetting_Request = 30058;//请求当前俱乐部彩池设置信息
	MsgID_JackpotSetting_Response = 30059;
	MsgID_JackpotSetting_Notice = 30060;
	
	MsgID_SetJackpot_Request = 30061;//请求设置彩池信息
	MsgID_SetJackpot_Response = 30062;
	MsgID_SetJackpot_Notice = 30063;
	
	MsgID_RecoverJackpotSetting_Request = 30064;//恢复默认设置
	MsgID_RecoverJackpotSetting_Response = 30065;
	
	MsgID_JackpotAmout_Notice = 30066;//广播推送奖池金额
	
	MsgID_CurrentRoomJackpot_Request = 30067;//请求当前俱乐部牌局盲注级别奖池信息
	MsgID_CurrentRoomJackpot_Response = 30068;
	MsgID_CurrentRoomJackpot_Notice = 30069;
	
	MsgID_JackpotAwardRecord_Request = 30070;//当前俱乐部奖池获奖历史记录
	MsgID_JackpotAwardRecord_Response = 30071;
	MsgID_JackpotAwardRecord_Notice = 30072;
	
	MsgID_JackpotInjectAmount_Request = 30073;//俱乐部管理员向jackpot注入金额
	MsgID_JackpotInjectAmount_Response = 30074;
	MsgID_JackpotInjectAmount_Notice = 30075;
	
	MsgID_JackPotAwardInfo_Notice = 30079; //推送击中牌形消息
	
	MsgID_CreateAlliance_Request = 30080;
	MsgID_CreateAlliance_Response = 30081;
	
	MsgID_LeaveAlliance_Request = 30082;
	MsgID_LeaveAlliance_Response = 30083;
	
	MsgID_SearchAlliance_Request = 30084;
	MsgID_SearchAlliance_Response = 30085;
	MsgID_SearchAlliance_Notice = 30086;
	
	MsgID_KickoffAllianceMember_Request = 30087;
	MsgID_KickoffAllianceMember_Response = 30088;
	MsgID_KickoffAllianceMember_Notice = 30089;
	
	MsgID_AllianceList_Request = 30090;
	MsgID_AllianceList_Response = 30091;
	MsgID_AllianceList_Notice = 30092;
	
	
	MsgID_JoinAlliance_Request = 30093;//俱乐部申请加入联盟（clientnotadmin->world）
	MsgID_JoinAlliance_Notice_To_Member = 30094;//请求批准俱乐部（反馈）
	MsgID_JoinAlliance_Notice_To_Admin = 30095; //--> 管理员或创建者
	MsgID_JoinAllianceReply_To_World = 30096; //管理员或创建者户端-->world 请求批准/拒绝
	MsgID_JoinAlliance_Response_To_Member = 30097;//请求批准俱乐部（反馈）
	MsgID_JoinAlliance_Response_To_Admin = 30098;//请求批准俱乐部（反馈给管理员）

	MsgID_AddRemarks_Request = 30099; //增加备注
	MsgID_AddRemarks_Response = 30100;
	MsgID_AddRemarks_Notice = 30101;

	MsgID_GetAllRemarks_Request = 30102; //获取备注
	MsgID_GetAllRemarks_Response = 30103;
	MsgID_GetAllRemarks_Notice = 30104;
	
	MsgID_LeaveAlliance_Notice  = 30105; //退出联盟通知盟主
	
	MsgID_ClearAllianceMaxBuyinLimit_Request = 30106; //清除联盟买入额度上限
	MsgID_ClearAllianceMaxBuyinLimit_Response = 30107;
	
	MsgID_SetAllianceMaxBuyinLimit_Request = 30108; //设置联盟买入额度上限
	MsgID_SetAllianceMaxBuyinLimit_Response = 30109;
	
	MsgID_SetAllianceControlBuyin_Request = 30110; //设置联盟买入额度控制
	MsgID_SetAllianceControlBuyin_Response = 30111;

	MsgID_FairPlay_Report_Request = 30112; //fairplay report
	MsgID_FairPlay_Report_Response = 30113;
  MsgID_ModifyRemark_Request = 30114;
  MsgID_ModifyRemark_Response = 30115;

	MsgID_DeviceInfo_Report_Request = 30120; //设备信息上报接口
	MsgID_DeviceInfo_Report_Response = 30121;

	MsgID_ClubGrantFund_Notice = 30122;		//通知俱乐部转账信息
	
	MsgID_GetIncome_Request = 30123;//请求收益信息
	MsgID_GetIncome_Response = 30124;
	MsgID_GetIncome_Notice = 30125;

	MsgID_GetUserClubGrantInfo_Request = 30126;	//请求玩家俱乐部转账信息
	MsgID_GetUserClubGrantInfo_Response = 30127;
	MsgID_GetUserClubGrantInfo_Notice = 30128;

	MsgID_NotifyUserGoldNum_Notice = 30129;		//通知客户端金币变化

	MsgID_GetUserMailListInfo_Request = 30130;	//客户端请求玩家邮件列表
	MsgID_GetUserMailListInfo_Response = 30131;
	MsgID_GetUserMailListInfo_Notice = 30132;

	MsgID_ReadAndFetchOneMail_Request = 30133;		//客户端阅读领取邮件
	MsgID_ReadAndFetchOneMail_Response = 30134;		
	MsgID_ReadAndFetchOneMail_Notice = 30135;

	MsgID_NotifyUserMailNum = 30136;		//通知客户端当前邮件和公告数量

	MsgID_NoticeCreateClub = 30137;			//通知客户端创建或解散俱乐部

	MsgID_RequestAnounceList = 30138;		//客户端请求公告列表
	MsgID_ResponseAnounceList = 30139;		
	MsgID_NoticeAnounceList = 30140;		
	
	MsgID_NoticeOneAnounce = 30141;		//通知客户端新的一条公告
	
	MsgID_NoticeCreateAlliance = 30142;	//通知联盟创建/解散结果

    MsgID_AddCoinOrder_Pay_Request  = 30143;     //加币订单请求
    MsgID_AddCoinOrder_Pay_Response = 30144;     //加币订单请求回复
    MsgID_AddCoinResult_Pay_Notice  = 30145;     //加币结果回复

    MsgID_DelCoinOrder_Pay_Request  = 30146;     //减币订单请求
    MsgID_DelCoinOrder_Pay_Response = 30147;     //减币订单请求回复
    MsgID_DelCoinResult_Pay_Notice  = 30148;     //减币结果回复

    MsgID_SearchClubMember_Request = 30149;
    MsgID_SearchClubMember_Response = 30150;
	MsgID_SearchClubMember_Notice = 30151;
	
	MsgID_ReadAndFetchOneAnounce_Request = 30152;		//客户端阅读领取公告
	MsgID_ReadAndFetchOneAnounce_Response = 30153;
	MsgID_ReadAndFetchOneAnounce_Notice = 30154;

	MsgID_NoticeOneMail = 30155;		//通知客户端新的一条邮件

	MsgID_NoticeWithdrawMail = 30156;			//撤销一封邮件
	MsgID_NoticeWithdrawAnounce = 30157;		//撤销一封公告

    MsgID_SetClubInvitePercent_Request = 30158; // 设置俱乐部个人百分比
    MsgID_SetClubInvitePercent_Response = 30159;

	MsgID_AutoAgreeClubReply_Request = 30160; // 是否开启自动同意俱乐部申请
	MsgID_AutoAgreeClubReply_Response = 30161;
	MsgID_AutoAgreeClubReply_Notice = 30162;

	MsgID_QuerySendFairReport_Request = 30163;	//查询举报是否免费
	MsgID_QuerySendFairReport_Response = 30164;

	MsgID_Login_Notice	= 30165;		//登录通知 当前的游戏状态

	MsgID_GetWebToken_Request = 30166;
	MsgID_GetWebToken_Response = 30167;

    MsgID_CowBoy_List_Request = 30168;   // 德州牛仔房间列表
    MsgID_CowBoy_List_Response = 30169;

    MsgID_GlobalMessage_Notice = 30170;

	MsgID_GameStatus_Request = 30171;
	MsgID_GameStatus_Response = 30172;

	MsgID_HumanBoy_List_Request = 30173; // 百人德州列表
	MsgID_HumanBoy_List_Response = 30174;

    MsgID_DepositInStrongbox_Request = 30175; // 存金币入保险箱
    MsgID_DepositInStrongbox_Response = 30176;

    MsgID_TakeoutStrongbox_Request = 30178; // 从保险箱中取金币
    MsgID_TakeoutStrongbox_Response = 30179;


    MsgID_StrongboxDetail_Request = 30181; // 保险箱明细
    MsgID_StrongboxDetail_Response = 30182;


    MsgID_GetStrongboxInfo_Request = 30184; // 获取保险箱信息
    MsgID_GetStrongboxInfo_Response = 30185;


    MsgID_Luck_Draw_Done_Request = 30187; // 拉霸游戏结果通知
    MsgID_Luck_Draw_Done_Response = 30188; // 拉霸游戏结果通知回复
    MsgID_Luck_Draw_Notice = 30189; // 拉霸游戏信息推送

    MsgID_Aof_JackPot_List_Request = 30190;  //jackpot全列表请求
    MsgID_Aof_JackPot_List_Response = 30191; //jackpot全列表回复

    MsgID_Aof_Thouthand_Request = 30192; // aof千手数据
    MsgID_Aof_Thouthand_response = 30193;

    MsgID_CheckSafe_Request = 30194; // 检查用户二级密码
    MsgID_CheckSafe_response = 30195;

    MsgID_Luck_Turntable_Draw_Notice = 30196;  //通知客服端转盘开始抽奖
    MsgID_Luck_Turntable_Result_Request = 30197;//客户端转盘抽奖结果请求
    MsgID_Luck_Turntable_Result_Response = 30198;///服务端转盘抽奖结果应答
    MsgID_Luck_Turntable_Ready_Notice = 30199; //抽奖活动结束倒计时
    MsgID_Luck_Turntable_Over_Notice = 30200; //转盘活动结束给客户端的通知
	MsgID_Luck_Turntable_StartTime_Notice = 30201;//转盘活动开始时间相关信息通知
	MsgID_Luck_Turntable_EndTime_Notice = 30202;// //转盘活动结束时间相关信息通知
	MsgID_Luck_Turntable_Snaplist_Request = 30203; // 抽奖列表请求
	MsgID_Luck_Turntable_Snaplist_Response = 30204;//抽奖列表回复
	MsgID_Luck_Turntable_Snaplist_Notice = 30205; //抽奖列表通知
	MsgID_Luck_Turntable_Countdown_Notice = 30206; //抽奖即将开始倒计时
	MsgID_Luck_Turntable_Result_Notice = 30207;  //服务端转盘抽奖结果本房间通知

	MsgID_GameStatusV2_Request = 30211;//获取游戏维护状态的2版本
	MsgID_GameStatusV2_Response = 30212;

	MsgID_ClubCurrentBoardV2_Request = 30213;
	MsgID_ClubCurrentBoardV2_Response = 30214;
	MsgID_ClubCurrentBoardV2_Notice = 30215;
  
  MsgID_ClubCurrentBoardV3_Request = 30216;
  MsgID_ClubCurrentBoardV3_Response = 30217;
  MsgID_ClubCurrentBoardV3_Notice = 30218;
  MsgID_ClubCurrentBoardPlayerCountV1_Request = 30219;
  MsgID_ClubCurrentBoardPlayerCountV1_Response = 30220;

	MsgID_Banner_Request = 30300;  // 请求banner
	MsgID_Banner_Response = 30301; // banner返回

	MsgID_ZoomPlayerSettle_Notice = 30302; // 通知玩家结算信息

	MsgID_SetThisAreaPlayer_Notice = 30303;
	
    MsgID_Create_RedBag_Request = 30400;  //创建红包
    MsgID_Create_RedBag_Response = 30401; //创建红包回复
    MsgID_RedBag_Notice = 30402;          //红包推送
    MsgID_RedBag_Info_Request = 30403;    //红包信息请求
    MsgID_RedBag_Info_Response = 30404;   //红包信息回复
    MsgID_RedBag_Set_Amount_Request = 30405;  //红包金额级别设置
    MsgID_RedBag_Set_Amount_Response = 30406; //红包金额级别设置回复
    MsgID_RedBag_Draw_Request = 30407;        //红包抽奖请求
    MsgID_RedBag_Draw_Response = 30408;       //红包抽奖结果回复
    MsgID_RedBag_Open_Notice = 30409;         //红包活动开启关闭通知
    MsgID_RedBag_History_Request = 30410;     //红包历史记录请求
    MsgID_RedBag_History_Response = 30411;    //红包历史记录回复
    MsgID_RedBag_Boom2Creater_Notify = 30412; //红包中雷推送红包主
    MsgID_RedBag_Status_Request = 30413;      //红包状态请求
    MsgID_RedBag_Status_Response = 30414;     //红包状态回复
	MsgID_RedBag_AutoDraw_Request = 30415;    // 自动抢红包请求
	MsgID_RedBag_AutoDraw_Response = 30416;    // 自动抢红包返回
	MsgID_RedBag_Drawed2Creator_Notice = 30417; // 红包被人抢中返回消息给发红包的人
	MsgID_RedBag_LastInfo_Request = 30418;  // 最近的自己发的和抽过的红包请求
	MsgID_RedBag_LastInfo_Response = 30419; // 最近的自己发的和抽过的红包返回
	MsgID_RedBag_JackpotInfo_Request = 30420; // 红包Jackpot信息请求
	MsgID_RedBag_JackpotInfo_Response = 30421; // 红包Jackpot信息返回
	MsgID_RedBag_StatisticsInfo_Request = 30422; // 红包统计信息请求
	MsgID_RedBag_StatisticsInfo_Response = 30423; // 红包统计信息请求
	MsgID_RedBag_JackpotUpdate_Notice	 = 30424; // 红包Jackpot刷新推送

	MsgID_Create_RedBagM_Request              = 30440;  //创建红包
    MsgID_Create_RedBagM_Response              = 30441; //创建红包回复
    MsgID_RedBagM_Notice                       = 30442; //红包推送
    MsgID_RedBagM_Info_Request                 = 30443; //红包信息请求
    MsgID_RedBagM_Info_Response                = 30444;  //红包抽奖结果回复
    MsgID_RedBagM_Set_Amount_Request           = 30445;  //红包金额级别设置
    MsgID_RedBagM_Set_Amount_Response          = 30446; //红包金额级别设置回复
    MsgID_RedBagM_Draw_Request                 = 30447; //红包抽奖请求
    MsgID_RedBagM_Draw_Response                = 30448; //红包抽奖结果回复
    MsgID_RedBagM_History_Request              = 30449; //红包历史记录请求
    MsgID_RedBagM_History_Response             = 30450; //红包历史记录回复
    MsgID_RedBagM_Boom2Creater_Notify          = 30451; //红包中雷推送红包主
    MsgID_RedBagM_Status_Request               = 30452; //红包状态请求
    MsgID_RedBagM_Status_Response              = 30453; //红包状态回复
    MsgID_RedBagM_Drawed2Creator_Notice        = 30454; // 红包被人抢中返回消息给发红包的人
    MsgID_RedBagM_LastInfo_Request             = 30455; // 最近的自己发的和抽过的红包请求
    MsgID_RedBagM_LastInfo_Response            = 30456; // 最近的自己发的和抽过的红包返回
    MsgID_RedBagM_StatisticsInfo_Request       = 30457; // 红包统计信息请求
    MsgID_RedBagM_StatisticsInfo_Response      = 30458; // 红包统计信息请求
	
    MsgID_RedBagM_ShowUI_Notify                = 30459; // 发红包UI显示通知

	MsgID_VideoCowboy_List_Request = 30480;   // 视频牛仔房间列表
    MsgID_VideoCowboy_List_Response = 30481;

	MsgID_AutoExchange_Notice = 30482;

	// 35000 -转发消息
	MsgID_GetRank_Request = 35001;       // 获取荣誉榜请求
	MsgID_GetRank_Response = 35002;      // 获取荣誉榜返回
	
	MsgID_SetSecretKey_Request = 35101;   //设置数据加密秘钥请求
	MsgID_SetSecretKey_Response =  35102;//设置数据加密秘钥回复
	
	MsgID_SetSecretKeyEx_Request = 35103;   //ECDH设置数据加密秘钥请求
	MsgID_SetSecretKeyEx_Response =  35104;//ECDH设置数据加密秘钥回复


	MsgID_Referrals_Request = 35110; // 获取推广明细
	MsgID_Referrals_Response = 35111; // 返回推广明细

    //邀请页签消息
    MsgID_InviteSummary_Request = 35201;   		//邀请页签数据请求
	MsgID_InviteSummary_Response =  35202; 		//邀请页签数据回复
	MsgID_InviteIncomeRedeem_Request = 35203;   //邀请收益领取请求
    MsgID_InviteIncomeRedeem_Response =  35204; //邀请收益领取回复


	MsgID_JoinAlliance_UserCancel_Request = 35301;  //用户取消申请加入公会请求
	MsgID_JoinAlliance_UserCancel_Response = 35302;  //用户取消申请加入公会回复

	MsgID_PokerMaster_List_Request = 35205;   // 德州牛仔房间列表
	MsgID_PokerMaster_List_Response = 35206;
    MsgID_MiniGames_List_Request = 35207; // 小游戏房间列表
	MsgID_MiniGames_List_Response = 35208;
	MsgID_MiniGamesAtmosphereNotice = 35209; // 小遊戲氣氛消息
	MsgID_MiniGamesAtmosphereRequest = 35211; // 小遊戲氣氛消息
	MsgID_MiniGamesAtmosphereResponse = 35212; // 小遊戲氣氛消息
	
    //部落相关消息
    MsgID_AuthApi_Request =  35401; //部落登录
    MsgID_AuthApi_Notice =  35402; //
    MsgID_AuthApi_Response =  35403; //

    MsgID_GameMaintainStatus_Notice = 35404; // 通知游戏维护状态

    MsgID_MttResult_Request =  35405; //战绩列表
    MsgID_MttResult_Notice =  35406; //
    MsgID_MttResult_Response =  35407; //

    MsgID_MttDetail_Request =  35408; //战绩详情
    MsgID_MttDetail_Notice =  35409; //
    MsgID_MttDetail_Response =  35410; //

    MsgID_MttGameSum_Request =  35411; // 数据统计
    MsgID_MttGameSum_Notice =  35412; //
    MsgID_MttGameSum_Response =  35413; //


	//Spin n Go game records related msg
	MsgID_SpinResult_Request = 35416; // spin战绩列表
	MsgID_SpinResult_Notice = 35417;
	MsgID_SpinResult_Response = 35418;
	MsgID_SpinDetail_Request = 35419; // spin战绩详情
	MsgID_SpinDetail_Notice = 35420;
	MsgID_SpinDetail_Response = 35421;
	MsgID_SpinGameSum_Request =  35422; // Spin数据统计
	MsgID_SpinGameSum_Notice = 35423;
	MsgID_SpinGameSum_Response = 35424;

    MsgID_EventReport_Request = 35414; // 玩家操作做记录详情
    MsgID_EventReport_Response = 35415;

	MsgID_Exchange_UserPoints_Request = 35450; //用户兑换积分请求
	MsgID_Exchange_UserPoints_Response = 35451; //用户兑换积分应答
	
	MsgID_Goods_List_Request = 35452;   //商品兑换列表请求
	MsgID_Goods_List_Response =35453;   //商品兑换列表应答
	
	MsgID_Bank_Details_Query_Request = 35454;   //银行流水查询
	MsgID_Bank_Details_Query_Response =35455;   //银行流水应答

	MsgID_StarInfo_Request = 35458; // 明星信息
	MsgID_StarInfo_Response = 35459;


	MsgID_ReceiveTools_Request = 35460; // 领取道具(门票)
	MsgID_ReceiveTools_Response = 35461;
	MsgID_ReceiveTools_Notice = 35462; // 通知玩家领取道具(门票)

	MsgID_Get_Scaler_Quote_Request = 35463; //获取货币汇率请求
	MsgID_Get_Scaler_Quote_Response = 35464; //获取货币汇率回复

	MsgID_Exchange_Currency_Request = 35465; //用户兑换货币请求
	MsgID_Exchange_Currency_Response = 35466; //用户兑换货币应答

	MsgID_GetUserMarks_Request = 35467;  // 获取玩家个性签名
	MsgID_GetUserMarks_Response = 35468;

	MsgID_AuthVerify_Request = 35469; // 真人验证
	MsgID_AuthVerify_Response = 35470;

	MsgID_UpdateUserMarks_Request = 35471; // 修改个性签名
	MsgID_UpdateUserMarks_Reponse = 35472;
	
	MsgID_BuyinEvent_UsdtChanage_Notice = 35473;		//买入时有usdt变化时发送的通知

	MsgID_QuickRaise_Request = 35474; // 快捷加注
	MsgID_QuickRaise_Response = 35475;

	MsgID_DefaultSetting_Request = 35576; // 快捷加注恢复默认设置
	MsgID_DefaultSetting_Response = 35577;

	MsgID_StarAllow_Request = 35578; // 明星桌是否放行
	MsgID_StarAllow_Response = 35579;

	MsgID_StarWillBegin_Notice = 35580; // 明星桌即将开始游戏
	
	MsgID_UsdtExchange_Config_Notice = 35581; //usdt兑换配置改变通知
	MsgID_GetUsdtExchange_Config_Request = 35582; // 获取usdt兑换配置
	MsgID_GetUsdtExchange_Config_Response = 35583;//获取usdt兑换应答

	MsgID_AddHelpWrap_Notice = 35600; // 用户获得新助力包通知 

	MsgID_GetUserHelpWarpList_Request = 35601; // 获取助力红包列表请求
	MsgID_GetUserHelpWarpList_Response = 35602; //// 获取助力红包列表回复
	
	MsgID_LeftHelpCountChange_Notice =35603;
	
	
	MsgID_AddHelper_Request = 35605; // 根据助力码助力玩家请求
	MsgID_AddHelper_Response = 35606;// 根据助力码助力玩家回复(助力成功返回MsgID_UserHelpWarpChange_Notice通知)
	
	MsgID_GetTotalHistoryAmount_Request = 35607; //获获近7天总领取金额请求
	MsgID_GetTotalHistoryAmount_Response = 35608; //获获近7天总领取金额回复
	
	MsgID_UserReceiveHelpWarp_Request = 35609; // 用户领取助力红包请求
	MsgID_UserReceiveHelpWarp_Response = 35610; //用户领取助力红包回复
	
	
	MsgID_UserHelpWarpChange_Notice = 35611;// 根据助力码助力玩家回复(助力成功返回通知)

	MsgID_UserChangeLanguage_Request = 35621;//用户切换语言请求
	
	MsgID_UserChangeLanguage_Response = 35622; //用户切换语言回复

	MsgId_GetTexasTotalHands_Request = 35623; // 获取所有德州类游戏的总手数
	MsgId_GetTexasTotalHands_Response = 35624;

	MsgID_SportsLogin_Request = 35700; // 体育登陆
	MsgID_SportsLogin_Response = 35701;

	MsgID_SportsLeave_Request = 35702;
	MsgID_SportsLeave_Response = 35703; // 退出体育

	MsgID_BatchDelRemarks_Request = 35704; //批量删除备注
	MsgID_BatchDelRemarks_Response = 35705;

	MsgID_PgLogin_Request = 35706; // pg登陆
	MsgID_PgLogin_Response = 35707;

	MsgID_PgLeave_Request = 35708; // pg退出
	MsgID_PgLeave_Response = 35709;

	MsgID_PgBonusAndFreeGames_Request = 35710; // pg红利和免费游戏
	MsgID_PgBonusAndFreeGames_Response = 35711; // pg红利和免费游戏

	MsgId_KYCVerificationStatus_Request = 35721;  // kyc
	MsgId_KYCVerificationStatus_Response = 35722; //

	MsgId_BlackJackLogin_Request = 35712; // 21点登陆获取token
	MsgId_BlackJackLogin_Response = 35713;

	MsgID_OpenCalmDownWindows_Notice = 35726;    // 打开冷静提示窗口
	MsgID_CalmDownConfirm_Request = 35727;        // 冷静确认提示
	MsgID_CalmDownConfirm_Response = 35728;
	MsgID_CalmDownConfirmResult_Notice = 35729;

	MsgID_MemePoker_Rank_Request = 35801; // 获取排行榜
	MsgID_MemePoker_Rank_Response = 35802;

	MsgID_MemePoker_PropsList_Request = 35804; //道具列表
	MsgID_MemePoker_PropsList_Response = 35805; //
	MsgID_MemePoker_SearchUser_Request = 35807; // 搜索玩家
	MsgID_MemePoker_SearchUser_Response = 35808;
	MsgID_MemePoker_PropsLog_Request = 35814;   //道具记录
	MsgID_MemePoker_PropsLog_Response = 35815;
	MsgID_MemePoker_PropsAction_Request = 35817; // 道具操作
	MsgID_MemePoker_PropsAction_Response = 35818;

	MsgID_MemePoker_CoinExchangeShop_Request = 35821; // 金币兑换的列表
	MsgID_MemePoker_CoinExchangeShop_Response = 35822; //
	MsgID_MemePoker_CoinExchange_Request = 35824; // 兑换请求，砖石兑换金币、积分兑换金币
	MsgID_MemePoker_CoinExchange_Response = 35825;
	MsgID_MemePoker_RechargeGoods_Request = 35827; //充值物品列表
	MsgID_MemePoker_RechargeGoods_Response = 35828;
	
	MsgID_MemePoker_NewPayOrder_Request = 35831; 
	MsgID_MemePoker_NewPayOrder_Response = 35832;
	
	MsgID_MemePoker_PayOrderConfirm_Request = 35833; 
	MsgID_MemePoker_PayOrderConfirm_Response = 35834;

	MsgID_SportsQuickBet_Request = 35902;
	MsgID_SportsQuickBet_Response = 35903;
	MsgID_SportsMatchList_Request = 35904;
	MsgID_SportsMatchList_Response = 35905;
	MsgID_SportsTipTemplate_Request = 35906;
	MsgID_SportsTipTemplate_Response = 35907;
	MsgID_SportsQuickBetFeatureFlag_Notice = 35908;
	MsgID_SportsMatchListV2_Request = 35909; // wpk後台處理過的賽事列表
    MsgID_SportsMatchListV2_Response = 35910; // wpk後台處理過的賽事列表

	MsgID_Sports_Notice_Request = 35911; // wpk體育提示
    MsgID_Sports_Notice_Response = 35912; // wpk體育提示
	
	MsgID_WPK_User_Status_Notice = 35920; // 通知wpk用戶狀態

	MsgId_Rebate_GetEventStatus_Request = 36100;
	MsgId_Rebate_GetEventStatus_Response = 36101;
	MsgId_Rebate_ReceiveReward_Request = 36102;
	MsgId_Rebate_ReceiveReward_Response = 36103;
	MsgId_Rebate_GetEventStatus_Notice = 36104;
	MsgId_Notice_Platform_Maintenance = 36105;
// GeoComply
	MsgID_GeoComply_License_Request = 34000;
	MsgID_GeoComply_License_Response = 34001;
	MsgID_GeoComply_Store_Token_Request = 34002;
	MsgID_GeoComply_Store_Token_Response = 34003;
	MsgID_GeoComply_Config_Notification = 34004;
// SquidHunt
	MsgID_CheckSquidGameBlockStatus_Request = 36300;
	MsgID_CheckSquidGameBlockStatus_Response = 36301;
	MsgID_SquidGameLeave_Existing_Room_Request = 36302;
    MsgID_SquidGameLeave_Existing_Room_Response = 36303;

	MsgID_JackPotMarquee_Notice = 30076;
	MsgID_GeoComply_Generate_Token_Notice = 34005;
}

enum GameId {
	GameId_Dummy = 0;
    World  = 1;      // 世界服务器
	Texas = 2;       // 德州
	StarSeat = 3;   	// 明星桌
	DataServer = 10101;  //数据服
	CowBoy = 10;     // 德州牛仔
    Allin = 20;    //allin or fold
	HumanBoy = 30; // 百人德州

   // 极速服务区间
	ZoomTexas = 40; // 德州极速
	ZoomTexasMax = 49;

	VideoCowboy = 50;	//视频牛仔
	//必下
	Bet = 60;
	// 扑克大师
	PokerMaster=70;
	// 菠萝蜜
	Jackfruit = 80;
	PLO        = 90; //  pot limit omaha

	BlMtt = 900; // 部落mtt

	Sports = 1000; // 体育竞技

	TopMatches = 1001; // 一起看球

	PocketGames   = 1010; // 老虎机

	BlackJack = 1020; // 21点

	SquidHunt = 5001;
}

enum SpecialCards {
    Cards_Dummy = 0 ;
    Cards_Zero  = 255;
    Cards_Back = 256;
}

 // 客户端类型
enum ClientType {
	Dummy = 0;
	Normal = 1; // c++
	OverSeas = 2; // 海外版
	H5 = 3; // h5多语言版app
	H5OverSeas = 4; // h5海外缩减版app
	H5Web = 5; // h5多语言网页版 私语h5版本用这个
	H5WebOverSeas = 6; // h5海外缩减版网页版
	H5VietnamLasted = 7; // h5越南版
	H5WebVietnamLasted = 8; // h5越南网页版
	H5CowboyWeb = 9;//牛仔独立网页版
	H5Thailand = 10; // 泰文版
	H5WebThailand = 11; // 泰文版网页版
    H5Arab=12;//阿拉伯版
    H5Hindi=13;//印度语版
    H5Mempoker=14;//MEMEPOKER
    PC =15;// PC版
}

// 客户端语言类型
enum LanguageType {
	Chinese = 0 ;	// 中文
	English  = 1;	// 英语
	VietNam = 2;	// 越南
}

// 玩家操作事件类型
enum EventType {
	EventType_Dummy = 0;

	// datasercer
	Favorite = 100; // 牌普收藏


	// game
	JoinRoom = 300; // 加入房间
	LeaveRoom = 301; // 离开房间
	SitDown = 302; // 在房间坐下
	StandUp = 303; // 在房间站起
	Buyin = 304; // 买入
	Action = 305; // 玩家行动
	BuyInsure = 306; // 购买保险
	Situation = 307; // 实施战况
	SendCardsFun = 308; // 发发看
	SendChat = 309; // 普通表情
	StayPosition = 310; // 保卫离桌
	BackPosition = 311; // 返回牌桌
	ShowCards = 312; // 亮牌
	BuyOut = 313; // 撤回筹码
	CheckoutAndLeave = 314; // 结算离桌
	DefaultFold = 315; // 预操作
	ForceShowCards = 316; // 强制亮牌
	AutoWithDraw = 317; // 开启自动撤回

	QuickLeave = 318; // 快速离桌
	QuickFold = 319; // 快速弃牌

	BetAction = 320; // 下注
	AutoBet = 321; // 续投
	AdvanceAutoBet = 322; // 高级续投

	SendBarrage = 323; // 发送弹幕
}

//消息体定义

// 被挤下线通知
message DupLoginNotice {
	int32 error = 1;	// error=224网络不稳定, 其他表示挤下线
}

//logon
message RequestLogon {
  string version = 1;
  string token = 2;
  string device_info = 3;
  string invitation_code = 4;
  ClientType client_type = 5; // 客户端类型
  string CurrentLanguage = 6;//客户端现在使用的语言
  string os =7;//客户端操作系统
  string os_version =8;//客户端操作系统版本
  string third_version = 9; // the version of thirdparty
}

message ResponseLogon {
    int32 error = 1; // 0: success; 1:version not match; 2:uid not-exist; 3:token-failure
    uint32 firstClubId = 2;
    uint32 firstAlliId = 3;
	repeated GameId  swtichList= 4;
	int32 bl_mtt_status = 5;
	bool  is_help_warp = 6;//用户当前是否有示过多成助力红包
	int32 blackJackStatus = 7; // 21点是否再维护
	BlackJackData blackJackData = 8; // 21点token相关数据
	MttData mttData = 9; // mtt相关数据
	bool is_c2c_block = 10;
	int32 blackJackDualStatus = 11;
	int32 blSpinStatus = 12;// BL Spin status
}

// create club
message ClubParams{
	string club_name 					= 1;
	string club_area 					= 2;
	string club_icon 					= 3;
    uint32 club_percent                 = 4;
    uint32 create_source                = 5; // 如何创建(扫码 手动)
}

message RequestCreateClub{
	ClubParams param = 1;
}

message ResponseCreateClub{
	int32 error = 1; 
}

// ClubSnapshotList
message RequestClubSnapshotList{
	uint32 uid = 1;
}

message ResponseClubSnapshotList{
	int32 error = 1; 
}

message ClubSnapshotListParams{
	uint32 club_id 						= 1;
	string club_name 					= 2;
	string club_area 					= 3;
	string club_icon 					= 4;
	int32 club_member_max 				= 5;
	int32 club_member_count 			= 6;
	int32 club_type 					= 7;//是否私密：1 私密 0 公开 2 推荐
	int32 club_owner 					= 8;
	string club_descrption 				= 10;
	int32 club_create_time 				= 11;
	int32 club_level                    = 12; //俱乐部星级
	uint32 expire_time                  = 13; //到期时间
	int32 is_public_member              = 14; //是否为公共俱乐部成员 1：是 2：否
	repeated int32 opened_blindlevels   = 15; //俱乐部当前开启的盲注级别奖池
	int32 is_manager                    = 16; //是否为管理员
	string invitation_code 				= 17; //俱乐部邀请码
    string InvitationMemberCode         = 18; //俱乐部个人邀请码
	bool has_create_alliance		    = 19; //该俱乐部是否有创建并审核通过的联盟
	uint32   is_agree                   = 20;  // 0 表示false, 1 表示true
    uint32 invitation_percent           = 21; //俱乐部个人邀请百分比
    bool  setInvitePercentMark          = 22; //设置个人邀请百分比标记(true:已设置过，flase:没有设置过)
	bool  has_join_otheralliance		= 23;	//是否加入别人的联盟 不包含自己创建的联盟
}

message NoticeClubSnapshotList{
	repeated ClubSnapshotListParams list = 1;
}

// Join club
message JoinClubParams{
	uint32 club_id = 1;
	uint32 club_uid = 2;
	string club_message = 3;
	uint32 apply_time = 4;
}

message RequestJoinClub{
	uint32 club_id = 1;
	uint32 club_uid = 2;
	string club_message = 3;
	string invitation_code = 4;
}

message NoticeJoinClub{
	uint32 club_id = 1;
	uint32 club_uid = 2;
	string club_message = 3;
	
	string applicant_name = 4; //申请者的名字
	string applicant_thumb = 5;
	string club_name = 6;
	
	uint32 op_time = 7;	//发送时间
	uint32 msg_type = 8;	//消息类别
}

message ResponseJoinClubToMember{
	int32 error = 1;
}

message ResponseJoinClubToAdmin{
	int32 error = 1;
}

message NoticeJoinClubToMember{
	int32 result = 1; // 1：同意 2：拒绝
	uint32 club_id = 2;// 俱乐部id
	uint32 uid = 3;    // 被操作者id
	string reason = 4;// 理由
	string club_name = 5;
	uint32 op_time = 6;	//操作时间
	uint32 msg_type = 7;	//消息类别
	uint32 admin_id = 8;    // 管理员id
	uint32 operator_id = 9;   // 操作者
	string Operator_name = 10; // 操作者名字
	uint32 is_agree = 11;
	string apply_name = 12; // 申请者名字
}

message ReplyJoinClub{
	int32 result = 1; // 1：同意 2：拒绝
	uint32 club_id = 2;// 俱乐部id
	uint32 uid = 3;    // 被操作者id
	string reason = 4;// 理由
}

message RequestLeaveClub{
	LeaveClubParams param = 1;
}

message ResponseLeaveClub{
	int32 error = 1;
}

//
message LeaveClubParams{
	uint32 club_id = 1;
	uint32 club_did = 2;
}

//
/*
message ClubCurrentBoardParams{
	uint32 club_id = 1;
	uint32 club_uid = 2;
}*/

message RequestClubCurrentBoard{
}

message MvpData {
    uint32 uid = 1;
    string nickname = 2;
    string thumb = 3;
    uint32 plat = 4;
	bool is_ad_user = 5; // 標註是否為廣告用戶
    string wpk_sys_avatar = 6; // wpk 廣告取代頭像
}

message StarData {
	uint32 uid = 1;
	string nickname = 2;
	string thumb = 3;
	uint32 status = 4; // 状态 1. 在线 2. 已下播
}

message ClubGameSnapshot{
	//uint32 create_player_id = 1;
	//string creator_name = 2;
	uint32 club_id = 3;
	int32 game_mode = 4; //区分普通牌局/比赛/其它游戏类型
	string room_name = 5; //区分普通牌局/比赛/其它游戏类型
	int32 player_count  = 6;   
	int64 small_blind  = 7;   
	int64 big_blind  = 8;  
	int64 buyin_min  = 9;   
	int64 buyin_max  = 10;  
	uint32 create_time = 11;
	//bool  buyin_control = 12; 
	bool  insurance = 13; 
	bool  anti_cheating = 14; 
	bool  straddle = 15; 
	//bool  rand_seat = 16; 
	int32 ante = 17;
	
	int32 player_count_max 	= 18; //牌桌最大人数
	//int32 owner_type 	    = 19; //区分普通牌具/俱乐部牌局/定制俱乐部牌局
	
	//string pic_path = 20; //图片路径
	string club_name = 21; //俱乐部名称
	int32 rule_time_limit = 22; //牌局时长对应8个时长类型
	uint32 room_id = 23; //房间id
	int32 game_status  = 24;     //1:牌局准备中 2:牌局进行中
	uint32 start_time = 25; //未开始=0；已点击开始=点击时间
	bool jackpot_isopen  = 26;     //1:已开启 2:未开启
	bool is_allin_allfold = 27; //是否是allin allfold
	uint32 extra_time = 28;
	bool is_opened_drawback             = 29; //是否打开撤码功能
	bool short_fullhouse_flush_straight_three = 30;
	//bool is_force_showcard = 31;
	//int32  room_type = 32; //房间类型 1-联盟牌局 0-非联盟牌局
	int32  has_buyin = 33; // 是否买入 1-买入  0-没买入 2 买入了但提前结算了
	string join_password = 34; //加入房间密码
	string buyin_password =35; //带入密码
	int32 is_mirco = 36;	//呵呵
	int32 left_seatnum = 37;	//剩余空位数
	bool anti_simulator = 38;  // 禁用模拟器
	int32 game_id = 39;	//剩余空位数 5-跑马
	//uint32  allinRate = 40; // allin率

    //bool    hasEndTime = 45;      // false 没有 true 有
    //repeated uint32  showForClients    = 47; // 此房间对哪些客户端类型可见(客户端忽略这个字段)
    bool   isCriticismField = 48; // 是否暴击场(只针对德州)
    uint32 minCritProb                 = 49; // 最小暴击时间
    uint32 maxCritProb                 = 50; // 最大暴击时间
    uint32 critNeedMoney               = 51; // 暴击场暴击倍数
	uint32 anti_simulator_ignore_cond               = 52; // 当级别手数大于设定值是忽略模拟器限制
	int32 manual_created = 53;               // 是否为手动创建的牌局 0:模板创建 1:手动创建
	MvpData mvp_data = 54; //mvp信息(盈利最多的人)
	//int32 minimum_amount = 55;    // 能玩游戏的最小金额(菠萝蜜游戏专用)
	bool IscalcIncomePerhand = 56; // 是否把抽（只针对长牌小 中 大局）
	//repeated uint32 plats = 57; // 第三方平台
	repeated StarData starData = 58; // 明星数据
	uint32 bystanderNum = 59; // 旁观人数
	int64 notifyTime = 60; // 放行时间(客户端用来展示)
	repeated ProDatas proDatas = 61; // 多级别
	int32 proLevel = 62; // 玩家当前房间级别
	//uint32 curLevel = 63; // pro玩家对应的优先级别
	int32  currencyType = 64; // 牌桌货币类型 0:金币 101:usd
	bool  red_envelope_switch = 65; // Red envelope toggle to make it on/off
	bool stick_on_top =66;
	bool forceWithdrawMode = 67; // it is used to display the room name

	//int64 tablesCount = 68; // plo room counts PLO房間數量 僅PLO使用
	bool looseMode =69; // 松浪桌模式 : 德州扑克长牌 ante设置为大盲的1.25倍 无straddle 最小带入为200bb
	GameLevelEnum stickOnLevelTab = 70; // 強制將牌局使前端顯示在某級別
	bool is_loose_mode_stick_on_top = 71; // 松浪桌置頂
	int64 starseatStartTime = 72; // starseat start timestamp
}

enum GameLevelEnum {
	GameLevelEnumNone = 0;
	GameLevelEnumMicro = 1;
	GameLevelEnumSmall = 2;
	GameLevelEnumMedium = 3;
	GameLevelEnumHigh = 4;
}

enum GameSizeType {
	GameSizeTypeNone = 0;
	GameSizeTypeMicro = 1;
	GameSizeTypeSmall = 2;
	GameSizeTypeMedium = 3;
	GameSizeTypeHigh = 4;
}

enum RoomMode {
	RoomModeNone = 0;
	RoomModeLoose = 1;
	RoomModeBomb = 2;
}



enum FilterMode {
    INCLUDE = 0;  // 包含
    EXCLUDE = 1;  // 排除
}

enum CurrencyType {
  GOLD = 0;
  USD = 101;
  DIAMOND = 105;
}

message RequestClubCurrentBoardV3 {
    FilterMode currencies_filter_mode = 1; // 預設給 0 (INCLUDE) or 1 (EXCLUDE)  , optional
    repeated CurrencyType currencies = 2; // optional
}

message NoticeClubCurrentBoardV3 {
    repeated ClubGameSnapshotV3 list = 1;
    int32 total = 2;
    int32 page = 3;
    FeatureFlags flags = 4;
    uint32 seq = 5; // request header seq, to prevent mixed response
}

message SquidHuntGameData {
    enum Mode {
        NORMAL_MODE = 0; // normal squidhunt game mode
        MULTIPLIER_MODE = 1; // multiplier squidhunt game mode
    }
    Mode mode = 1;
    bool isFirstRoundDoubleSquid = 2; // whether double the first round winning
}

message ClubGameSnapshotV3 {
    uint32 game_id = 1;    //剩余空位数 5-跑马
    uint32 room_id = 2; //房间id
    uint32 iden_num = 3; //房間號碼
    int32 game_mode = 4; //区分普通牌局/比赛/其它游戏类型
    RoomMode room_mode = 6;
    int32 player_count  = 7;
    int64 small_blind  = 8;
    int64 big_blind  = 9;
    int64 buyin_min  = 10;
    int64 buyin_max  = 11;
    uint32 create_time = 12;
    bool  straddle = 13;
    int32 ante = 14;
    int32 player_count_max     = 15; //牌桌最大人数
    int32 rule_time_limit = 16; // ?? 牌局时长对应8个时长类型
    uint32 start_time = 17; //未开始=0；已点击开始=点击时间
    uint32 extra_time = 18; // 延時時間
    bool is_force_showcard = 19;
    int32  has_buyin = 20; // ?? 是否买入 1-买入  0-没买入 2 买入了但提前结算了
    int32 is_mirco = 21;   //
    int32 left_seatnum = 22;   //剩余空位数
    bool anti_simulator = 23;  // 禁用模拟器
    uint32 anti_simulator_ignore_cond               = 24; // 当级别手数大于设定值是忽略模拟器限制
    MvpData mvp_data = 25; //mvp信息(盈利最多的人)
    bool IscalcIncomePerhand = 26; // ?? 是否把抽（只针对长牌小 中 大局
    repeated StarData starData = 27; // 明星数据
    uint32 bystanderNum = 28; // 旁观人数(明星桌用)
    CurrencyType  currencyType = 29; // 牌桌货币类型 0:金币 101:usd
    bool red_envelope_switch = 30;
    bool forceWithdrawMode = 31;             // 用來通知客戶端此房間是否強制開啟自動撤碼模式.目前只支援德州短牌
    bool is_loose_mode_stick_on_top = 32; // 松浪桌置頂
    int64 starseatStartTime = 33; // starseat start timestamp
    GameLevelEnum stickOnLevelTab = 34; // 強制將牌局使前端顯示在某級別
    string room_name = 35; //只有 starseat 才有
    map<string,string> room_sub_title = 36; // 明星桌副標題
    int64 averagePotAmount = 37;
    optional SquidHuntGameData squidHuntGameData = 38;
}

// 房间多级别参数
message ProDatas {
	int32 levelLimit = 1;  // 当前级别对应限制的数量 -1 代表不限制 0 代表不让进 N代表这个级别最多玩家数量
	uint32		nowNum =2; // 当前这个级别房间里面有多少人
	uint32 tableLevel = 3; // 牌桌优先级别
}

message ResponseClubCurrentBoard{
	int32 error = 1;
}

message NoticeClubCurrentBoard{
	repeated ClubGameSnapshot list = 1;
	int32 total = 2;
	int32 page = 3;
	FeatureFlags flags = 4; 
}


message FeatureFlags {  
		bool shortdeck_visible_micro  = 1;
		bool shortdeck_visible_small  = 2;
		bool shortdeck_visible_medium = 3;
		bool shortdeck_visible_big    = 4;
		bool splash_visble_micro 	  = 5;	
		bool splash_visble_small 	  = 6;	
		bool splash_visble_medium     = 7;	
		bool splash_visble_big 		  = 8;
		bool is_platform_maintenance = 9;		
    	bool show_squid_icon = 10;
   		bool squid_stick_top = 11;
}

//
message ClubMemberSnapshotListParams{
	uint32 club_id = 1;
	uint32 club_uid = 2;
	uint32 pull_pos = 3;
	uint32 pull_count = 4;
}

message RequestClubMemberSnapshotList{
	ClubMemberSnapshotListParams param = 1;
}

message ResponseClubMemberSnapshotList{
	int32 error = 1;
}

message ClubMemberSnapshot{
	uint32 member_uid = 1;
	string member_icon = 2;
	string member_name = 3;
	int32 club_member_active = 4;
	int32 total_member_active = 5;
	int32 member_last_login_time = 6;
	int32 member_auth = 7;
	int64 user_gold = 9;
	bool is_online = 10;
}

message NoticeClubMemberSnapshotList{
	repeated ClubMemberSnapshot snapshots = 1;
	uint32   total_count = 2;
	uint32   last_inc_id = 3;
}

//
message ModifyClubMemberParams{
	uint32 club_id = 1;
	uint32 club_uid = 2;
	uint32 target_id = 3;
	int32 action_type = 4;
	bool  rec_type = 5; // true: php  false: 客户端
}

message RequestModifyClubMember{
	ModifyClubMemberParams param = 1;
}

message ResponseModifyClubMember{
	int32 error = 1;
}

message NoticeModifyClubMember{
	int32 action_type = 1;
	uint32 club_id = 2;
	string club_name = 3;
	uint32 target_player_id = 4;
	string target_player_name = 5;
	uint32 op_time = 6;	//操作时间
	uint32 msg_type = 7;	//消息类型
	uint32 member_inc_id = 8; //俱乐部成员的自增Id
	uint32 operator_player_id = 9;
}

//
message ModifyClubInfoParams{
	uint32 club_id = 1;
	uint32 club_uid = 2;
	string club_name= 3;
	string club_descrption = 4;
	int32 action_type = 5;
}

message RequestModifyClubInfo{
	ModifyClubInfoParams param = 1;
}

message ResponseModifyClubInfo{
	int32 error = 1;
}

//
message GrantClubFundParams{
	uint32 club_id = 1;
	uint32 club_uid = 2;
	uint32 target_id = 3;
	int64 amount = 4;
}

message RequestGrantClubFund{
	GrantClubFundParams param = 1;
}

message ResponseGrantClubFund{
	int32 error = 1;
}

message RequestSearchClubInfo{
	uint32 club_id = 1;
}

message ResponseSearchClubInfo{
	int32 error = 1; 
}

message NoticeSearchClubInfo{
	ClubSnapshotListParams snapshots = 1;
}

message RequestClubCreaterInfo{
	uint32 club_id = 1;
}

message ResponseClubCreaterInfo{
	int32 error = 1; 
}

message NoticeClubCreaterInfo{
	string create_player_name   = 1; //俱乐部创建者的名字
	string create_player_thumb  = 2; //俱乐部创建者的头像
}

message NoticePurchaseClubLevel{
	uint32 expire_time = 1; 
}

message RequestSendMsg{
	uint32 club_id = 1;
	string msg = 2;
}

message ResponseSendMsg{
	int32 error = 1; 
}

message NoticeSendMsg{
	uint32 club_id = 1;
	uint32 user_id = 2;
	uint32 chat_time = 3;
	string player_name = 4;
	string player_icon = 5;
	string msg = 6;
}

//
message RequestGetUserData{
	uint32 user_id = 1;
}

message ResponseGetUserData{
	int32 error = 1; 
}

message NoticeGetUserData{
	string mobile = 1; //手机号
	string nick_name = 2; //nick_name
	string avatar = 3;    //用户头像
	int32 gender = 4;        //性别 0未定义/保密 1男性 2女性
	int64 user_gold = 5;     //用户金币数值（单位：分）
	int32 clubs_max = 6;     //可创建的俱乐部数量
	int32 current_clubs = 7; //当前创建的俱乐部数量
	string user_marks = 8;     //个性签名
	uint32 user_id = 9;        //用戶id
	int32  card_type = 10;     //卡片类型 0未定义 1蓝卡，2金卡(月卡)，3白金卡(年卡)
	int64  deposit_gold = 11; // 存储金币
	int64  game_coin = 12;//小游戏金币
	int64  user_points = 13;//积分
	int64  ratio =14;//积分兑换比例
	int64  total_amount = 15;//用户总金额
	int64  usdt =16; //用户usdt数量
	int64  deposit_usdt = 17; // 存储usdt
	string areaCode = 18; // 手机区号，优先绑定的
	string mobile2 = 19; // 手机号，优先绑定的
	int64 system_time = 20;// 当前系统时间戳 秒级
	int64 calm_down_deadline_time = 21; // 冷静状态到什么时候 时间戳 秒级
	int64  diamond = 22; // 钻石，memepoker使用的
}

//GetJackpotData
message RequestGetJackpotData{
	uint32 club_id = 1;
	uint64 room_id = 2;
}

message ResponseGetJackpotData{
	int32 error = 1; 
}

message Jackpot{
	int64 amount = 1; //奖池金额
	int32 blind_level = 2;//盲注级别
}

message NoticeGetJackpotData{
	uint32 club_id = 1;
	string club_name = 2;
	string club_avatar = 3;
	string club_area = 4;
	repeated Jackpot jackpots = 5;
}

//JackpotSetting
message RequestJackpotSetting{
	uint32 club_id = 1;
}

message ResponseJackpotSetting{
	int32 error = 1; 
}

message JackpotInfo{
	int64 amount = 1; //奖池金额
	int32 blind_level = 2;//盲注级别
	int64 profit_scale = 3;//盈利规模
	int64 drawin_amount = 4;//抽水数额
}

message AwardType{
	int32 hand_level = 1; //牌型
	int32 award_percent = 2;//奖励比例
}

message NoticeJackpotSetting{
	uint32 club_id = 1;
	repeated JackpotInfo jackpots = 2;
	repeated AwardType awardTypes = 3;
	int32 award2club_percent = 4;//奖励比例
}

//SetJackpot
message RequestSetJackpot{
	uint32 club_id = 1;
	repeated JackpotInfo jackpots = 2;
	repeated AwardType awardTypes = 3;
	int32 award2club_percent = 4;//奖励比例
}

message ResponseSetJackpot{
	int32 error = 1; 
}

//RecoverJackpotSetting
message RequestRecoverJackpotSetting{
	uint32 club_id = 1;
}

message ResponseRecoverJackpotSetting{
	int32 error = 1; 
}

//JackpotAmout
message NoticeJackpotAmout{
	uint32 club_id = 1;
	int32 blind_level = 2;//当前局盲注级别
	int64 prev_amount = 3;//上次奖池金额
	int64 current_amout = 4;//当前奖池金额
}

//CurrentRoomJackpot
message RequestCurrentRoomJackpot{
	uint32 club_id = 1;
	int32 blind_level = 2;
	uint64 room_id = 3;
}

message ResponseCurrentRoomJackpot{
	int32 error = 1; 
}

message NoticeCurrentRoomJackpot{
	int64 profit_scale = 1;
	int64 drawin_amount= 2;
	repeated AwardType awardTypes = 3;
}

//JackpotAwardRecord
message RequestJackpotAwardRecord{
	uint32 club_id = 1;
	int32 blind_level = 2;
	uint64 room_id = 3;
}

message ResponseJackpotAwardRecord{
	int32 error = 1; 
}

message AwardInfo{
	uint32 player_id = 1; 	//玩家id
	int32 hand_level = 2;	//牌型
	int64 award_amount = 3;	//奖励金额
    uint32 award_time = 4;	//奖励时间
	string player_name = 5; //玩家昵称
	string avatar = 6;		//玩家頭像
	uint64 game_uuid = 7;	//牌局uuid
	AwardInfoType type = 8;	//奖励類型
	int32 platform = 9;
	int32 award_ratio = 10;
	string wpkSysAvatar = 11; // wpk 廣告取代頭像
	bool isAdUser = 12; // 標註是否為廣告用戶
}

message NoticeJackpotAwardRecord{
	uint32 club_id = 1;
	AwardInfo luckDog = 2;
	repeated AwardInfo awardInfos = 3;
}

//JackpotInjectAmount
message RequestJackpotInjectAmount{
	uint32 club_id = 1;
	int32 blind_level = 2;
	int64 amount = 3;
}

message ResponseJackpotInjectAmount{
	int32 error = 1; 
}

message NoticeJackpotInjectAmount{
	uint32 club_id = 1;
	int32 blind_level = 2;
	int64 amount = 3;
}

message NoticeJackPotAwardInfo{
	repeated AwardInfos awardInfo = 1;
	uint32 cur_time = 5;	//ʱtime
	uint32 msg_type = 6;
	uint32 blind_level = 7;		//当前盲注级别枚举	; 游戏菠萝蜜时，这个值表示的是级别
	uint32 sys_msg_type =8 ; //自定义消息 1-中奖消息 2-跑马灯消息
    uint32 gameId = 9;       //游戏Id
    int32  mode = 10;        //长短牌
	repeated uint32 playGameIds = 11; // 在哪些游戏播放，正常不会为空，为空时取gameId.
}

message AwardInfos {
	uint32 award_playid = 1;
	int64 award_amount = 2;
	int32 hand_level = 3;
	string award_player_name = 4;
	AwardInfoType type = 5;
}

enum AwardInfoType{
	None = 0;
	JP_Normal = 1;
	JP_Earth = 2;
	JP_Mars = 3;
}

message RequestHeartBeat{
	uint32 uid = 1;
	PositionInfo position=2;
}

message ResponseHeartBeat{
	uint32 uid = 1;
	int64 timestamp = 2; // 服务器当前时间戳
}

message RequestCreateAlliance{
	string alliance_name = 1;
	uint32 club_id = 2; 
	string area_code = 3;	// 电话号码区号
	string mobile = 4;		// 电话号码
	string email = 5;		// 邮箱
}

message ResponseCreateAlliance{
	int32 error = 1; 
}

message RequestLeaveAlliance{
	uint32 alliance_id = 1;
	uint32 club_id = 2;
}

message ResponseLeaveAlliance{
	int32 error = 1;
	uint32 isDisband = 2;
}

message NoticeLeaveAlliance{
	uint32 alliance_id = 1;
	uint32 club_id = 2;
	uint32 club_admin_id = 3;	//盟主俱乐部id
	string alliance_name = 4;
	string club_name = 5;
	uint32 op_time = 6;
	uint32 msg_type = 7;
	uint32 belong_club_id = 8;	//当前被通知者所属俱乐部id
}

//请求联盟信息
message RequestSearchAllianceInfo{
	uint32 alliance_id = 1;
}

message ResponseSearchAllianceInfo{
	int32 error = 1; 
}

message ClubItemInfo{
	uint32 club_id 					= 1;
	int32 club_member_max 			= 2;
	int32 club_member_count 		= 3;
	string club_name 			    = 4;
	string creater_name 			= 5;
	string club_thumb 			= 6;
	uint32 creater_id 			= 7;
	
	int64 max_buyin_limit       = 8; //额度上限
	int64 cur_buyin_limit      = 9; //实时带入
	bool control_buyin          = 10; //带入控制
}

message NoticeSearchAlliance{
	uint32 alliance_id 					= 1;
	uint32 creater_club_id				= 2;
	int32 alliance_club_max 			= 3;
	int32 alliance_club_count 			= 4;
	string alliance_name 				= 5;
	repeated ClubItemInfo clubItems     = 6;//俱乐部信息
}

//剔除联盟成员
message RequestKickoffAllianceMember{
	uint32 alliance_id = 1;
	uint32 club_id = 2;
	uint32 target_id = 3;
	bool   recv_type = 4; // true: php  false: 客户端
}

message ResponseKickoffAllianceMember{
	int32 error = 1;
}

message NoticeKickoffAllianceMember{
	uint32 alliance_id = 1;
	uint32 target_club_id = 2;
	string alliance_name = 3;
	string target_club_name = 4;
	uint32 op_time = 5;
        uint32 msg_type = 6;
	uint32 belong_club_id = 7;		//当前接受者所属的俱乐部id
}


//AllianceSnapshotList
message RequestAllianceList{
	uint32 club_id = 1;
}

message ResponseAllianceList{
	int32 error = 1; 
}

message AllianceListParams{
	uint32 alliance_id 			= 1;
	string alliance_name 		= 2;
	int32 club_max 				= 3;
	int32 club_count 			= 4;
	bool is_creater             = 5; //是否为盟主
	int32 reviewed 			    = 6;
	uint32 creater_club_id 		= 7; //创建这个联盟的俱乐部id
	uint32 expire_left_time     = 8;//社区申请过期剩余时间 
}

message NoticeAllianceList{
	repeated AllianceListParams list = 1;
}

// Join alliance
message RequestJoinAlliance{
	uint32 alliance_id = 1;
	uint32 club_id = 2;
	string message = 3;
	uint32 apply_time = 4;
}

message NoticeJoinAlliance{
	uint32 alliance_id = 1;
	uint32 club_id = 2;
	string message = 3;
	string club_name = 4; //申请者的名字
	string alliance_name = 5;
	uint32 apply_time = 6;
	uint32 msg_type = 7;
}

message ResponseJoinAllianceToMember{
	int32 error = 1;
}

message ResponseJoinAllianceToAdmin{
	int32 error = 1;
}

message NoticeJoinAllianceToMember{
	int32 result = 1;     // 1：同意 2：拒绝
	uint32 alliance_id = 2;// 联盟id
	uint32 club_id = 3;    // 被操作者俱乐部id
	string reason = 4;    // 理由
	string alliance_name = 5;
	uint32 club_admin_id = 6;     // 申请者俱乐部管理员id
	uint32 op_time = 7;
	uint32 msg_type = 8;	
	uint32 alli_club_id = 9; // 创建联盟的俱乐部id
	uint32 curr_club_id = 10; // 当前的俱乐部id
}

message ReplyJoinAllianceToWorld{
	int32 result = 1;      // 1：同意 2：拒绝
	uint32 alliance_id = 2; // 俱乐部id
	uint32 club_id = 3;     // 被操作者id
	string reason = 4;     // 理由
}

message RequestAddRemarks{
	uint32 target_id = 1;
	int32 remark_type = 2;
	string taget_remark = 3;
}

message ResponseAddRemarks{
	int32 error = 1; 
}

message RequestBatchDelRemarks{
	repeated uint32 targetIds = 1;
}

message ResponseBatchDelRemarks{
	int32 error = 1;
	repeated uint32 targetIds = 2;
}

message NoticeAddRemarks{
	uint32 target_id = 1;
	int32 remark_type = 2;
	string taget_remark = 3;
}

message RequestGetAllRemarks{
	uint32 playerid = 1;
}

message ResponseGetAllRemarks{
	int32 error = 1; 
}

message NoticeGetAllRemarks{
	string remarks_data = 1;
	bool start = 2; // 第一个包为true,其他都为false
}

message RequestClearAllianceMaxBuyinLimit{
	uint32 alliance_id = 1; // 俱乐部id
	uint32 club_id = 2;     // 被操作者id
}

message ResponseClearAllianceMaxBuyinLimit{
	int32 error = 1; 
}

message RequestSetAllianceMaxBuyinLimit{
	uint32 alliance_id = 1; // 俱乐部id
	uint32 club_id = 2;     // 被操作者id
	int64 amount = 3;
}

message ResponseSetAllianceMaxBuyinLimit{
	int32 error = 1; 
}

message RequestSetAllianceControlBuyin{
	uint32 alliance_id = 1; // 俱乐部id
	uint32 club_id = 2;     // 被操作者id
	bool control_buyin = 3; //带入控制
}

message ResponseSetAllianceControlBuyin{
	int32 error = 1; 
}

message RequestFairPlayReport{
	uint32 roomid = 1;
	uint32  clubid = 2;
	uint64 room_uuid = 3;
	uint64 game_uuid = 4;
	repeated uint32 suspect_uids = 5; //怀疑对象uid列表
	string contact = 6; //反馈联系方式（如果有）
	string detail = 7; //描述细节（如果有）
	string room_uuid_js = 8;
	string game_uuid_js = 9;
}

message ResponseFairPlayReport{
	int32 error = 1;
}

message RequestDeviceInfoReport{
    uint32 report_channel = 1; //1:注册上报；2：充值上报 (3:登录上报 已经有了不用上传)
	string device_info = 2;
	string Ip   = 3; // 玩家ip
}

message ResponseDeviceInfoReport{
	int32 error = 1; 
}

message ClubGrantFundNotice{
	int32	error = 1;
	uint32	op_playerId = 2;		//转账的人的id
	uint32	clubid = 3;				//俱乐部id
	uint32	targetid = 4;			//目标的id
	int64	amount = 5;				//转账金额	
	uint32	op_time = 6;			//时间
	uint32	msg_type = 7;	
	string	op_name = 8;			//操作者名字
	string	target_name = 9;		//目标名字
}

//GetIncome
message RequestGetIncome{
	uint32 uid = 1;
}

message ResponseGetIncome{
	int32 error = 1; 
}

message NoticeGetIncome{
	int64 club_fee = 1;
	int64 club_insurance = 2;
	int64 club_jackpot = 3;
	int64 alli_fee = 4;
	int64 alli_insurance = 5;
	int64 alli_jackpot = 6;
}

message TransferToOtherInfo{
	uint32	operator_id = 1;
	uint32	club_id = 2;
	uint32	targer_id = 3;
	int64	amount = 4;
	int32	grant_time = 5;	//转账时间
	string	operator_name = 6;
	string	target_name = 7;
}

message RequestGetUserClubGrantInfo{
	uint32 uid = 1;
}

message ResponseGetUserClubGrantInfo{
	int32 error = 1;
}

message NoticeGetUserClubGrantInfo{
	repeated TransferToOtherInfo to_list = 1;	//转给别人的钱的信息
	repeated TransferToOtherInfo get_list = 2;	//别人转过来的钱的信息
}

message NoticeNotifyUserGoldNum{
	uint32	uid = 1;
	int64	changeNum = 2;	//金币变化值(客户端没用到)
	int64	goldNum = 3;	//变化后金币值
	int64   game_coin=4;    //变化后小游戏金币
	int64   total_amount=5; //变化后账户总资产
	int64   total_points=6;    //变化后总用户积分
	int64   usdt=7;   //变化后总usdt
	int64    diamond = 8; // 变化后的钻石。
}

message AttachmentInfo{
	uint32	item_id = 1;		//物品ID 非0
	string  item_name = 2;		//物品名称 非0
	int32	item_num = 3;		//物品数量 非0 负数表示惩罚
}


message MailInfo{
        uint32  mail_id = 1;            //邮件ID 
        uint32  mail_type = 2;          //邮件类型 1邮件 2需要及时弹出的公告 3不需要即时弹出的公告
        uint32  mail_state = 3;         //邮件状态 1已读 2未读
        repeated string  mail_title = 4;         //邮件标题 非空 有且仅有2个数组元素 0号位元素表示中文 1号位元素表示英文 多语言情形下以此类推
		uint32  mail_sender_id = 5;     //发送者uid 非0
		string  mail_sender_nickname = 6; //发送者昵称 非空
        string  mail_appellation = 7;   //邮件抬头 非空
        repeated string  mail_content = 8;       //邮件内容 非空 有且仅有2个数组元素 0号位元素表示中文 1号位元素表示英文 多语言情形下以此类推
        repeated string  mail_inscribe = 9;      //邮件签名 非空 有且仅有2个数组元素 0号位元素表示中文 1号位元素表示英文 多语言情形下以此类推
        uint32  mail_sendtime = 10;      //邮件发送时间 非0
        uint32  mail_expiredtime = 11;  //失效时间 默认15天 非0
		uint32	isexpired = 12;			//是否过期 1是2否3已删除
        repeated AttachmentInfo attachment_list = 13;         //附件列表 非nil             
}

message RequestGetUserMailList{
	uint32	uid = 1;	
	uint32	mail_begin_index = 2;	//邮件开始索引  从0开始
	uint32	mail_end_index = 3;	//请求结束索引
}

message ResponseGetUserMailList{
	uint32 error = 1;	//错误码
}

message NoticeGetUserMailList{
	repeated MailInfo mail_list = 1; //邮件信息列表
}

message RequestFetchOneMail{
	uint32	uid = 1;	
	uint32	mail_id = 2;	
}

message ResponseFetchOneMail{
	uint32 error = 1;
}

message NoticeFetchOneMail{ 
	MailInfo onemail = 1;
}


message NotifyUserMailNum{
	uint32 uid = 1;	
	uint32 mail_total_num = 2;		//邮件总数
	uint32 mail_new_num = 3;		//未读邮件数量
	uint32 anounce_total_num = 4;	//公告总数
	uint32 anounce_new_num = 5;		//未读公告总数
}


//通知创建或解散俱乐部 --系统消息
message NoticeCreateClub{
	uint32 uid = 1;
	uint32 op_type = 2;	//1创建 2解散 3俱乐部成员离开俱乐部
	uint32 clubid = 3;	//俱乐部id
	uint32 result = 4;	//结果 1成功 2失败
	uint32 op_time = 5;	//时间
	uint32 msg_type = 6;	//1俱乐部消息 2转账消息 3联盟消息 4牌局消息 5全局消息
	string club_name = 7;	//俱乐部名称
	uint32 club_create_uid = 8;	//创建者uid
	string u_name = 9;		//通知目标的名字
}

//客户端请求公告列表
message RequestAnounceList{
	uint32 uid = 1;
}

message ResponseAnounceList{
	uint32 error = 1;
}

message NoticeAnounceList{
	repeated MailInfo anounce_list = 1; //邮件信息列表
}

//通知新的公告
message NoticeOneAnounce{
        MailInfo oneanounce = 1;
}

//创建联盟通知消息
message NoticeCreateAlliance{
	uint32 uid = 1;
	uint32 op_type = 2;	//1创建申请已发送 2创建成功/失败 
	string a_name = 3;	//联盟名称
	uint32 result = 4;	//结果 1成功2失败
	uint32 op_time = 5;     //时间
    uint32 msg_type = 6;    //1俱乐部消息 2转账消息 3联盟消息 4牌局消息 5全局消息
	uint32 alliance_id = 7;	//联盟id
	string reason = 8;		//创建失败时的理由	
}

//MsgID_AddCoinOrder_Pay_Request   加币订单请求
message RequestAddCoinOrder{
    int32  type = 1;        //渠道号 1:ios 2:第三方
    uint32 uid = 2;         //用户uid
    string productid = 3;   //产品号(目前就ios渠道有，其他渠道则不填)
    uint32 amount = 4;      //产品价格(目前ios)
	string geoComplyToken = 5; // token for geocomply validation
}

//MsgID_AddCoinOrder_Pay_Response  加币订单请求回复
message ResponseAddCoinOrder{
    int32  error = 1;           //错误码
    uint32 srv_add_order  = 2;   //加币游戏订单号
    string cb_url         = 3;  //发货回调地址
    string token = 4;
	string failedReasons = 5; 	// geocomply check JSON string
}

//MsgID_AddCoinResult_Pay_Notice 加币结果回复
message NoticeAddCoinResult{
    int32 error = 1;            //加币错误码
    int32 add_coin = 2;         //加币成功，加币数
}

//MsgID_DelCoinOrder_Pay_Request   减币订单请求
message RequestDelCoinOrder{
    int32  type = 1;        //渠道号 目前就 2:第三方
    uint32 uid = 2;         //用户uid
	string geoComplyToken = 3; // token for geocomply validation
}

//MsgID_DelCoinOrder_Pay_Response  减币订单请求回复
message ResponseDelCoinOrder{
    int32  error         = 1;   //加币错误码
    uint32 srv_del_order = 2;   //减币游戏订单号
    string cb_url        = 3;   //减币校验回调地址
    string token         = 4;
    int64 reward_points  = 5; //用户积分
	string failedReasons = 6; 	// geocomply check JSON string
}

//MsgID_DelCoinResult_Pay_Notice 减币结果回复
message NoticeDelCoinResult{
    int32 error = 1;            //减币错误码
    int32 del_coin = 2;         //减币成功，加币数
}


message RequestSearchClubMember{
	uint32 club_id = 1;
	string find_str = 2;
	uint32 find_type = 3;
}

message ResponseSearchClubMember{
	int32 error = 1;
}

message NoticeSearchClubMember{
	repeated ClubMemberSnapshot snapshots = 1;
	uint32 find_type = 3;
}

message RequestFetchOneAnounce{
	uint32	uid = 1;	
	uint32	mail_id = 2;	
}

message ResponseFetchOneAnounce{
	uint32 error = 1;
}

message NoticeFetchOneAnounce{ 
	MailInfo oneanounce = 1;
}

message NoticeOneMail{
	MailInfo onemail = 1;
}

message NoticeWithdrawMail{
	uint32 mail_id = 1;
}

message NoticeWithdrawAnounce{
	uint32 mail_id = 1;
}

message RequestAutoAgreeClub{
    uint32   club_id = 1;
    uint32   is_agree = 2; // 0 表示false, 1 表示true
}

message ResponseAutoAgreeClub{
   uint32 error = 1;
}

message NoticeAutoAgreeClub{
       uint32   club_id = 1;
       uint32   is_agree = 2; // 0 表示false, 1 表示true
}

message RequestSetClubInvitePercent{
    uint32 club_id = 1;
    uint32 percent = 2; //百分比(100为100%)
}

message ResponseSetClubInvitePercent{
    uint32 error = 1;
    uint32 club_id = 2;
    uint32 percent = 3; //百分比(100为100%)
    bool setInvitePercentMark=4;
}

message RequestQuerySendFairReport{
	uint32 club_id = 1;			//俱乐部id
	uint64 game_uuid = 2;
	uint64 room_uuid = 3;		//roomuuid
	string room_uuid_js = 4;
	string game_uuid_js = 5;
}

message ResponseQuerySendFairReport{
	uint32 error = 1;
	uint32 isfirst = 2;			//是否首次1是0否
	uint32 isgoldenough = 3;	//金币是否足够1是0否
	uint64 chargefee = 4;		//收取费用  免费为0
	uint32 freecounts = 5; 		//剩余免费次数
	string room_uuid_js = 6;
	string game_uuid_js = 7;
}

message NoticeLogin{
	uint32 playerid = 1;	
	uint32 gameid = 2;		//表示游戏类型 2是德州 5是跑码
	uint32 roomid = 3;
}

message RequestGetWebToken{ // 获取token
    uint32 playerid = 1;
    uint32 status = 2;
    int32  type = 3;        //渠道号 目前就 2:第三方
}

message ResponseGetWebToken{
    uint32 error = 1;
    uint32 playerid = 2;
    string token = 3;
    uint32 status = 4;
}

// 德州牛仔游戏列表
message CowBoyGameListRequest {
}

message CowBoyGameListResponse {
    uint32 error = 1;
    repeated CowBoyGame games = 2;
    repeated VideoCowboyGame videoGames = 3; // 视频牛仔的列表
}

message CowBoyGame {
    uint32 roomid = 1; // 房间号
    repeated uint64 AmountLevel = 2;  // 下注级别
    uint32 playerNum = 3; // 当前房间人数
    uint32 deskType = 4; // 1 初级 2 中级 3 高级
	repeated string pictureCn = 5; // 房间说明相关的图片(中文)
	repeated string pictureEn = 6; // 房间说明相关的图片(英文)
	repeated string pictureVn = 7; // 房间图片说明(越南文)
}

// 视频牛仔游戏列表
message VideoCowboyGameListRequest {
}

message VideoCowboyGameListResponse {
    uint32 error = 1;
    repeated VideoCowboyGame games = 2;
}

message VideoCowboyGame {
    uint32 roomid = 1; // 房间号
    repeated uint64 AmountLevel = 2;  // 下注级别
    uint32 playerNum = 3; // 当前房间人数
    uint32 deskType = 4; // 1 初级 2 中级 3 高级
	repeated string pictureCn = 5; // 房间说明相关的图片(中文)
	repeated string pictureEn = 6; // 房间说明相关的图片(英文)
	repeated string pictureVn = 7; // 房间图片说明(越南文)
}

enum MsgType {
	common = 0;               		// 常规通知：系统维护等
  	medal = 1;               		// 抽红包（为满足客户端牛仔中奖在右上角特殊显示）
  	mtt_game_notify = 2;      		// mtt比赛通知
	rank_notify = 3;          		// 排行榜变更跑马灯
	mtt_NotifyGameDetail = 100; 	// mtt已結束
	star_big_tip = 303;    			// 明星桌大额跑马灯
	jackfruit_all_will = 801; 		// 波罗蜜的全胜跑马灯
	rebate_notify = 810;      		// 返利跑马灯 type 1~3 notify
	rebate_top_notify = 811;      	// 返利跑马灯 type 4 top winner notify
	rebate_surpassed_notify = 812;	// 返利跑马灯 type 4 surpassed reward notify
}

enum MttNotifyType {
	notify_type_1min                 = 0; //开赛前1分钟
	notify_type_start                = 1; //开赛前10秒
	notify_type_started              = 2; //已开赛，不能退报名费
	notify_type_blockrobot_startgame = 3; //挂机机器人开赛通知
	notify_type_blockrobot_stopgame  = 4; //挂机机器人停止报名最后一分钟通知
	notify_type_autofeed             = 5; //获得门票后自动报名成功通知
	notify_type_30min                = 6; //开赛前30分钟 (30 minutes before the start)
	notify_type_60min                = 7; //开赛前60分钟 (60 minutes before the start)
	notify_type_180min               = 8; //开赛前180分钟 (180 minutes before the start)
}

//全服跑马灯通知
message NoticeGlobalMessage{
	int32 repeat_count = 1; 			//重复播放次数
	string msg = 2;
	repeated GameId source_type = 3; 	//服务器标识
	MsgType msg_type = 4; 				//跑马灯消息类型
	uint32 mtt_id = 5;
	string mttGameName = 6; 			//mtt报名的比赛名称
    int64 mttRemainTime = 7; 			//报名的比赛剩余开始的时间(秒)
	MttNotifyType mttNotifyType = 8; 	//mtt通知类型
}

message GameStatusRequest {
	GameId id = 1; //
}

message GameStatusResponse {
	uint32 status = 1; // 1 正常   2 维护
	GameId id = 2; // 哪个游戏id
}

// 获取游戏维护状态的2版本(H5里视频牛仔和德州牛仔是独立的)
message GameStatusV2Request {
	GameId id = 1;
}

message GameStatusV2Response {
	uint32 status = 1;                   // 1 正常   2 维护
	GameId id = 2; 						 // 哪个游戏id
}


// 德州牛仔游戏列表
message HumanBoyGameListRequest {
}

message HumanBoyGameListResponse {
	uint32 error = 1;
	repeated HumanBoyGame games = 2;
}

message HumanBoyGame {
	uint32 roomid = 1; // 房间号
	repeated uint64 AmountLevel = 2;  // 下注级别
	uint32 playerNum = 3; // 当前房间人数
	uint32 deskType = 4; // 1 初级 2 中级 3 高级
	repeated string pictureCn = 5; // 房间说明相关的图片(中文)
	repeated string pictureEn = 6; // 房间说明相关的图片(英文)
	repeated string pictureVn = 7; // 房间图片说明(越南文)
}

//存金币入保险箱
message DepositInStrongboxRequest {
    uint64 amount = 1;
	int32 deposit_type = 2; //0-gold 保险箱 1-usdt 保险箱
}

message DepositInStrongboxResponse {
    uint32 error = 1;
	int64 carry_gold = 2; // deposit_type 值  0- dtb_user_main中的user_gold   1-dtb_user_main中的usdt
	int64 deposit_gold = 3;// deposit_typed 值  0-保险箱中存的金币  1-保险箱中存的usdt
	int64 operating_gold = 4; //deposit_typed 值 0-操作金币 1-操作usdt
	int32 deposit_type = 5; //0-gold 保险箱 1-usdt 保险箱
}


// 从保险箱中取金币
message TakeoutStrongboxRequest {
    uint64 amount = 1;
    string password = 2;
	int32 deposit_type = 3; //0-gold 保险箱 1-usdt 保险箱
}

message TakeoutStrongboxResponse {
    uint32 error = 1;
	int64 carry_gold = 2; //  deposit_type 值  0- dtb_user_main中的user_gold   1-dtb_user_main中的usdt
	int64 deposit_gold = 3; // deposit_typed 值  0-保险箱中存的金币  1-保险箱中存的usdt
	int64 operating_gold = 4; //deposit_typed 值 0-操作金币 1-操作usdt
	int32 deposit_type = 5; //0-gold 保险箱 1-usdt 保险箱
}


// 保险箱明细
message StrongboxDetailRequest{
	int32 deposit_type = 1; //0-gold 保险箱 1-usdt 保险箱
}

message StrongboxDetailResponse {
    uint32 error = 1;
	repeated  StrongboxDetail list = 2;
	int32 deposit_type = 3; //0-gold 保险箱 1-usdt 保险箱
}

message StrongboxDetail {
    uint32 time = 1;
    int32  type = 2; // 1:存入  2:取出
    int64  amount = 3;
    uint64 balance = 4; // 余额
}

//保险箱金额信息
message GetStrongboxInfoRequest {

}

message GetStrongboxInfoResponse {
    uint32 error = 1;
	int64 carry_gold = 2; //  dtb_user_main中的user_gold   
	int64 deposit_gold = 3; //保险箱中存的金币  
	int64 carry_usdt = 4; // dtb_user_main中的usdt
	int64 deposit_usdt = 5; // 保险箱中存的usdt
}

message LuckDraw {
    int32 id = 1;                      //唯一key
    repeated int64 amount_ranges = 2;  //游戏数值范围
    int32 index= 3;                  //中奖数值下标
    int32 hands = 4;
}

//完成一次拉霸游戏请求
message LuckDrawDoneRequest {
    int32 id = 1; //唯一key
}

message LuckDrawDoneResponse {
    int32 error = 1;
}

//拉霸信息推送
message LuckDrawNotice {
    repeated LuckDraw lucks = 1;
}


message AofThouthandRequest {
   uint32 Playerid = 1;
}

message AofThouthandResponse {
   int32 error = 1;
   int32 hand_num = 2;
   int32 LuckDrawsLen = 3;
   repeated int32 PlayerHands = 4; // 下发配置(客户端显示铜 银 金奖)
   int32 Hand_New   = 5; // 是否新产生数据
}

//jackpot数据
message JackPotAof {
    uint32 blind_level = 1;
    int64 amount = 2;
}

message JackPotListRequest {

}

//jackpot回复
message JackPotListResponse{
    repeated JackPotAof jack_pot_lst = 1;
    int64 total_amount = 2; //总数值

    repeated JackPotAof short_jack_pot_lst = 3; //短牌jackpot
    int64 short_total_amount = 4; //短牌总数值
}

//检查用户二级密码
message CheckSafeRequest {
    string safe = 1; //二级密码
}

message CheckSafeResponse {
    int32 error = 1;
}

message TurntableItem {
	uint32 amount = 1;  // 当currency_type小于3时有值,其它时候为0，抽奖金额
	int32  currency_type = 2;  // 0-金币 1-小游戏金币 2-USDT 3-实物 4-钻石
	uint32 goods_id = 3; //商品id  当currency_type等于3时有值,其它时候为0
}

message LuckTurntableDraw {
   int64 record_id  = 1;   //抽奖记录唯一id
   uint32 amount_index = 2;  //中奖金额转盘索引
   repeated TurntableItem amount_list = 3; //转盘相关金额列表
   int32  award_type = 4; //0-自动发奖 1-后台客服发奖 2-助力奖
   int32  currency_type=5;//货币类型 0-金币  1-小游戏币
   string goods_desc =6;//商品描述
   uint32 player_lottery_mode = 7;
}

message LuckTurntableDrawNotice {
   repeated LuckTurntableDraw  draw_list= 1; //转盘相关金额列表
}

message LuckTurntableResultRequest {
   int64 record_id = 1; //抽奖记录唯一id
   uint32 player_lottery_mode = 2;
}

message LuckTurntableResultResponse {
   int32 error = 1;//错误码
   int32  currency_type=2;//货币类型 0-金币  1-小游戏币
   uint32 amount =3 ;//中奖金额
   uint32 player_lottery_mode = 4;
}

message LuckTurntableResultNotice {
   int32 uid = 1;//中奖人uid
   int32  currency_type=2;//货币类型 0-金币  1-小游戏币
   uint32 amount =3 ;//中奖金额
   uint32 player_lottery_mode = 4;
}

message LuckTurntableReadyNotice  {
   uint32 left_interval_time = 1; //剩余时间
   int32 amount_list_gametype = 2;
   uint32 player_lottery_mode = 3;
}

message LuckTurntableOverNotice {
   uint32 error = 1; //错误码
   uint32 player_lottery_mode = 2;
}

message LuckTurntableStartTimeNotice{
	string title = 1; //标题
	string content = 2; //内容
	string text = 3; //红包简介
	string share_image_url = 4;//红包分享图片
	uint32 player_lottery_mode = 5;
}


message LuckTurntableEndTimeNotice{
	uint32 error = 1; //错误码
	uint32 player_lottery_mode = 2;
}

message LuckTurntableSnaplistRequest{
   uint32  lamp_cnt = 1;
   uint32  record_cnt = 2;
   uint32 player_lottery_mode = 3;
}

message LuckTurntableSnaplistResponse {
   uint32 error = 1; //错误码
}

message LuckTurntableLamp {
   uint32 game_type = 1;
   string nick_name = 2;
   uint32 amount = 3;  //商品价值
   string room_name = 4;
   int32  currency_type=5;//货币类型 0-金币  1-小游戏币 2-usdt 3-实物 4-钻石
   uint32 goods_id = 6; //实物id  
   string goods_desc=7;//实物描述,多语言(中文#英文#越南语)
   uint32 time = 8; //更新時間
   uint32 player_lottery_mode = 9;
}


message LuckTurntableData {
   uint32 seq_num = 1;
   string nick_name = 2;
   uint32 amount = 3;
   uint32 lottery_time = 4;
   int32  currency_type=5;//货币类型 0-金币  1-小游戏币 2-usdt 3-实物 4-钻石
   uint32 goods_id = 6; //实物id   
   string goods_desc=7;//实物描述,多语言(中文#英文#越南语)
   uint32 winner_type=8;//0 in normal case and 1 in usd
   uint32 player_lottery_mode = 9;
}

message LuckTurntableSnaplistNotice {
   repeated LuckTurntableLamp lamp_list = 1;
   repeated LuckTurntableData record_list = 2;
}


message LuckTurntableCountdownNotice  {
   uint32 left_interval_time = 1; //剩余时间
   uint32 player_lottery_mode = 2;
}


message BannerRequest {
	LanguageType language = 1; // 客户端语言类型
	string languageStr = 2; // 客户端语言字符串
}

message BannerResponse {
	string banner_json = 1; // json格式的banner
}

// 抽红包记录
message RedBagDrawHistory {
    uint32 rdb_id = 1;     //红包id
    uint32 uid = 2;        //抽中uid
    string name = 3;       //抽中者名称
    string head_url = 4;   //抽中者头像地址
    int64 draw_amount = 5; //抽中的红包金额
    int64 boom_amount = 6; //中雷的红包金额
    int64 draw_time = 7;   //抽红包的时间
    int64 jp_amount = 8; //中jp金额
	string rdb_name = 9; // 红包名称
	int64 boom_percent = 10; // 中雷赔率(x100)
	uint32 boom_number = 11; // 雷数
}

message RedBagInfo {
    uint32 rdb_id = 1;           //红包id
    string rdb_name = 2;         //红包名
    int64  rdb_amount_level = 3; //红包金额级别
    uint32 creater_uid = 4;      //红包主uid
    string creater_name = 5;     //红包创建者 
    string creater_head_url = 6; //红包主头像
    int64  create_time = 7;      //红包创建时间
    uint32 boom_number = 8;      //中雷数字
    uint32 status = 9;           //红包状态(0:待抽红包 1:抽完的红包 2:超时红包 3:超时红包)
    uint32 draw_count = 10;      //抽红包次数
	bool   is_drawed = 11;       // 自己是否抢过
	int64  access_time = 12;     // 最近访问时间(服务器做冷数据落地处理)
}

message CreateRedBagRequest {
    int64  rdb_amount_level = 1; //红包金额级别
    uint32 boom_number = 2;      //中雷数字
}

message CreateRedBagReponse {
	int32 error = 1;
	uint32 rdb_id = 2;           //红包id
}

message RedBagTemplet {
    int64 amount = 1;            // 红包级别
    uint32 count = 2;            // 红包可抽次数
    string name = 3;             // 红包名
    int64 min_amount = 4;        // 最小金额
    int64 max_amount = 5;        // 最大金额
	int64 required_amount = 6;   // 最小要求金额
}


//红包推送 (新红包推送/登陆或者请求全部红包信息 都是用这个结构)
message RedBagNotice{
    bool  open  = 1;                     //红包活动开关
    bool  clear = 2;                     //是否是 设置监听金额后的红包信息推送 是:清除掉原来的
    int64 listen_amount = 3;             //已设置的监听红包的金额级别
    repeated RedBagTemplet templets = 4; //可创建的红包模板
    repeated RedBagInfo    redbags = 5;  //新红包只有1条
	string title = 6; // 活动标题
	string content = 7; // 活动内容
}

message RedBagInfoRequest{

}

message RedBagInfoResponse {
	int32 error = 1;
}

//红包金额级别监听设置
message RedBagSetAmountRequest{
    int64 amount = 1;	// amount为0表示查询自己的监听金额
}
message RedBagSetAmountResponse{
	int32 error = 1;
	int64 amount = 2;
}

//红包抽奖请求 
message RedBagDrawRequest {
    uint32 rdb_id = 1;
}

message RedBagDrawResponse {
	int32  error = 1;                           //错误码 优先判断
	uint32 rdb_id = 2;                          //红包id
	uint32 status = 3;                          //红包状态(0:待抽红包 1:抽完的红包 2:超时红包 3:超时红包)
	RedBagDrawHistory historys = 4;    //抽该红包的记录(抽过的玩家有一条，红包主有多条)
}

//红包活动开启
message RedBagOpenNotice {
    bool open = 1;
    repeated RedBagTemplet templets = 2; // 可创建的红包模板
	string title = 3; // 活动标题
	string content = 4; // 活动内容
}

//红包历史记录请求
message RedBagHistoryRequest {
    uint32 rdb_id = 1;
}

// 抽中红包之后的信息
message RedBagDraewdInfo {
	uint32 rdb_id 		= 1; 	// 红包ID
	uint32 status 		= 2;    //红包状态
	string rdb_name 	= 3;	// 红包名
	uint32 boom_count 	= 4;	// 中雷次数
	int64  boom_amount 	= 5; 	// 中雷金额
	uint32 drawed_count = 6;  	// 已领取次数
	int64 drawed_amount = 7; 	// 已抽完总额
	uint32 creator_id = 8; // 红包创建者ID
}

//红包历史记录回复
message RedBagHistoryResponse {
    int32  error = 1;                           //错误码 优先判断
    uint32 rdb_id = 2;                          //红包id
    uint32 status = 3;                          //红包状态(0:待抽红包 1:抽完的红包 2:超时红包 3:超时红包)
    repeated RedBagDrawHistory historys = 4;    //抽该红包的记录(抽过的玩家有一条，红包主有多条)
	RedBagDraewdInfo info = 5;  // 抽中红包之后的信息
	RedBagInfo detail = 6;
}

//通知红包主中雷 
message NotifyRedBagBoom2Creater{
    uint32 rdb_id = 1;          //红包id
    string rdb_name = 2;        //红包名
    int64  boom_amount = 3;     //中雷金额
    uint32 boom_number = 4;     //中雷数字
	int64 boom_percent = 5; // 中雷赔付倍率(x100)
	int64 jp_amount = 7; // jackpot金额
	uint32 boom_count = 8; // 中雷人数
}

// 发红包记录
message RedBagCreateHistory {
    uint32 rdb_id = 1;			// 红包ID
	int64  create_time = 2; 	// 发红包时间
    string rdb_name = 3;		// 红包名
	int64  rdb_amount_level = 4; // 红包金额级别
	int64 left_amount = 5; 		// 剩余金额
	uint32 draw_count = 6;  	// 抽红包次数
    uint32 left_count =  7;		// 剩余抽的次数
	uint32 boom_count = 8;		// 暴雷次数
	int64  boom_amount = 9;		// 暴雷总金额
}

// 红包状态请求
message RedBagStatusRequest {
	uint32 rdb_id = 2;       //红包id
}

// 红包状态回复
message RedBagStatusResponse {
	uint32 status = 1;
	uint32 rdb_id = 2;
	bool is_drawed = 3; // 是否抢过
}

//红包抽奖请求
message AutoRedBagDrawRequest {
	uint32 auto_count = 1; // 抢1次或10次
}

message AutoRedBagDrawResponse {
	int32  error = 1;                           //错误码 优先判断
	uint32 auto_count = 2; // 抢1次或10次
	repeated RedBagDrawHistory historys = 3;    //抽该红包的记录(抽过的玩家有一条，红包主有多条)
}


// 抽中红包发给发红包的人消息
message DrawedRedBag2CreatorNotice {
	RedBagDrawHistory historys = 1;	// 抽中人的信息
	RedBagDraewdInfo info = 2;  // 抽中红包之后的信息
}

// 最近红包消息
message LastRedbagInfoRequest {

}

message LastRedbagInfoResponse {
	int32 error = 1;
	repeated RedBagCreateHistory create_historys = 2;
	repeated RedBagDrawHistory draw_historys = 3;
}

message RedBagJackpotRequest {

}

// 红包Jackpot模板
message RedBagJackpotTemplate {
	uint32 number  =1; //中的数字
	uint32 count =2;   //数字出现的个数
	int64 percent =3;  //jackpot比率
	string number_string = 4;
}

// 红包彩池金额
message RedBagJackpotAmount {
	int64 amount_level = 1; // 红包级别
	int64 jackpot_amount = 2; // 彩池
}

// 红包击中Jackpot记录
message RedBagJackpotRecord {
	int64 amount_level = 1; // 红包级别
	uint32 uid = 2; // 中奖人ID
	string nick_name = 3; // 中奖人昵称
	int64 redbag_amount = 4; // 红包金额
	int64 award_amount = 5; // 奖励金额
	int64 time = 6; // 中间时间
}

// 请求红包Jackpot信息
message RedBagJackpotInfoRequest {

}

// 返回红包Jackpot信息
message RedBagJackpotInfoResponse {
	int32 error = 1;
	repeated RedBagJackpotTemplate jackpot_templates = 2; // Jackpot模板
	repeated RedBagJackpotAmount jackpot_amount = 3; // 彩池总览
	repeated RedBagJackpotRecord jackpot_records = 4; // 中间记录

}

// 红包统计信息
message RedbagStatisticsInfoRequest {

}


// 红包统计信息返回
message RedbagStatisticsInfoResponse {
	int32 error = 1;
	int32 JoinNumber = 2; // 参与发红包人数
	int32 create_number = 3; // 发红包总数
	repeated string booms =4;
}

// 红包Jackpot更新推送
message RedbagJackpotUpdateNotice {
	int64 amount_level = 1;	// 红包级别
	int64 jp_amount = 2;	// jackpot总额
}

///////////////////////////////////////
// 抽红包记录
message RedBagMDrawHistory {
    uint32 rdb_id = 1;     //红包id
    uint32 uid = 2;        //抽中uid
    string name = 3;       //抽中者名称
    string head_url = 4;   //抽中者头像地址
    int64 draw_amount = 5; //抽中的红包金额
    int64 boom_amount = 6; //中雷的红包金额
    int64 draw_time = 7;   //抽红包的时间
    int64 jp_amount = 8; //中jp金额
	string rdb_name = 9; // 红包名称
	int64 boom_percent = 10; // 中雷赔率(x100)
}

message RedBagMInfo {
    uint32 rdb_id = 1;           //红包id
    string rdb_name = 2;         //红包名
    int64  rdb_amount_level = 3; //红包金额级别
    uint32 creater_uid = 4;      //红包主uid
    string creater_name = 5;     //红包创建者
    string creater_head_url = 6; //红包主头像
    int64  create_time = 7;      //红包创建时间
    uint32 boom_number = 8;      //中雷数字
    uint32 status = 9;           //红包状态(0:待抽红包 1:抽完的红包 2:超时红包 3:超时红包)
    uint32 draw_count = 10;      //抽红包次数
	bool   is_drawed = 11;       // 自己是否抢过
}

message CreateRedBagMRequest {
    int64  rdb_amount_level = 1; //红包金额级别
    uint32 boom_number = 2;      //中雷数字
	int64  task_id = 3;         //任务id
}

message CreateRedBagMReponse {
	int32 error = 1;
	uint32 rdb_id = 2;           //红包id
}

message RedBagMTemplet {
    int64 amount = 1;            // 红包级别
    uint32 count = 2;            // 红包可抽次数
    string name = 3;             // 红包名
    int64 min_amount = 4;        // 最小金额
    int64 max_amount = 5;        // 最大金额
	int64 required_amount = 6;   // 最小要求金额
}


//红包推送 (新红包推送/登陆或者请求全部红包信息 都是用这个结构)
message RedBagMNotice{
    bool  open  = 1;                     //红包活动开关
    bool  clear = 2;                     //是否是 设置监听金额后的红包信息推送 是:清除掉原来的
    int64 listen_amount = 3;             //已设置的监听红包的金额级别
    repeated RedBagMTemplet templets = 4; //可创建的红包模板
    repeated RedBagMInfo    RedBagMs = 5;  //新红包只有1条
	string title = 6; // 活动标题
	string content = 7; // 活动内容
}

message RedBagMInfoRequest{

}

message RedBagMInfoResponse {
	int32 error = 1;
}

//红包金额级别监听设置
message RedBagMSetAmountRequest{
    int64 amount = 1;	// amount为0表示查询自己的监听金额
}
message RedBagMSetAmountResponse{
	int32 error = 1;
	int64 amount = 2;
}

//红包抽奖请求
message RedBagMDrawRequest {
    uint32 rdb_id = 1;
}

message RedBagMDrawResponse {
	int32  error = 1;                           //错误码 优先判断
	uint32 rdb_id = 2;                          //红包id
	uint32 status = 3;                          //红包状态(0:待抽红包 1:抽完的红包 2:超时红包 3:超时红包)
	RedBagMDrawHistory historys = 4;    //抽该红包的记录(抽过的玩家有一条，红包主有多条)
}

//红包活动开启
message RedBagMOpenNotice {
    bool open = 1;
    repeated RedBagMTemplet templets = 2; // 可创建的红包模板
}

//红包历史记录请求
message RedBagMHistoryRequest {
    uint32 rdb_id = 1;
}

// 抽中红包之后的信息
message RedBagMDraewdInfo {
	uint32 rdb_id 		= 1; 	// 红包ID
	uint32 status 		= 2;    //红包状态
	string rdb_name 	= 3;	// 红包名
	uint32 boom_count 	= 4;	// 中雷次数
	int64  boom_amount 	= 5; 	// 中雷金额
	uint32 drawed_count = 6;  	// 已领取次数
	int64 drawed_amount = 7; 	// 已抽完总额
	uint32 creator_id = 8; // 红包创建者ID
	uint32 total_count = 9;  	// 红包总数
	
}

//红包历史记录回复
message RedBagMHistoryResponse {
    int32  error = 1;                           //错误码 优先判断
    uint32 rdb_id = 2;                          //红包id
    uint32 status = 3;                          //红包状态(0:待抽红包 1:抽完的红包 2:超时红包 3:超时红包)
    repeated RedBagMDrawHistory historys = 4;    //抽该红包的记录(抽过的玩家有一条，红包主有多条)
	RedBagMDraewdInfo info = 5;  // 抽中红包之后的信息
	RedBagMInfo detail = 6;
}

//通知红包主中雷
message NotifyRedBagMBoom2Creater{
    uint32 rdb_id = 1;          //红包id
    string rdb_name = 2;        //红包名
    int64  boom_amount = 3;     //中雷金额
    uint32 boom_number = 4;     //中雷数字
	int64 boom_percent = 5; // 中雷赔付倍率(x100)
	int64 jp_amount = 7; // jackpot金额
	uint32 boom_count = 8; // 中雷人数
}

// 发红包记录
message RedBagMCreateHistory {
    uint32 rdb_id = 1;			// 红包ID
	int64  create_time = 2; 	// 发红包时间
    string rdb_name = 3;		// 红包名
	int64  rdb_amount_level = 4; // 红包金额级别
	int64 left_amount = 5; 		// 剩余金额
	uint32 draw_count = 6;  	// 抽红包次数
    uint32 left_count =  7;		// 剩余抽的次数
	uint32 boom_count = 8;		// 暴雷次数
	int64  boom_amount = 9;		// 暴雷总金额
}

// 红包状态请求
message RedBagMStatusRequest {
	uint32 rdb_id = 2;       //红包id
}

// 红包状态回复
message RedBagMStatusResponse {
	uint32 status = 1;
	uint32 rdb_id = 2;
	bool is_drawed = 3; // 是否抢过
}

//红包抽奖请求
message AutoRedBagMDrawRequest {
	uint32 auto_count = 1; // 抢1次或10次
}

message AutoRedBagMDrawResponse {
	int32  error = 1;                           //错误码 优先判断
	uint32 auto_count = 2; // 抢1次或10次
	repeated RedBagMDrawHistory historys = 3;    //抽该红包的记录(抽过的玩家有一条，红包主有多条)
}


// 抽中红包发给发红包的人消息
message DrawedRedBagM2CreatorNotice {
	RedBagMDrawHistory historys = 1;	// 抽中人的信息
	RedBagMDraewdInfo info = 2;  // 抽中红包之后的信息
}

// 最近红包消息
message LastRedBagMInfoRequest {

}

message LastRedBagMInfoResponse {
	int32 error = 1;
	repeated RedBagMCreateHistory create_historys = 2;
	repeated RedBagMDrawHistory draw_historys = 3;
}

message RedBagMJackpotRequest {

}

// 红包Jackpot模板
message RedBagMJackpotTemplate {
	uint32 number  =1; //中的数字
	uint32 count =2;   //数字出现的个数
	int64 percent =3;  //jackpot比率
	string number_string = 4;
}

// 红包彩池金额
message RedBagMJackpotAmount {
	int64 amount_level = 1; // 红包级别
	int64 jackpot_amount = 2; // 彩池
}

// 红包击中Jackpot记录
message RedBagMJackpotRecord {
	int64 amount_level = 1; // 红包级别
	uint32 uid = 2; // 中奖人ID
	string nick_name = 3; // 中奖人昵称
	int64 RedBagM_amount = 4; // 红包金额
	int64 award_amount = 5; // 奖励金额
	int64 time = 6; // 中间时间
}

// 请求红包Jackpot信息
message RedBagMJackpotInfoRequest {

}

// 返回红包Jackpot信息
message RedBagMJackpotInfoResponse {
	int32 error = 1;
	repeated RedBagMJackpotTemplate jackpot_templates = 2; // Jackpot模板
	repeated RedBagMJackpotAmount jackpot_amount = 3; // 彩池总览
	repeated RedBagMJackpotRecord jackpot_records = 4; // 中间记录

}

// 红包统计信息
message RedBagMStatisticsInfoRequest {

}


// 红包统计信息返回
message RedBagMStatisticsInfoResponse {
	int32 error = 1;
	int32 JoinNumber = 2; // 参与发红包人数
	int32 create_number = 3; // 发红包总数
	repeated string booms =4;
}

message RedBagMShowUINotice {
    repeated int64 level_amout_list = 1; //红包级别金额模板
	int64 def_amout_index = 2;  //红包级别索引
	int32  def_boom_number =3;   //默认数字雷
	int32 time_out =4;          //超时时间
	int64 task_id = 5;        //任务id
}

//荣誉榜元素
//message RankData {
//	uint32 uid = 1;
//	string name = 2;
//	string head = 3;
//	int64 updateAt = 4;   // 更新时间
//	int32 rank = 5;  	  // -1表示不在榜
//	int64 profit = 6;     // 盈利值
//	int64 frequency = 7;  // 在连胜榜是	连胜次数 ，在其他榜此值为0
//}

// 请求荣耀排行榜
message GetRankRequest {
	int64 rankId = 1; // 排行榜id
	uint32 uid = 2;   // ps:不用传,备用
}


// 荣誉榜返回
message GetRankResponse {
	int32 error = 1;

	//荣誉榜元素
	//message RankData {
	//	uint32 uid = 1;
	//	string name = 2;
	//	string head = 3;
	//	int64 updateAt = 4;   // 更新时间
	//	int32 rank = 5;  	  // -1表示不在榜
	//	int64 profit = 6;     // 盈利值
	//	int64 frequency = 7;  // 在连胜榜是	连胜次数 ，在其他榜此值为0
	//  int32 plat      = 8;  // 玩家平台，有可能没有下发这个字段(老数据没有这个字段)，没有这个字段或者此字段值为0表示是自己的平台
	//}
	repeated string list = 2;  //json 串 请求排行榜的用户的数据数组 RankData
	string owner = 3;          //json 串 请求排行榜的用户的数据 RankData
}

message SetSecretKeyRequest {
	string Secret_key = 1; //通过RSA公钥加密的密钥
} 

message SetSecretKeyResponse {
	int32 error = 1; //错误码
}

enum SecretType {
	UseX= 0;      
    UseY = 1;     
	UseXY = 2;   
}


message SetSecretKeyExRequest {
    SecretType  secret_type = 1 ;
	string cli_public_key_x = 2; //客户端ECDH公钥x
	string cli_public_key_y = 3; //客户端ECDH公钥y 
}

message SetSecretKeyExResponse {
	int32 error = 1; //错误码
	SecretType  secret_type = 2 ;
	string svr_public_key_x = 3; //服务端ECDH公钥x 
	string svr_public_key_y = 4; //服务端ECDH公钥y 
}


message SetThisAreaPlayerNotice {
    uint32 uid = 1;
    bool   isBanArea = 2;
}


// 请求推广明细
message ReferralsRequest {
	uint32 uid = 1; // 从哪个uid开始拉取(如果 get_front 为true则获取大于这个uid的)
	bool get_front = 2; // 是否获取前面的
	int32 page_size = 3; // 一页多少条（<=0 默认10条）
}

message ReferralsItem {
	uint32 uid = 1; // 玩家ID
	string name = 2; // 昵称
	string head = 3; // 头像
	int64 rebate = 4; // 返利
	uint32 plat = 5; // 0. pkw 1. wpk玩家
}

message ReferralsResponse {
	int32 total = 1; // 总条数
	uint32 uid = 2; // 从哪个uid开始拉取
	bool get_front = 3; // 是否获取前面的
	int32 max_club_member = 4; // 社区人数上限
	repeated ReferralsItem list = 5; // 列表
}

message GetInviteSummaryRequest {
    uint32 uid = 1;
}

message GetInviteSummaryResponse {
    int64 last_income = 1;       //过去30天收益
    int64 total_income = 2;      //总收益
    uint32 last_referrals = 3;    //过去30天邀请的人数
    uint32 total_referrals = 4;   //邀请总人数
	int64 redeem_income =5;      //可领取收益
	bool hasClub = 6;            //是否有社区
    int64 invite_percent = 7;    //邀请人比例
}

//领取邀请收益请求
message RedeemInviteIncomeRequest {
	uint32 uid = 1;
}

message RedeemInviteIncomeResponse {
	int32 error = 1;   
}

message JoinAllianceUserCancelRequest {
	uint32 alliance_id = 1; //公会id 
	uint32 club_id = 2;     // 社区id
}

message JoinAllianceUserCancelResponse {
	int32 error = 1; 
	uint32 club_id = 2;     // 社区id
}

message PokerMasterGameListRequest {}


message PokerMasterGameListResponse {
	uint32 error = 1;
	repeated PokerMasterGame games = 2;
}

message PokerMasterGame {
	uint32 roomid = 1; // 房间号
	repeated uint64 AmountLevel = 2;  // 下注级别
	uint32 playerNum = 3; // 当前房间人数
	uint32 deskType = 4; // 1 初级 2 中级 3 高级
	repeated string pictureCn = 5; // 房间说明相关的图片(中文)
	repeated string pictureEn = 6; // 房间说明相关的图片(英文)
	repeated string pictureVn = 7; // 房间图片说明(越南文)
	repeated string pictureThai = 8; // 房间图片说明(泰文)
}


//AuthApi
message RequestAuthApi{
	int32 platform = 1; //平台(暫時1為pkw)，暫時只用作前端UI識別
	string language   = 2; // zh_CN en_US th_PH yn_TH
}

message ResponseAuthApi{
	int32 error = 1;
}

message NoticeAuthApi{
	string bl_token   = 1; //部落返回的token
	string url   = 2; //部落方的url
}

message NoticeGameMaintainStatus {
    GameId game_id = 1;
    int32 status = 2; 	// 1 正常   2 维护状态
}

message MiniGamesListRequest {}

message MiniGamesListResponse {
	uint32 error = 1;
	repeated MiniGame games = 2; // 牛仔列表
}

message MiniGame {
	uint32 roomid = 1; // 房间号
	repeated uint64 AmountLevel = 2;  // 下注级别
	uint32 playerNum = 3; // 当前房间人数
	uint32 deskType = 4; // 1 初级 2 中级 3 高级
	uint32 sourceType = 5; // 游戏id
	repeated PgGameData pgGameData = 6; // 老虎机游戏相关数据
	string topMatches = 7; // 一起看球
	bool isHot = 8; // 是否hot
	MiniLabel label = 9; // 小游戏其他标签
}

enum MiniLabel {
     MiniLabelNormal = 0; // 正常
     MiniLabelNew = 1; // 最新
}

message PgGameData {
	string gameCode = 1; // 游戏代码
	uint32 gameId = 2; // 游戏ID
	uint32 label = 3; // 标签
	string gameName = 4; // 游戏名
	int64  expire = 5; // 过期时间
	string	gameIcon = 6; // 游戏图标地址
	uint32    sortId = 7; // 游戏排序
	uint32 gameStatus = 8; //  游戏状态
	int64  createTime = 9; // 游戏创建时间（sortId相同时, creatime优先排前）
	uint32   isChamPoin = 10; // 是否锦标赛, 1 false 2 ture
}

// pg游戏标签
enum PgGameLabel {
	LabelNormal= 0; // 普通标签
	LabelPopular = 1; // 最受欢迎
	LabelMost = 2; // 最多人玩
	LabelRecommend =3;// 人工推荐
	LabelNew =4;// 最新
}

message RequestMttResult{
	int32 foreign_id = 1;
	int32 offset   = 2;
	int32 limit   = 3;
}

message ResponseMttResult{
	int32 error = 1;
}

message NoticeMttResult{
	string data   = 1;
}

message RequestMttDetail{
	int32 foreign_id = 1;
	int32 mtt_id   = 2;
}

message ResponseMttDetail{
	int32 error = 1;
}

message NoticeMttDetail{
	string data   = 1;
}

message RequestMttGameSum{
	int32 foreign_id = 1;
}

message ResponseMttGameSum{
	int32 error = 1;
}

message NoticeMttGameSum{
	string data   = 1;
}

message RequestSpinResult{	
	int32 foreign_id = 1;
	int32 offset   = 2;
	int32 limit   = 3;
	string language = 4;
}

message ResponseSpinResult{
	int32 error = 1;
}

message NoticeSpinResult{
	string data   = 1;
}

message RequestSpinDetail{
	int32 foreign_id = 1;
	string uuid   = 2;
	string language = 3;
}

message ResponseSpinDetail{
	int32 error = 1;
}

message NoticeSpinDetail{
	string data   = 1;
}

message RequestSpinGameSum{
	int32 foreign_id = 1;
}

message ResponseSpinGameSum{
	int32 error = 1;
}

message NoticeSpinGameSum{
	string data   = 1;
}


message EventReportRequest{
	uint32 roomId = 1;
	GameId sourceType = 2;
	EventType eventType = 3; // 事件类型
	string desc = 4; // 事件描述
	int32 param1 = 5;
	int32 param2= 6;
	int32 param3 = 7;
}

message EventReportRsp{
	int32 error = 1;
}

message ExchangeUserPointsRequest {
    int32 goods_id =1; //要兑换的商品id
}

message ExchangeUserPointsResponse {
    int32 error = 1; //错误码
	int32 goods_id =2; //兑换成功的商品id
	int64 real_change_user_points =3; //转换了多少积分
	int64 real_add_game_coin =4; //转换得到的小游戏金币
}

message Goods {
	int32 goods_id = 1; //商品id
	int64 cost_user_points =2; //需要的用户积分
	int64 obtain_game_coin =3; //得到的小游戏金币  
	int64 exchange_total_count = 4; //兑换总次数 
	
}

message GoodsListRequest {
	
}

message GoodsListResponse {
	int32 error = 1; //错误码
	repeated Goods goods_list = 2;
}

enum BankDetailsType {
	Gold= 0;      
    GameCoin = 1;     
	UserPoints = 2;   
	Usdt =3;
	Diamond = 4;  // 钻石
}

message BankDetailsQueryRequest {
    BankDetailsType  detail_type  = 1; //要查询的流水类型  
    bool is_prev_pull= 2; 	//向上拉(last_inc_id传数据的当前页第一条id)，还是向下拉(last_inc_id传数据当前页的最后一条id)
	uint32 pull_count = 3; //每次拉取数据个数(小于20服务器处理等于20，大于1000服务器处理等于1000)
	int64  pull_pos = 4; //查询数据位置，(服务端返回的,第一次为0) 非第一次 is_prev_pull 等于 true 传 first_inc_id ,is_prev_pull 等于 flase 传 last_inc_id
	int64 begin_time =5;   //查询起启时间，(服务端返回的,第一次为0)
	int64 end_time =6;   //查询结束时间，(服务端返回的,第一次为0)
	int64 table_suffix_time  =7;  //表名后缀时间戳，(服务端返回的,第一次为0)
}

message BankDetailsSnapshot{
	int64 amount = 1;//金额 （有正有负）
	int32 source_type = 2; //流水类型
	int64 create_time = 3; //创建时间
	
}

message BankDetailsQueryResponse{
    int32 error = 1; //错误码
	repeated BankDetailsSnapshot snapshots = 2;
	BankDetailsType  detail_type  = 3;//请求参数带回
	bool is_prev_pull= 4; //请求参数带回
	int32   total_count = 5; //符合条件数据总数
	int64 first_inc_id = 6; //第一数据的位置
	int64 last_inc_id = 7; //最后一条数据位置，(客户端请求透传数据)
	int64 begin_time =8;   //查询起启时间，(客户端请求透传数据)
	int64 end_time =9;   //查询结束时间，(客户端请求透传数据)
	int64 table_suffix_time  =10;  //表名后缀时间戳，(客户端请求透传数据)
}

message StarInfoRequest {
	repeated uint32 starIds = 1;
}

message StarInfoResponse {
	int32 error = 1; //错误码
	uint32 firstId = 2; // 顺势第一个明星
	repeated StarInfo starInfo = 3;
}

message StarInfo {
	uint32 uid = 1;
	string name = 2;
	string profilePic = 3; // 明星简介图
}


message ReceiveToolsRequest {
	uint32 toolId = 1; // 道具ID
	uint32 qty = 2; // 数量
	int64  issueTime = 3; // 发放时间(时间戳)
	string imgAddr = 4; // 中文版道具图片地址
	string imgAddrEn = 5; // 英文版道具图片地址
	int32 optId = 6; // 后台操作项id
	int32 currency_type = 7; //0-道具(门票) 1-积分 2-金币 3-小游戏币  4-usdt
}

message ReceiveToolsResponse {
	int32 error = 1; //错误码
}

message ReceiveToolsNotice {
	repeated ToolsInfo toolsInfos = 1; // 玩家可领取全部道具信息
}

message ToolsInfo {
	uint32 toolId = 1; // 道具ID
	uint32 qty = 2; // 道具数量
	int64 issueTime = 3; // 发放时间
	string imgAddr = 4; // 中文版道具图片地址
	string imgAddrEn = 5; // 英文版道具图片地址
	int32 optId = 6; // 后台操作项id
	int32 currency_type = 7; //0-道具(门票) 1-积分 2-金币 3-小游戏币  4-usdt
}

message AuthVerifyRequest {
	uint32 result = 1; // 0.成功 1.失败
}

message AuthVerifyResponse {
	int32 error = 1; //错误码
}

message GetScalerQuoteRequest {
	int32 op_type = 1; //获取汇率的类型, 0-获取人民币到Usdt汇率 1-获取usdt到人民币的汇率
}

message GetScalerQuoteResponse {
	int32	error= 1; //错误码
	int32 op_type = 2; //请求参数带回
	string	rate = 3; //汇率
}

message ExchangeCurrencyRequest {
    int32	op_type =1; //兑换操作类型, 0-人民币到Usdt的兑换 1-usdt到人民币的兑换
	int64	from_amt=2; //要兑换货币的金额  以分为单位
	bool    is_point_deduction = 3;//产生手续费是否用积分抵扣
}

message ExchangeCurrencyResponse {
	int32	error= 1; //错误码
    int32	op_type =2; //兑换操作类型, 0-人民币到Usdt的兑换 1-usdt到人民币的兑换
	int64	from_amt=3; //请求参数带回
	int64	to_amt =4; //兑换成功货币的金额 以分为单位
	string	rate  = 5; //兑换汇率
}

message GetUserMarksRequest {
	uint32 targetId = 1; // 获取谁的签名
}

message GetUserMarksResponse {
	int32 error = 1; //错误码
	uint32 targetId = 2;
	string marks = 3; // 签名
	bool   isAuthVerify = 4; // 真人验证是否通过
	int32 edit_state=5; // 0-可以修改   >0不能修改(1-修改次数达到上限   2-输入敏感字符次数达到上限)
}

message UpdateUserMarksRequest {
	string marks = 1; // 签名
	int32 op =2; //0-正常修改个性签名 1-不修改签名,客户端输入了敏感字符上报
}

message UpdateUserMarksResponse {
	int32 error = 1;
	string marks = 2;
	// 0-可以修改   >0不能修改(1-修改次数达到上限   2-输入敏感字符次数达到上限)
	int32 edit_state=3;
	//透传 0-正常修改个性签名 1-不修改签名,客户端输入了敏感字符上报
	int32 op =4;
}

//德州类买入有usdt变动时发送通知
message BuyinEventUsdtChanageNotice {
	int32	game_id= 1; //游戏服务id
	int64   usdt_subtract=2;//usdt数量减少了多少
	int64   gold_add =3;// 金币增加 当game_id菠萝蜜时  菠萝蜜分数增加了多少 
	int64   gold_real_buyin =4;//实际带入的金币
}

message QuickRaiseRequest {
	uint32 whichRaise = 1; // 3. 3个按钮 5. 5个按钮
	repeated string changeVals = 2;
	GameId game_id = 3;  // plo需要传，其他的不传也可以
	bool  isPreFlop = 4; // 是否翻前
}

message QuickRaiseResponse {
	int32 error = 1;
	uint32 whichRaise = 2; // 3. 3个按钮 5. 5个按钮
	repeated string changeVals = 3;
	bool  isPreFlop = 4;
}

//翻前恢复所有
message DefaultSettingRequest {
	uint32 whichRaise = 1; // 3. 3个按钮 5. 5个按钮
	GameId game_id = 2;    // 哪个游戏的设置,目前只是plo不一样，plo需要传下  2021.3.2
	bool isPreFlop = 3; // 是否翻前
}

message DefaultSettingResponse {
	int32 error = 1;
	uint32 whichRaise = 2; // 3. 3个按钮 5. 5个按钮
	bool isPreFlop = 3;
	repeated string defaultVal = 4; // 默认值
}

message StarAllowRequest {
	uint32 roomId = 1;
}

message StarAllowResponse {
	int32 error = 1;
	uint32 roomId = 2;
	int64 notifyTime = 3; // 开放时间
}

message StarWillBeginNotice {
	uint32 roomId = 1;
	repeated BeginStarInfo starInfo = 2; // 一张头像对应标题
	string notifyText = 3; // 通知正文(支持多语言)
}

message BeginStarInfo {
	string starPic = 1; // 明星头像图片
	string notifyTopic = 2; // 通知标题(支持多语言)
}


message UsdtExchangeConfigNotice{

}

message GetUsdtExchangeConfigRequest{

}

message GetUsdtExchangeConfigResponse{
   int32 error = 1; //错误码
   uint32 max_usdt_exchange_count =2;//每日最大兑换次数
   uint32 left_usdt_exchange_count =3;//每日剩余兑换次数
   string usdt_fee_ratio = 4; //手续费占比,带小数的字符串   范围(0 <usdt_fee_ratio<1)
   int64 point_to_usd_deduction =5;//多少积分抵扣一个usdt的手续费，放大了100倍
   uint32 usdt_exchange_interval =6;//兑换时间间隔(单位：秒)
}

message CaptchaInfo {
	uint32	code = 1;		//助力码
	int64	create_time = 2;  //助力红包生成时间 uinx时间戳
	uint32	expire_time = 3;  //过期时间单位秒
	bool    is_available  =4;//是否可领取
	uint32	help_count = 5;   //助力人数
	UserPrizes user_prizes_data = 6; //用户奖数据
	string share_image_url = 7;//红包分享图片,为空表示没有,用默认
}

message UserPrizes {
    uint32  luck_warp_type =1; //红包类型 0-金额红包 1-门票红包  
	uint32	amount = 2;   //金额 放大了100倍
	string  ticket_url =3; //门d票url
	string  ticket_name = 4;//门票名字
	uint32  ticket_count =5;//门票数量 
	string  ticket_title =6;//门票副标题
	int32   red_type = 7;//0-正常 1-分裂包
}


message HelperInfo {
    uint32  user_id =1;  //助力用户ID
	string	nick_name = 2; //助力用户昵称
	string	avatar = 3;   //助力用户头像
}

message HelpWrapInfo {
	CaptchaInfo captcha_data = 1;   //助力包信息
	repeated	HelperInfo	helper_data = 2;  //助力玩家信息	
}

message GetUserHelpWarpListRequest {
}


message GetUserHelpWarpListResponse {
	int32 error = 1; //错误码
	repeated	HelpWrapInfo	help_wrap_data = 2;  //助力红包详情数据
	int32 left_help_count = 3; //剩余帮助资数
	
}



message AddHelperRequest {
	uint32 code = 1; //助力码 
} 

message AddHelperResponse {
	int32 error = 1; //错误码
	uint32  user_id =2;  //被帮助者用户ID
	string	nick_name = 3;     	//被帮助者用户昵称
	string	avatar = 4;    //被帮助者用户头像   
	UserPrizes user_prizes_data = 5; //用户奖数据
	int64	total_history_amount = 6; //最近7天领取助力红包总金额
	int32 left_help_count = 7; //剩余帮助天数
	uint32 check_register_days =8;//要检查注册天数
	
}


message GetTotalHistoryAmountRequest {

} 

message GetTotalHistoryAmountResponse {
	int32 error = 1; //错误码
	int64	total_history_amount = 2; //最近7天领取助力红包总金额
}


message UserReceiveHelpWarpRequest {
	uint32 code = 1; //助力码 
} 

message UserReceiveHelpWarpResponse {
	int32 error = 1; //错误码
	uint32 code = 2; //助力码  
	UserPrizes user_prizes_data = 3; //用户奖数据
	int64	total_history_amount = 4; //最近7天领取助力红包总金额
}

message AddHelpWrapNotice {

}


message UserHelpWarpChangeNotice {
    HelpWrapInfo help_wrap_data  = 1;//助力成功发送给红包玩家的通知
}

message LeftHelpCountChangeNotice {
   int32 LeftHelpCount = 1;//每日助力剩余次数
}

message UserChangeLanguageRequest {
	string CurrentLanguage = 1 ;//当前客户端语言名字
	
}

message UserChangeLanguageResponse {
	int32 error = 1; //错误码
}

message LanguageItem {
    string lang = 1; // 什么语言
    string value = 2; // 语言对应的文字
}

message SportsLoginRequest {
	uint32 gameId = 1; // 游戏ID
	string matchId = 2; // 比赛ID
}

message SportsLoginResponse {
	int32 error = 1;
	string token = 2; // 玩家token
	string frontId = 3; // NBM提供标识id
	uint32 gameId = 4; // 游戏Id
	string matchId = 5; // 比赛Id
}

message SportsLeaveRequest {
}

message SportsLeaveResponse {
	int32 error = 1;
}

message GetTexasHandsRequest {
}

message GetTexasHandsResponse{
	int32 error = 1;
	int32 totalHands = 2; // 德州类游戏总手数
}

message PgLoginRequest {
}

message PgLoginResponse {
	int32 error = 1;
	string playerSession = 2; // 玩家身份令牌
	string operatorToken = 3; // 运营商独有的身份识别
}

message PgLeaveRequest {
}

message PgLeaveResponse {
	int32 error = 1;
}

message PgBonusAndFreeGamesRequest {
}

message PgBonusAndFreeGamesResponse {
	int32 error = 1;
	repeated BonusData bonus = 2; // 红利信息
	repeated FreeGamesData freeGames = 3; // 免费次数信息
	repeated PgGameData pgGames = 4;
}

message BonusData {
	uint32 bonusId = 1; // 红利游戏标识
	string bonusName = 2; // 红利游戏名称
	repeated uint32 gameIds  = 3;//游戏的唯一标识符
	float balanceAmount = 4; //未转换红利游戏的玩家余额，或红利游戏的已转换金额
	uint32 status = 5; // 红利钱包的状态 0: 无效（已取消） 1: 活跃 2: 已过期 3: 已转换 4: 已完成 5: 全新 6: 已取消用户 7: 已锁定 8: 待处理
	int64 expiredDate = 6; // 红利游戏的截止日期 （以毫秒为单位的 Unix 时间戳
}

message FreeGamesData {
	uint32 freeGameId = 1; // 免费游戏的唯一标识符
	string freeGameName = 2; // 免费游戏的名称
	repeated uint32 gameIds  = 3;//游戏的唯一标识符
	uint32 gameCount = 4; //给定免费游戏的总数
	uint32 TotalGame = 5; // 每位玩家将获得的免费游戏总数
	uint32 status = 6; // 免费游戏钱包的状态 0: 无效（已取消） 1: 活跃 2: 已过期 3: 已转换 4: 已完成 5: 全新 6: 已取消用户 7: 已锁定 8: 待处理
	int64 expiredDate = 7; // 免费游戏的截止日期 （以毫秒为单位的 Unix 时间戳
}

message RequestKYCVerificationStatus {

}
message ResponseKYCVerificationStatus {
	int32 error = 1;
	string KYCVerificationStatus = 2;  // kyc验证状态
}

message MemePokerRankRequest {
	//	int32 size = 1; // 默认50个
}

message PlayerAtomData {
	uint32 uid = 1;
	string nickname = 2;
	string thumb = 3;
}

// memePoker的排行榜
message memePokerRankData {
	uint32 uid = 1;
	string nickname = 2;
	string thumb = 3;
	int64 amount = 4;
	string marks = 5; //签名
	int64  time  = 6;
}
message MemePokerRankResponse {
	int32 error = 1;
	repeated memePokerRankData rankData = 2;
}

message MemePokerPropsListRequest {
	int32 who = 1; // 1表示自己的,0表示商店
}

message memePokerPropsData {
	int32 id = 1;        // 道具id
	string img = 2;      //  道具图片
	int64 amount = 3;    // 道具价格
	int32 total = 4;     // 道具数量
	int64 fee = 5;       // 服务费
	string desc = 6;     // 道具描述
	string name = 7;     // 道具名称
}
message MemePokerPropsListResponse {
	int32 error = 1;
	repeated memePokerPropsData list = 2;
}

message MemePokerSearchUserRequest {
	int32 uid = 1;
}

message SearchUserData {
	uint32 uid = 1;
	string nickname = 2;
	string thumb = 3;
}

message MemePokerSearchUserResponse {
	int32 error = 1;
	SearchUserData user = 2;
}

enum memePokerPropsAction {
	memePokerPropsAction_Dummy = 0;
	memePokerPropsAction_Give = 1; // 赠送
	memePokerPropsAction_Exchange = 2; //兑换
	memePokerPropsAction_Buy      = 3;  //购买
	memePokerPropsAction_Gain = 4; // 被别人赠送。
}

message MemePokerPropsActionRequest {
	memePokerPropsAction action = 1;
	int32 propsId = 2;   // 道具id
	int32 propsTotal = 3;  // 数量 道具数量
	int64 propsAmount = 4; // 道具单价   兑换或者购买时用于验证，因为可能会变化
	int64 propsFee = 5;   // 道具兑换的服务费单价  兑换时用于验证，因为服务费会变化
	string safe = 6;      // 二级密码 购买和赠送时传密码
	uint32 toUserId = 7; // 赠送给哪个玩家action为4时有效。
}

message MemePokerPropsActionResponse {
	int32 error = 1;
//	int32 propsId = 2;
	//	int32 change = 3; // 改变数量
//	int32 balance = 4; // 余额
}

message MemePokerPropsLogRequest {
	memePokerPropsAction action = 1; // 0是不限制类型，
	int32 page = 2;  // 从1开始
	int32 size = 3;  // 1页多少条
}

message MemePokerPropsLogResponse {
	int32 error = 1;
	int32 total = 2;
	int32 page = 3;
	repeated memePokerPropsActionData list = 4;     //数据
}

message memePokerPropsActionData {
	memePokerPropsAction action = 1;
	int64 time = 2;                // 发生时间

	int32 propsId =3;              // 道具id
	string propsName = 4;           // 道具名称
	int32 propsTotal = 5;          // 道具数量

	uint32 userId = 6;             // 交互玩家id
	string userNickName = 7;       // 交互玩家昵称
//	uint32 buyQuantity = 8;      // 购买数量
	int64 exchangeAmount = 9;      // 兑换得金币
	int64 buyCostAmount = 10;      // 购买时的价格金币
}

message  memePokerCoinExchangeShopGoods {
	int32 id = 1;
	int64 costDiamond = 3;                       // 花费多少砖石，
	int64 obtainCoin = 4;                        // 兑换得多少金币，
}
message MemePokerCoinExchangeShopRequest {
}
message  memePokerCoinExchangeShopResponse {
	int32 error = 1;
	repeated  memePokerCoinExchangeShopGoods list = 2;
}

message  memePokerCoinExchangeRequest {
	int32 id = 1;
	int64 costDiamond = 2;
	int64 obtainCoin = 3;
}

message  memePokerCoinExchangeResponse {
	int32 error = 1;
}

message memePokerRechargeGoods {
	int32 id = 1;
	int64 costAmount = 2;  // 充值多少
	int64 obtainDiamond = 3;  // 购买得钻石
}

message memePokerRechargeGoodsRequest {
}

message memePokerRechargeGoodsResponse {
	int32 error = 1;
	repeated  memePokerRechargeGoods list = 2;
}
message BlackJackLoginRequest {}

message BlackJackLoginResponse {
	int32 error = 1;
	string token = 2;
	string urlBlackJack = 3;
	BjConfig config = 4;
}
message NoticeOpenCalmDownWindows {
	// 小游戏需要弹，目前是百人、牛仔、扑克大师、视频牛仔。
	int32 calmDownSeconds = 2;  // 冷静多长时间 秒
	int32 numNotification = 3;  // 本次是第几次
}

message RequestCalmDownConfirm {
	bool confirm = 1;          // 点击确认，取消可以不请求直接关掉窗口好了
}

message ResponseCalmDownConfirm {
	int32 error = 1;           // 错误描述
}

message NoticeCalmDownConfirmResult {
	int64    CalmDownLeftSeconds = 1;
	int64    CalmDownDeadLineTimeStamp = 2;
	int32 	 numNotification = 3;
}

message BjConfig {
	repeated string world = 1;
	repeated string game = 2;
	repeated  string api = 3;
}

// 21点的数据
message BlackJackData {
	string token = 1;
	string urlBlackJack = 2;
	BjConfig config = 3;
}

// 部落的数据
message MttData {
	string token   = 1; //部落返回的token
	string url   = 2; //部落方的url
}

//用户位置数据
message PositionInfo{
	float longtitude = 1; //经度
	float latitude  = 2; //纬度
	string ip = 3; //ip addr
}

//支付订单请求
message NewPayOrderRequest{
   int32 pay_mode   =1;//支传方式1-apple支付 2-google支付
   string product_id  =2;//产品id
}

//支付订单回复
message NewPayOrderResponse {
	int32 error = 1;
	string pay_order_no = 2;
	string product_id  =3;//产品id
}

//支付订单确认请求
message PayOrderConfirmRequest{
  string pay_order_no  = 1; //定单号
  string receipt = 2;//支付请求数据
}

//支付订单确认回复
message PayOrderConfirmResponse {
	int32 error = 1;
	uint32 diamond_count =2;
}

message SportsMatchListRequest {
}

message SportsMatchListResponse {
   int32 error = 1;
   repeated SportsMatchData matches = 2;
}

message SportsMatchData{
   	int32 sportType = 1;
   	int32 matchId = 2;
   	int64 kickOffTime = 3;
   	string leagueName = 4;
   	string leagueNameEn = 5;
   	string homeTeamName = 6;
   	string homeTeamNameEn = 7;
   	string homeTeamIcon = 8;
   	string awayTeamName = 9;
   	string awayTeamNameEn = 10;
   	string awayTeamIcon = 11;
   	repeated Market markets = 12;
	
	uint32 sort = 13;
	uint32 marketType = 14;
	int32 homeScore = 15;
	int32 awayScore = 16;
	map<string,string> tipsContent = 17;

	message Market{
		int32 betType = 1;
		int32 marketId = 2;
		repeated Selection selections = 3;
		uint32 marketStatus = 4; 

		message Selection{
			string key = 1;
			double price = 2;
			double point = 3;
			string pointStr = 4;
		}
	}
}

message SportsQuickBetRequest {
   int32 sportType = 1;
   int32 matchId = 2;
   int32 marketId = 3;
   double price = 4;
   double point = 5;
   string key = 6;
   double betAmount = 7;
}

message SportsQuickBetResponse {
   int32 error = 1;
   double betPrice = 2;
   double currentPrice = 3;
   double maxBet = 4;
   double minBet = 5;
}

message SportsTipTemplateRequest {
}

message SportsTipTemplateResponse {
   int32 error = 1;
   SportsTipSetting tipSetting = 2;
}

message SportsTipSetting {
   bool isEnabled = 1;
   TipTemplate loginTemplate = 2;
   TipTemplate beforeStartTemplate = 3;
   TipTemplate afterStartTemplate = 4;
}

message TipTemplate {
    string en_US = 1;
    string zh_CN = 2;
    int32 displayCount = 3;
    int32 intervalTime = 4;
}

message SportsQuickBetFeatureFlagNotice {
    bool isEnabled = 1;
}

message RequestBoardVisibleSwitch{
}

message ResponseBoardVisibleSwitch{
	int32 error = 1;
	bool diamond_table_visible = 2;
}

message AutoExchangeNotice {
	int32 source_currency = 1; // currency enum
	int64 source_decrease = 2;
	int32 target_currency = 3; // currency enum
	int64 target_increase = 4;
	double currency_rate = 5;
}


enum RebateEventType {
  NONE = 0;
  TYPE_1 = 1;
  TYPE_2 = 2;
  TYPE_3 = 3;
  TYPE_4 = 4;
}

enum GeocomplyLicenseKey {
    Undefine = 0;
    Geocomply = 1;
    Ndbs = 2;
}

message ClaimRewardRequest {
  uint32 player_id   = 1; // client side don't need to pass it
  int64 event_id = 2;
  int32 bet_time_index = 3;
  int32 reward_progress_index = 4; // use -1 for type4 Surpassed reward
}

message ClaimRewardResponse {
  int32 error = 1;
  int64 event_id = 2;
  map<uint32, int64> reward_amount = 3;
}

message GetEventStatusRequest {
  uint32 player_id   = 1; // client side don't need to pass it
  RebateEventType event_type  = 2; // for dev testing
  int64 event_id  = 3; // for dev testing
}

message GetEventStatusResponse {
  message RebateSetting { // for backoffice setting
    bool stop = 1;
    int64 event_start_time = 2;
    int64 event_end_time = 3;
    RebateEventType event_type = 4;
    int64 player_bet_start_time = 5;
    int64 player_bet_end_time = 6;

    message EventType1 { // 指定时间范围内下注总量
      repeated EventData.BetTime.RewardProgress reward_progress = 1;
    }
    message EventType2 { // 指定时间范围内每天下注量
      repeated EventData.BetTime.RewardProgress reward_progress = 1;
    }
    message EventType3 { // 指定星期几完成任务获得奖励
      repeated EventData.BetTime.RewardProgress reward_progress = 1;
      repeated int32 day_of_week = 2; // 0~6 example: [0,1,2,3,4,5,6] ; Sun, Mon, ..., Sat
    }
    message EventType4 { // 限时排行榜活动
      bool is_daily = 1;
      EventDataWithType4.BetTime.SurpassedReward surpassed_reward = 2;
      repeated EventDataWithType4.BetTime.RewardProgress reward_progress = 3;
    }
    EventType1 type1 = 7;
    EventType2 type2 = 8;
    EventType3 type3 = 9;
    EventType4 type4 = 10;

    int64 trigger_marquee_reward_threshold = 100; // 領取獎勵超過多少（含）會觸發跑馬燈，單位: cents
  }
  int32 error = 1;
  int64 id = 2;
  RebateSetting setting = 3;

  int64 system_time = 4;
  EventData event_data_type1 = 5;
  EventData event_data_type2 = 6;
  EventData event_data_type3 = 7;
  EventDataWithType4 event_data_type4 = 8;
}

message EventData {
  message BetTime {
    message RewardProgress {
      int64 amount_gte = 1;
      int64 reward = 2;
      bool can_get = 3;
      bool got = 4;
	  optional uint32 currency_type = 5;
    }
    int64 start_time = 1;
    int64 end_time = 2;
    int64 betting_amount = 3;
    repeated RewardProgress reward_progress = 4;
  }
  repeated BetTime bet_time = 1;
}

message EventDataWithType4 {
  message BetTime {
    message RewardProgress {
      int64 reward = 2;
      bool can_get = 3;
      bool got = 4;
	  optional uint32 currency_type = 5;
    }
    int64 start_time = 1;
    int64 end_time = 2;
    int64 betting_amount = 3;
    repeated RewardProgress reward_progress = 4;

    message PlayerStatus {
      int32 surpassed = 1; // 0 ~ 100 win Surpassed
      int32 rank = 2;
    }
    message SurpassedReward {
      int32 surpassed_gte = 1;  // from setting. value: 0 ~ 100
      bool is_enabled = 2;
      int64 reward = 3;
      bool can_get = 4;
      bool got = 5;
      int32 top_n_can_get = 6; // 設定前幾名會得到獎勵
	  optional uint32 currency_type = 7;
    }
    message GlobalPlayerRank {
      uint32 player_id = 1;
      string avatar = 2;  // 头像地址
      string nickname = 3; //昵称
      int64 betting_amount = 4;
      uint32 plat = 5;
    }
    PlayerStatus player_status = 5; // dynamic caculate
    repeated GlobalPlayerRank global_player_rank = 6; // dynamic caculate
    SurpassedReward surpassed_reward = 7; // dynamic caculate
  }
  repeated BetTime bet_time = 1;
}

message AddBettingAmountRequest {
  uint32 player_id   = 1; // client side don't need to pass it
  int64 event_id = 2; // for dev testing
  int64 betting_amount = 3;
}
message AddBettingAmountResponse {
  int32 error = 1;
  int64 betting_amount = 2;
}

message Filter {
  message PlayerFilter {
    repeated uint32 white_list = 1;
    repeated uint32 black_list = 2;
    bool block_newly_registered_after_event_start = 3;
  }
  message GameFilter {
    repeated uint32 game_ids = 1;
  }
  message PlatFilter {
    repeated uint32 plat_ids = 1;
  }

  PlayerFilter player_filter = 1;
  GameFilter game_filter = 2;
  PlatFilter plat_filter = 3;
}

message NoticePlatformMaintenance{
	int32 status = 1;
}

enum big_reward_level{
	big_reward_level_lv0 = 0;
	big_reward_level_lv1 = 1;  
	big_reward_level_lv2 = 2; 
	big_reward_level_lv3 = 3;  
	big_reward_level_lv4 = 4;   
  }
  
  enum special_reward_type{
	special_reward_type_normal = 0;
	special_reward_type_free_spin = 1;  // free spin
	special_reward_type_bonus_game = 2; // bonus
	special_reward_type_jp_award = 3;   // hit jp
  }
  
  message MiniGameAtmosphereMessageParams {
	  uint32 player_id =1;
	  string player_name = 2;
	  string player_head = 3;
	  int64 currency_amount = 4;
	  uint32 currency_type = 5;
	  uint32 game_id = 6;
	  uint32 win_count = 7;
	  uint32 target = 8;
	  float multiplier = 9;
	  int64 loss = 10;
	  uint32 player_plat = 11;
	  string league_name = 12;
	  string team_names = 13;
	  string special_reward_name =14 ;
	  string  big_reward_name = 15 ;
	  string game_name = 16; // mutiple language json {"en":"pp_someone_game","zh":"pp某款遊戲"}
	  special_reward_type special_reward_type = 17;
	  big_reward_level big_reward_level = 18; // lv0->lv4 :大奖、幸运大奖、超级幸运大奖、超级幸运头奖、超极幸运巨奖
	  string sports_odds_name = 19;
	  uint32 sports_bets_multi_n = 20;
	  uint32 sports_bets_multi_m = 21;
  	  int64 timestamp = 22;
	  // Additional fields can be added here...
  }
  message MiniGameAtmosphereMessage {
	  uint32 template = 1;
	  uint32 time_left = 2; // seconds
	  MiniGameAtmosphereMessageParams params = 3;	
  }
  // 35209
  message MiniGamesAtmosphereNotice {	
	  MiniGameAtmosphereMessage message = 1;
  }
  
  // 35211
  message MiniGamesAtmosphereMessagesRequest {	
	  
  }
  // 35212
  message MiniGamesAtmosphereMessagesResponse {		
	  repeated MiniGameAtmosphereMessage messages = 1;
  }

message DeskSportNoticeBase{
	string moreOneHoursStr  = 1;
	string lessOneHoursStr  = 2;
	string startStr = 3;
	int64 sportStart = 4;
	int32 cdTime = 5;
	int32 showTimes = 6;
}

message DeskSportNoticeResponse{
	DeskSportNoticeBase msg = 1;
	uint32 errorCode = 2;  // not 1 is fail
}

// 35911
message SportNoticeReq {

}

message GeoComplyLicenseRequest {
	GeocomplyLicenseKey keyType = 1;
}

message GeoComplyLicenseResponse {
	int32 error = 1;
	string license = 2;
	int64 expiry = 3;
}

message GeoComplyStoreTokenRequest {
	uint32 userId = 1;	
	string payload = 2;
}

message GeoComplyStoreTokenResponse {
	int32 error = 1;
	int64 expiry = 2;
	string token = 3;
}

message GeocomplyConfigNotification {
	bool isWhitelisted = 1; // True if user is whitelisted
	bool bypassGeocomply = 2; // True if geocomply feature is disabled on server
}

enum WpkUserStatus {
    normal = 0; // default
    canSeeRoom = 1; // not ban, can't join room
    ban = 2; //not
}

// 35920
message WpkUserStasuNotice {
    WpkUserStatus status = 1;
    int64 timestamp = 2;
}

message SquidGameBlockStatusRequest {
}

message SquidGameBlockStatusResponse{
	int32 error = 1;
	uint32 squidRoomId = 2;
}

message SquidGameLeaveExistingRoomRequest {
  int32 roomId = 1;
}

message SquidGameLeaveExistingRoomResponse {
  int32 error = 1;
}

message NoticeJackpotMarquee {
    uint32 msgType = 1;
    uint32 sysMsgType = 2;
    uint32 gameId = 3;
    uint32 time = 4;
    repeated JackpotMarquee marquees = 5;
}

message JackpotMarquee {
    uint32 winnerCardType = 1;
    uint32 loserCardType = 2;
    string loserName = 3;
    uint64 amount = 4;
	uint32 currencyType = 5;
}

// 请求编辑备注
message RequestModifyRemark {
  uint32 targetId = 1;
  string remark = 2;
  uint32 type = 3;
}

message ResponseModifyRemark {
  int32 error = 1;
  uint32 targetId = 2;
  string remark = 3;
  uint32 type = 4;
  string nickname = 5;
  string avatar = 6;
  uint32 plat = 7;
}

message GeoComplyGenerateTokenNotification {
  bool generate_now = 1;
  bool auto_generate_by_client = 2;
  bool bypass = 3;
}

message RequestClubCurrentBoardPlayerCountV1 {
	FilterMode currencies_filter_mode = 1; // 預設給 0 (INCLUDE) or 1 (EXCLUDE)  , optional
	repeated CurrencyType currencies = 2; // optional
}
message ResponseClubCurrentBoardPlayerCountV1 {
	uint32 player_count = 1;
}