import { EvaluatedHand, evaluateHand, featureFlagValue, HandName, logging } from 'shared';

export interface OpponentHand {
    cardSymbols: string[];
}

interface HandEvaluation {
    cardSymbols: string[];
    evaluation: EvaluatedHand;
}

// For now this feature only support the case when hero has already folded
export const ShowCardsFeature = {
    _getCardsIdxHighestValueScenario(
        firstCardData: HandEvaluation,
        secondCardData: HandEvaluation,
        bothCardsData: HandEvaluation,
        otherPlayersData: HandEvaluation[],
        communityCards: string[],
    ): (0 | 1)[] {
        const rareCombinations = [
            HandName.THREE_OF_A_KIND,
            HandName.STRAIGHT,
            HandName.FLUSH,
            HandName.FULL_HOUSE,
            HandName.FOUR_OF_A_KIND,
            HandName.STRAIGHT_FLUSH,
        ];

        const othersMaximumValue = Math.max(
            ...otherPlayersData.map((player) => player.evaluation?.value ?? 0),
        );

        const contenders: { value: number; toShowIndexes: (0 | 1)[] }[] = [];
        if (
            firstCardData.evaluation.value > othersMaximumValue &&
            rareCombinations.includes(firstCardData.evaluation.handName)
        ) {
            logging.info('[DEBUG][SHOW_CARDS_FEATURE]- highest value with first card', {
                firstCardData,
                communityCards,
                othersMaximumValue,
            });
            contenders.push({ value: firstCardData.evaluation.value, toShowIndexes: [0] });
        }

        if (
            secondCardData.evaluation.value > othersMaximumValue &&
            rareCombinations.includes(secondCardData.evaluation.handName)
        ) {
            logging.info('[DEBUG][SHOW_CARDS_FEATURE]- highest value with second card', {
                secondCardData,
                communityCards,
                othersMaximumValue,
            });
            contenders.push({ value: secondCardData.evaluation.value, toShowIndexes: [1] });
        }

        // We prefer to show one card if they have the same value
        // So showing two only if that's necessary
        if (
            bothCardsData.evaluation.value > othersMaximumValue &&
            rareCombinations.includes(bothCardsData.evaluation.handName) &&
            bothCardsData.evaluation.value !== firstCardData.evaluation.value &&
            bothCardsData.evaluation.value !== secondCardData.evaluation.value
        ) {
            logging.info('[DEBUG][SHOW_CARDS_FEATURE]- highest value with both cards', {
                bothCardsData,
                communityCards,
                othersMaximumValue,
            });
            contenders.push({ value: bothCardsData.evaluation.value, toShowIndexes: [0, 1] });
        }

        if (contenders.length) {
            contenders.sort((a, b) => b.value - a.value);
            return contenders[0].toShowIndexes;
        }

        return [];
    },

    async showCardsIndexes(
        opponentHand: OpponentHand[],
        communityCards: string[],
        holeCards: string[],
    ): Promise<(0 | 1)[]> {
        try {
            const showCardsEnabled = await ShowCardsFeature._isEnabled();
            if (!showCardsEnabled) {
                return [];
            }

            const firstCardData: HandEvaluation = {
                cardSymbols: [holeCards[0]],
                evaluation: evaluateHand([holeCards[0]], communityCards),
            };
            const secondCardData: HandEvaluation = {
                cardSymbols: [holeCards[1]],
                evaluation: evaluateHand([holeCards[1]], communityCards),
            };

            const bothCardsData: HandEvaluation = {
                cardSymbols: holeCards,
                evaluation: evaluateHand(holeCards, communityCards),
            };

            const otherPlayersData: HandEvaluation[] = opponentHand.map((player) => ({
                cardSymbols: player.cardSymbols,
                evaluation: evaluateHand(player.cardSymbols, communityCards),
            }));

            logging.info('[DEBUG][SHOW_CARDS_FEATURE]- intermediate data', {
                firstCardData,
                secondCardData,
                bothCardsData,
                otherPlayersData,
                communityCards,
            });

            // Highest value among others
            const highestComboIndexes = ShowCardsFeature._getCardsIdxHighestValueScenario(
                firstCardData,
                secondCardData,
                bothCardsData,
                otherPlayersData,
                communityCards,
            );

            if (highestComboIndexes.length) {
                return ShowCardsFeature._returnWithProbability(highestComboIndexes);
            }

            return [];
        } catch (error) {
            logging.error('[SHOW_CARDS_FEATURE] - error in showCardsIndexes calculation', error);
            return [];
        }
    },

    async _returnWithProbability(result: (0 | 1)[]): Promise<(0 | 1)[]> {
        const probability = await ShowCardsFeature._showCardsProbability();
        if (Math.random() < probability) {
            return result;
        }

        logging.info('[DEBUG][SHOW_CARDS_FEATURE]- chance is not met', {
            probability,
        });
        return [];
    },

    async _isEnabled(): Promise<boolean> {
        return Boolean(await featureFlagValue('show-cards-feature-enabled', {}, false));
    },

    async _showCardsProbability(): Promise<number> {
        return featureFlagValue('show-cards-feature-probability', {}, 0.5);
    },
};
