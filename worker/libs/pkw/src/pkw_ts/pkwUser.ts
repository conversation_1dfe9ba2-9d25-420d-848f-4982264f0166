import { UserStatus } from "shared";

export enum UserActionEnum {
    default = 0,
    login,
    requestLobbyData,
    joinRoom,
    sitDown,
    buyin,
    rebuy,
    sitBack,
    buyOut,
    sitOut,
    leaveRoom,
    checkCurrentRoom,
}

export class User {
    status: UserStatus = UserStatus.default;

    constructor(
        public userId: number,
        public userActions: UserActionEnum[],
    ) {}

    // Add actions uniquely to the front of the queue
    prependUserActions(...actions: UserActionEnum[]): void {
        const resultActions = [];
        for (let i = 0, j = 0; i < actions.length || j < this.userActions.length; ) {
            if (i < actions.length && j < this.userActions.length) {
                if (actions[i] === this.userActions[j]) {
                    resultActions.push(actions[i]);
                    i++;
                    j++;
                    continue;
                }
            }

            if (i < actions.length) {
                resultActions.push(actions[i]);
                i++;
                continue;
            }

            if (j < this.userActions.length) {
                resultActions.push(this.userActions[j]);
                j++;
            }
        }
        this.userActions = resultActions;
    }
}
