/* eslint-disable */
import { logging } from 'shared';
import cv from '../cv';
import { ecdhHandler } from '../tools/ecdhHandler';

export class NetWorkManager {
    private static _g_instance: NetWorkManager = null;

    public static getInstance(): NetWorkManager {
        if (!this._g_instance) {
            this._g_instance = new NetWorkManager();
            this._g_instance.init();
        }
        return this._g_instance;
    }

    public OnNeedRelogin(error: number) {
        logging.withTag('SDK').info(`[SDK] OnNeedRelogin`);
        cv.MessageCenter.send('onLoginServerError', error);
    }

    public onWorldHeartBeat() {
        console.log('onWorldHeartBeat');
    }

    public init() {
        this.startWorldHeartBeat();
    }

    // public reconnectByServerFailed() {
    //     cv.netWork.close();
    //     cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
    //     this._gameHeartBeat.stop();
    // }


    // relogin if time out
    public OnWorldTimeOut() {
        logging.withTag('SDK').info('[SDK] OnWorldTimeOut: re login');
        cv.worldNet.requestLoginServer();
    }

    public Logout(isOnlyClear: boolean = false) {
        cv.netWork.disconnect();
        this.closeGameConnect();

        cv.dataHandler.clearData();
        cv.MessageCenter.send('onLogoutScene');
    }

    public closeGameConnect(willReconnect: boolean = false) {
        logging.info('[SDK] closeGameConnect');
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
        cv.roomManager.resetRoomCache();

        if (!willReconnect) {
            cv.roomManager.reset();
        }
    }

    public OnWorldServerLogin(pSend) {
        ecdhHandler.getInstance().ecdh_init();

        // for switch Line in lobby
        if (cv.config.getCurrentScene() == cv.Enum.SCENE.HALL_SCENE) {
            //解决bug:5784
            //在大厅切换线路，重新登录world服后，服务器会将secretKey清除掉。会导致重新交换key之前，客户端与服务器secretkey不一致。
            //在登录world后，重新交换这个状态设置为false。这样在大厅点切换线路，再点击房间列表，joinRoom的时候就会重新交换密钥（不重新执行ecdh_init()初始化）
            //ecdhHandler.getInstance().ecdh_setNeedGenKeyState(false);
        }

        // if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.GameId_Dummy) {
        //     // if (cv.dataHandler.getUserData().m_bIsNewRegisted) {
        //     cv.worldNet.requestDeviceInfoReport(cv.Enum.ReportType.REPORT_REGEGIST);
        //     // }
        // }

        cv.dataHandler.getUserData().isFirstLogin = false;

        if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.GameId_Dummy) {
            //  this.requestClubInfo();
        }
    }

    public OnGameServerLogin(pSend) {
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = true;
        this.startGameHeartBeat();
        // this.StartGameHeartBeat();
        cv.roomManager.RequestJoinRoom();
    }

    private _gameHeartBeatId: NodeJS.Timeout = null;
    startGameHeartBeat() {
        clearInterval(this._gameHeartBeatId);

        this._gameHeartBeatId = setInterval(() => {
            cv.gameNet.requestHeartBeat();
        }, 8000);
    }

    private _worldHeartBeatId: NodeJS.Timeout = null;
    startWorldHeartBeat() {
        clearInterval(this._worldHeartBeatId);

        this._worldHeartBeatId = setInterval(() => {
            cv.worldNet.requestHeartBeat();
        }, 3000);
    }

    public startGame() {
        logging.info('PKW.NetWorkManager start');
        cv.worldNet.initCommon(cv.Enum.GameId.World);
        cv.gameNet.initCommon(cv.Enum.GameId.Texas);
        cv.dataNet.initCommon(cv.Enum.GameId.Data);

        if (!cv.netWork.isConnected()) {
            cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
            cv.netWork.connect();
        }
    }
}
