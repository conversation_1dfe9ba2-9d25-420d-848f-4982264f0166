import { logging } from 'shared';
import cv from '../cv';

export class NetWorkProxy {
    public serverPB: any;

    /**
     * 初始化pb，注册公共消息
     * @param gameId 游戏id
     */
    public initCommon(gameId: number) {
        this.initPb(gameId);
        this.registerCommonMessage(gameId);
    }

    /**
     * 注册游戏公共消息
     * @param serverid 游戏id
     */
    private registerCommonMessage(serverid: number) {
        cv.netWork.registerMsg(
            serverid,
            cv.Enum.GATE_MSGID.CONNECT_SERVER_FAILED_NOTIFY,
            this.ConnectServerFailedNotify.bind(this),
        );
        cv.netWork.registerMsg(
            serverid,
            cv.Enum.GATE_MSGID.SERVER_CLOSE_NOTIFY,
            this.NotifyServerClose.bind(this),
        );
    }

    /**
     * 中转服传递消息失败
     * @param puf pb内容
     */
    private ConnectServerFailedNotify() {
        console.warn(
            '连接失败连接失败连接失败连接失败连接失败连接失败连接失败连接失败连接失败连接失败连接失败连接失败连接失败连接失败',
        );
        // let msg = this.decodePB("ConnectServerFailedNotify", puf, cv.gatePB);
        // if (msg) {
        //     if ((msg.ServerId != cv.Enum.GameId.World && cv.config.getCurrentScene() == cv.Enum.SCENE.HALL_SCENE) || (msg.ServerId == cv.Enum.GameId.World && cv.config.getCurrentScene() != cv.Enum.SCENE.HALL_SCENE)) return;
        //     cv.netWorkManager.reconnectByServerFailed();
        //     return;
        // }
    }

    /**
     * 服务器关闭通知
     * @param puf pb内容
     */
    private NotifyServerClose(puf: any) {
        //console.warn("Server closed Notice!!!");
        const msg = this.decodePB('ServerCloseNotify', puf, cv.gatePB);
        if (msg) {
            if (msg.ServerId == cv.Enum.GameId.World) {
                logging.withTag('SDK').info('[SDK] world server closed, msg:' + JSON.stringify(msg));

                // Do reconnect!
                // cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
                // cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
                // cv.netWorkManager.reconnectByServerFailed();
                //clearInterval(this._gameHeartBeatId);
                //cv.netWork.connectServer();
            }
        }
    }

    private initPb(gameId: number) {
        switch (gameId) {
            case cv.Enum.GameId.World:
                this.serverPB = cv.worldPB;
                break;

            case cv.Enum.GameId.Texas:
                this.serverPB = cv.gamePB;
                break;

            case cv.Enum.GameId.Data:
                this.serverPB = cv.dataPB;
                break;

            default:
                break;
        }
    }
    //! 代理消息注册
    public registerMessage(msgid: number, fn: any, serverid: number): void {
        cv.netWork.registerMsg(serverid, msgid, fn);
    }

    //! 代理消息发送
    public sendMsg(pbbuf: any, msgid: number, Roomid: number, ServerType: number, ServerId: number): boolean {
        return cv.netWork.sendMsg(pbbuf, msgid, Roomid, ServerType, ServerId);
    }

    /**
     * @function 将pb对应数据模块序列化
     * @param pbMsgName 模块名
     * @param msgData   对应的数据
     */
    public encodePB(pbMsgName: string, msgData: any): any {
        if (!this.serverPB) {
            // 地区被屏蔽serverPB没有值导致的报错修复
            return null;
        }

        const wordPbModle = this.serverPB.lookupType(pbMsgName);

        if (wordPbModle) {
            const pbbuf = wordPbModle.encode(msgData).finish();
            return pbbuf;
        }

        console.log('encodePB:Error: PB没有找到此消息==>> ', pbMsgName);

        return null;
    }

    /**
     * Parse pb and deserialize
     * @param pbMsgName Module name
     * @param puf   Raw data
     */
    public decodePB(pbMsgName: string, puf: any, serverPB?: any): any {
        serverPB = serverPB || this.serverPB;
        const ResponseType = serverPB.lookupType(pbMsgName);
        if (!ResponseType) {
            logging.withTag('SDK').warn(`[SDK] decodePB: did not find this message '${pbMsgName}'`);
            return null;
        }

        // Normalize the incoming payload to Uint8Array without unnecessary copying
        let buffer: Uint8Array;
        if (puf instanceof Uint8Array) {
            buffer = puf;
        } else if (Buffer.isBuffer(puf)) {
            buffer = puf; // Buffer extends Uint8Array in Node.js
        } else {
            // Only copy if truly needed (e.g. ArrayBuffer)
            buffer = new Uint8Array(puf);
        }

        try {
            const msg = ResponseType.decode(buffer);
            return ResponseType.toObject(msg, {
                // Assign default values to missing fields
                enums: Number, // enums as string names
                longs: Number, // longs as strings (requires long.js)
                bytes: String, // bytes as base64 encoded strings
                defaults: true, // includes default values
                arrays: true, // populates empty arrays (repeated fields) even if defaults=false
                objects: true, // populates empty objects (map fields) even if defaults=false
                oneofs: true, // includes virtual oneof fields set to the present field's name
            });
        } catch (error) {
            logging.withTag('SDK').error(`[SDK] decodePB: error decoding message '${pbMsgName}'`, error);
            return null;
        }
    }
}
