{"name": "wptgo", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc && npm run cp_other_files", "cp_other_files": "cpx 'src/**/*.{js,d.ts,proto,json,csv}' dist", "test": "tsx --test", "start": "node dist/index.js", "dev": "tsc && node dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.6.7", "bytebuffer": "^5.0.1", "crypto-js": "^4.2.0", "https-proxy-agent": "^7.0.6", "long": "^5.3.2", "protobufjs": "7.2.6", "ws": "^8.16.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/long": "^4.0.2", "@types/ws": "^8.5.10", "@types/bytebuffer": "^5.0.49", "@types/node": "^22.15.3", "cpx2": "^8.0.0", "eslint": "^9.23.1", "prettier": "^3.5.3", "typescript": "^5.3.3"}}