import * as ws_protocol from './pb/ws_protocol.js';

import { NetWork } from './NetWork';
import { SocketServerType } from './utils/Enum';
import { dataHandler } from './DataHandler';
import { NetWorkProxy } from './NetWorkProxy';
import { logging, LoginError } from 'shared';

export class WorldNetWork extends NetWorkProxy {
    private static _instance: WorldNetWork;

    public onLogon?: (token: string) => void;
    public onLogonError?: (error: Error) => void;
    public onUserData?: (data: ws_protocol.pb.INoticeGetUserData) => void;
    public onBalance?: (balance: number) => void;

    public static getInstance(): WorldNetWork {
        if (!WorldNetWork._instance) {
            WorldNetWork._instance = new WorldNetWork();
        }
        return WorldNetWork._instance;
    }

    public init() {
        this.registerMsg(ws_protocol.pb.MSGID.MsgID_Logon_Response, this.responseLoginServer.bind(this));
        this.registerMsg(ws_protocol.pb.MSGID.MsgID_GetUserData_Response, this.responseGetUserData.bind(this));
        this.registerMsg(ws_protocol.pb.MSGID.MsgID_GetUserData_Notice, this.noticeGetUserData.bind(this));
    }

    public getLogonObject(forceSharedPlayer: boolean = false): object {
        const msg: ws_protocol.pb.RequestLogon = new ws_protocol.pb.RequestLogon();
        msg.version = '2.3.44';
        msg.token = forceSharedPlayer ? dataHandler.getSharedPlayerToken() : dataHandler.getUserToken();
        msg.device_info = `{"disroot":false,"dmodel":"","dname":"wefans","duuid":"${dataHandler.getDeviceId()}","dversion":""}`;
        msg.invitation_code = '';
        msg.client_type = ws_protocol.pb.ClientType.WPTO;
        msg.CurrentLanguage = 'en_US';
        msg.os = '';
        msg.os_version = '';
        return msg;
    }

    public requestLoginServer() {
        const pbbuf = ws_protocol.pb.RequestLogon.encode(this.getLogonObject()).finish();
        NetWork.getInstance().sendMsg(
            pbbuf,
            ws_protocol.pb.MSGID.MsgID_Logon_Request,
            0,
            SocketServerType.ServerType_World_WPTO,
            SocketServerType.ServerType_WebSocketType_WORLD
        );
    }

    public responseLoginServer(puf: any, msgid: number, serverType: number) {
        const msg: ws_protocol.pb.IResponseLogon = this.decodePB(ws_protocol.pb.ResponseLogon, puf);
        logging.withTag('WPTGO').info('responseLoginServer', msg);
        if (msg?.error && msg.error !== 1) {
            if (this.onLogonError) {
                this.onLogonError(new LoginError(msg.error.toString()));
            }
            return;
        }
        if (this.onLogon) {
            this.onLogon(msg?.mttData?.token);
        }
        if (this.onUserData || this.onBalance) {
            this.requestGetUserData();
        }
    }

    public requestGetUserData() {
        const msg = { user_id: dataHandler.getUserId() };
        const pbbuf = ws_protocol.pb.RequestGetUserData.encode(msg).finish();

        return NetWork.getInstance().sendMsg(
            pbbuf,
            ws_protocol.pb.MSGID.MsgID_GetUserData_Request,
            0,
            SocketServerType.ServerType_World_WPTO,
            SocketServerType.ServerType_WebSocketType_WORLD
        );
    }

    public responseGetUserData(puf: any) {
        const msg = this.decodePB(ws_protocol.pb.ResponseGetUserData, puf);
        logging.withTag('WPTGO').info('responseGetUserData', msg);
    }

    public noticeGetUserData(puf: any) {
        const data: ws_protocol.pb.INoticeGetUserData = this.decodePB(ws_protocol.pb.NoticeGetUserData, puf);
        logging.withTag('WPTGO').info('noticeGetUserData', data);
        if (this.onUserData) {
            this.onUserData(data);
        }
        if (this.onBalance) {
            this.onBalance((data?.total_amount ?? 0) / 100); // Assuming total_amount is in cents, convert to dollars
        }
    }
}
