export class BitHandler {
    /*
     *将数据逆转
     *比如将： "101001" 变成 "100101"
     *value 要交换的数据
     *bitLen: 交换的位数
     */
    static reverse_bits(value: number, bitLen: number) {
        let uiValue = 0;
        let v = value;
        for (let i = 0; i < bitLen; i++) {
            uiValue = (uiValue << 1) + (v & 0x01);
            v >>= 1;
        }
        return uiValue;
    }

    /*
     *将32位以内的数据相邻两个位数进行交换
     *比如将： "101001" 变成 "010110"
     */
    static swapoddeven_32bits(value: number) {
        return ((value & 0xaaaaaaaa) >>> 1) | ((value & 0x55555555) << 1);
    }

    /*
     *将16位以内的数据相邻两个位数进行交换
     *比如将： "101001" 变成 "010110"
     */
    static swapoddeven_16bits(value: number) {
        return ((value & 0xaaaa) >>> 1) | ((value & 0x5555) << 1);
    }

    /*
     *将8位以内的数据相邻两个位数进行交换
     *比如将： "101001" 变成 "010110"
     */
    static swapoddeven_8bits(value: number) {
        return ((value & 0xaa) >>> 1) | ((value & 0x55) << 1);
    }

    /*
     *取字节的个高几位
     *data: 被读取的数据
     *bitSize: 被数据的总位数  8 or 16 or 32
     *readLen: 读取的位数
     */
    static readLeftBitFromByte(data: number, bitSize: number, readLen: number): number {
        return data >>> (bitSize - readLen);
    }

    /*
     *取字节的低几位
     *data: 被读取的数据
     *bitSize: 被数据的总位数  8 or 16 or 32
     *readLen: 读取的位数
     */
    static readRightBitFromByte(data: number, bitSize: number, readLen: number): number {
        let _bitV = 0xff;
        if (bitSize === 8) {
            _bitV = 0xff;
        } else if (bitSize === 16) {
            _bitV = 0xffff;
        } else if (bitSize === 32) {
            _bitV = 0xffffffff;
        }
        const mv = _bitV >>> (bitSize - readLen);
        return data & mv;
    }

    /*
     *取字节的的中间几位
     *data: 被读取的数据
     *bitSize: 被数据的总位数  8 or 16 or 32
     *startIndex: 读取的开始位数
     *endIndex：被读取数据的结束位数
     */
    static getReadMidNumFromByte(data: number, bitSize: number, startIndex: number, endIndex: number): number {
        const _data = this.readLeftBitFromByte(data, bitSize, endIndex); // 先取高几位
        const _read = this.readRightBitFromByte(_data, bitSize, endIndex - startIndex); // 再取低几位
        return _read;
    }

    /*
     * 连接合并两个二进制  比如:"1011" "0101"  合并后为 "1011 0101"
     *bin1: 二进制1
     *bin2: 二进制2
     *bin2BitSize: 二进制bin2的位数 比如："0100" "1011" 都为4位
     */
    static concatBinaryNumber(bin1: number, bin2: number, bin2BitSize: number) {
        return (bin1 << bin2BitSize) | bin2;
    }
}
