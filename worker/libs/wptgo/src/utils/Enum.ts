export enum SocketServerType {
    ServerType_None = 0,
    ServerType_World = 1,
    ServerType_Game = 2,
    ServerType_Max = 3,
    ServerType_World_WPTO = 4,
    ServerType_RANK = 101,

    ServerType_WebSocketType_None = 1000,
    ServerType_WebSocketType_WORLD = 1001,
    ServerType_WebSocketType_HOLDEM = 1002,
    ServerType_WebSocketType_HOLDEM_SNG = 1003,
    ServerType_WebSocketType_HOLDEM_MTT = 1004,
    ServerType_WebSocketType_OMAHA_SNG = 1005,
    ServerType_WebSocketType_OMAHA_MTT = 1006,
    ServerType_WebSocketType_PINEAPPLE = 1007,
    ServerType_WebSocketType_PINEAPPLE_LOOP = 1008,
    ServerType_WebSocketType_PINEAPPLE_MTT = 1009
}

export enum GameId {
    GameId_Dummy = 0,
    World = 1,
    Texas = 2,
    StarSeat = 3,
    CowBoy = 10,
    Allin = 20,
    HumanBoy = 30,
    ZoomTexas = 40,
    ZoomTexasMax = 49,
    VideoCowboy = 50,
    Bet = 60,
    PokerMaster = 70,
    Jackfruit = 80,
    Plo = 90,
    Plo5 = 91,
    Mtt = 900,
    World_WPTO = 1001,
    Data = 10101,
    Squid = 5001,
    SportsBook = 1000, // 体育竞技
    CaribbeanStud = 6001,
    CaribbeanTexasHold = 6002
}

export enum BuildVariant {
    Production = 0, // production env, debug fully disabled
    Debug = 1, // debug env, debug fully enabled
    Staging = 2 // staging env, some debug features enabled
}

// 客户端类型
// IMPORTANT!!! This affect build-default package, so any change to this enum should be reflected also there
export enum ClientType {
    Dummy = 0, // 无效的值
    Normal = 1, // pkw（c++）app
    OverSeasNormal = 2, // pkc（c++）app
    H5 = 3, // (h5版) pkw (typescript) app
    OverSeas = 4, // (H5海外版app) pkc (typescript) app
    H5WebPage = 5, // (H5网页版) pkw 网页版
    OverSeasWebPage = 6, // (h5海外缩减版网页版) pkc 网页版
    Vietnam = 7, // h5越南版
    VietnamWebPage = 8, // h5越南版网页版
    CowboyWeb = 9, // 牛仔网页版(值应为9，如果要测试暂时写5)
    Thai = 10, // 泰语版
    ThaiWebPage = 11, // 泰语网页版,
    WhisperEdition = 12, // 私语版
    PKWDesktop = 15, // pkw desktop app
    WPTO = 16 // wpto/wptg (shared player pool)
}
