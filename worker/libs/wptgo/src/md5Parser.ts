import { dataHandler } from './DataHandler';
import { aesHandler } from './plugg/aesHandler';
import { createHash  } from 'node:crypto';

function md5(str: string) {
    return createHash('md5').update(str).digest('hex');
}

export class MD5Parser {
    private static readonly KEY: string = '@lnFi8<eIKYazt:$_;MX9T/d(gk[JW3{Upcw'.substring(0, 32);

    static md5token() {
        let token = dataHandler.getUserToken();
        token = aesHandler.DecryptBase64(token, MD5Parser.KEY);
        dataHandler.setUserToken(md5(md5(token)));

        token = dataHandler.getSharedPlayerToken();
        dataHandler.setSharedPlayerToken(md5(md5(token || '')));
    }

    static hashToken(token: string): string {
        return md5(md5(aesHandler.DecryptBase64(token, MD5Parser.KEY)));
    }
}
