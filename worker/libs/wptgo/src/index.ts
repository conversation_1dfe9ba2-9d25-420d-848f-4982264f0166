import { dataHand<PERSON> } from './DataHandler';
import { WorldNetWork } from './WorldNetWork';
import { MD5Parser } from './md5Parser';
import { NetWork } from './NetWork';
import { MttUserData, PlatformUserData } from 'shared';
import { pb } from './pb/ws_protocol.js';

async function balance(url: string, userData: PlatformUserData): Promise<number> {
    return withNetwork<number>(
        url,
        userData,
        (resolve) => {
            WorldNetWork.getInstance().onBalance = resolve;
        },
    );
}

async function mttData(url: string, userData: PlatformUserData): Promise<MttUserData> {
    return withNetwork<MttUserData>(
        url,
        userData,
        (resolve) => {
            let mttToken: string;
            WorldNetWork.getInstance().onLogon = (token: string) => {
                mttToken = token;
            };
            WorldNetWork.getInstance().onUserData = (userData: pb.INoticeGetUserData) => {
                const nickname = userData.nick_name;
                resolve({
                    mtt: {
                        token: mttToken,
                    },
                    user: {
                        nickname: nickname,
                    },
                });
            };
        },
    );
}

async function withNetwork<T>(
    url: string,
    userData: PlatformUserData,
    handler: (resolve: (value: T) => void) => void,
): Promise<T> {
    return new Promise<T>((resolve, reject) => {
        setUserData(userData);
        MD5Parser.md5token();

        WorldNetWork.getInstance().onLogonError = reject;
        handler(resolve);

        WorldNetWork.getInstance().init();
        NetWork.getInstance().connectServer(url);
    }).finally(() => NetWork.getInstance().disconnect());
}

function hashToken(token: string): string {
    return MD5Parser.hashToken(token);
}

function setUserData(userData: PlatformUserData): void {
    dataHandler.setUserId(userData.userId);
    dataHandler.setUserToken(userData.token);
    dataHandler.setDeviceId(userData.deviceId);
}

export { balance, mttData, hashToken };
