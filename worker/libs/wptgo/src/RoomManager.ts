import { GameId } from './utils/Enum';

export class RoomManager {
    private static _instance: RoomManager;

    public static getInstance(): RoomManager {
        if (!RoomManager._instance) {
            RoomManager._instance = new RoomManager();
        }
        return RoomManager._instance;
    }

    public checkGameIsZoom(serverId: number): boolean {
        return serverId >= GameId.ZoomTexas && serverId <= GameId.ZoomTexasMax;
    }
}
