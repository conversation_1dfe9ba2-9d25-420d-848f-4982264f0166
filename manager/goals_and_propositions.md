# Goals
- **Strategy service 🧠**
- **Worker + Bot management + BO 🤚**
    - Features
        - Bot management
        - Bot chat with emojis
    - Reliable
        - Bot statuses are 100% correct
        - No single failure point
        - Connection Reliability
    - User account information is safe
- **Smart deployment service**😸
    - Goals
        - Make user happy
    - Features
        - Start bots based on Player Profile
        - Player satisfaction analytics

# Overview
![Architeccture overview](imgs/scheme.png)

## Functionality
##### Bot management
We can start and stop a bot.👍
##### Bot chat with emojis
Not started

## Reliability
##### Correct statuses

Bot statuses are updated in two cases:

- Manual update on starting/stopping a bot

- Periodic status updates in the update_player_states task


Currently, it is known that sometimes bot statuses are not correct. This happens when a bot gets stuck due to the following reasons that should be fixed:

- The bot is waiting in an empty room for more than 5 minutes

- The bot doesn't know what to do if there is not enough cash

- The condition for quitting the game is unclear

Additional steps to improve statuses reliability:

- Implement tests for player updates_player_states

- Implement tests for worker edge cases behavior


##### No single failure point

The Manager and Worker services are independent or at least have a small cohesion.

The Manager service is a simple stateless FastApi application designed to run in multiple instances. The Scheduler - is a background tasks designed to run on a master instance of a Manager application. If the master node is dead, the scheduler will be started on another node. To improve stability, the Scheduler could be moved to a separate service.

The Worker service is a single process service that launches bots as separate threads. It uses the BullMQ queue to keep bot execution metadata. BullMQ supports distributed execution, so potentially we can create more instances of the Worker service. However, there are some edge cases that need to be checked:

- How many threads can a single Worker instance handle? Does this mean they will respond slower with a larger amount of threads?
- Do we need a special tool to spawn more worker instances?

To improve stability, the whole system should be monitored.

##### Connection
Currently, we are fully dependent on AWS. As engineers, we can monitor metrics and take action if the response time is high. To achieve this, proper metrics should be provided.

## User Account Information
The architecture has been modified to ensure the safety of user account data. Now, all sensitive data is stored in a separate database, and only the Worker has access to it.
##### Implementation
- Remove User Accounts from the Manager✔️
- Manager Dev deployed✔️
- Add User Account functionality to the Worker✔️
- Add new ENV variables to the Worker task definition✔️
- Worker Dev deployed
- Integration tested
- Add SECRET_KEY to the AWS secret manager
- Manager Prod deployed
- Worker Prod deployed

