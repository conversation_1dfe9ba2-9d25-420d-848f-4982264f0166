# API BotManager
### Installation
We use poetry instead of traditional pip manager. To install API BotManager on Windows, follow below steps:

1. Python 3.12 is required
2. Run the following command to install Poetry
    
    ```shell
    pip install poetry
    ```
3. Add and activate virtual environment:
    
    ```shell
    python -m venv venv
    # activation example may depends on your OS
    source venv/Scripts/activate
    ```

3. Run the following command to install the project dependencies:

    ```shell
    poetry install
    ```

## Run
To simply run on local machine you can use this line:

```shell
fastapi dev src/main.py --port 8888
```

### Run with docker-compose
#### If you have **docker** and **docker-compose** installed you can you this option
```shell
docker-compose up -d
```

#### But don't forget to setup you env vars properly (see .env.dummy)
```
REDIS_HOST=host.docker.internal
```

#### To check logs you can use
```
docker-compose logs
```

or  (to utilize terminal and follow them)
```
docker-compose logs -f
```


##### VS Code launch
Use this config to launch the project locally via VS code
```json
{
    "name": "Bot<PERSON>anager",
    "type": "debugpy",
    "request": "launch",
    "module": "uvicorn",
    "args": [
        "app.main:app",
        "--reload",
        "--port",
        "8888"
    ],
    "envFile": "${workspaceFolder}/local.env"
}
```

##### Local MongoDB
```shell
docker pull mongodb/mongodb-community-server:latest
docker run --name mongodb -p 27017:27017 -d mongodb/mongodb-community-server:latest
```
Envs:
```shell
MONGODB_INITDB_ROOT_USERNAME=user
MONGODB_INITDB_ROOT_PASSWORD=psswd
```

### Migration
To create new migrations and apply them you can use beanie functions:
```shell
beanie new-migration -n 001_seed_test_data -p src/db/migrations
beanie migrate -uri '************************' -db manager -p src/db/migrations
```

### Protobuf interfaces
After updating protobuf interfaces in its repo you may run
```shell
poetry update
```
and ensure that **bot-manager-interfaces** version was updated to the latest one
