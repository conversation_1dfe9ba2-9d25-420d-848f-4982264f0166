#!/bin/bash

CPU_COUNT=$(nproc)
echo "CPU_COUNT is ${CPU_COUNT}"

WORKERS=$((CPU_COUNT * 2 + 1))
echo "uvicorn WORKERS is set to ${WORKERS}"

KEEP_ALIVE=65
LOG_CONFIG=uvicorn_logs.yaml
HOST=0.0.0.0
PORT=8888

# Start Uvicorn with the specified settings
poetry run uvicorn src.main:app --workers $WORKERS --timeout-keep-alive $KEEP_ALIVE --host $HOST --port $PORT --log-config $LOG_CONFIG --timeout-graceful-shutdown 60