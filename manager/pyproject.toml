[tool.ruff]
line-length = 100

[tool.poetry]
name = "manager"
version = "0.18.29"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
bullmq = "^2.7.8"
fastapi = "^0.111.0"
uvicorn = "^0.30.1"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
beanie = "^1.26.0"
pydantic-settings = "^2.3.3"
apscheduler = "^3.10.4"
phone-gen = "^3.0.3"
aiohttp = "^3.10.5"
motor = "^3.6.0"
boto3 = "^1.35.63"
pyjwt = "^2.10.1"
requests = "^2.32.3"
orjson = "^3.10.16"
UnleashClient = "6.2.1"
betterproto = ">=2.0.0b7"
prometheus-client = {version = "^0.22.1", source = "pypi"}

[tool.poetry.group.dev.dependencies]
flake8 = "^7.1.0"
pytest = "^8.2.2"
mongomock = "^4.1.2"
mongomock-motor = "^0.0.34"
pytest-asyncio = "^0.21.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
filterwarnings = [
    'ignore::PendingDeprecationWarning',
	'ignore::pydantic.warnings.PydanticDeprecatedSince211'
]
