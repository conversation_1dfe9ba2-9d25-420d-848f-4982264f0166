from pydantic import BaseModel

from fastapi import Query
from typing import Literal
from beanie.operators import In, RegEx, Or

from src.db.models import Player
from src.utils.enums import PlatformType, CurrencyType, PlayerStatus

SortFields = Literal[
    "created_at",
    "-created_at",
    "country_code",
    "-country_code",
    "hands_played",
    "-hands_played",
    "total_buy_in",
    "-total_buy_in",
    "last_buy_in",
    "-last_buy_in",
    "rebuy_count",
    "-rebuy_count",
    "stack",
    "-stack",
    "chips",
    "-chips",
    "rank",
    "-rank",
]


class PageQueryParams(BaseModel):
    page: int = Query(default=1, ge=1)
    page_size: int = Query(default=10, ge=1, le=100)  # page_size must be between 1 and 100


class PlayerFilterQueryParams(BaseModel):
    sort: SortFields = Query(
        default="-created_at",
        description="Sort by field, prefix with '-' for descending",
    )
    include_disabled: bool = False
    include_idle: bool = False
    status: PlayerStatus | None = None
    app_id: int | None = None
    club_id: int | None = None
    country_code: str | None = None
    platform_id: PlatformType | None = None
    text_search: str | None = None
    currency: CurrencyType | None = None
    balance_gte: int | None = None
    balance_lte: int | None = None

    def get_query(self):
        query = []
        # Filters
        if not self.include_disabled:
            query.append(Player.enabled == True)  # noqa: E712
        if not self.include_idle:
            query.append(Player.status != PlayerStatus.IDLE.value)  # noqa: E712
        if self.status is not None:
            query.append(Player.status == self.status.value)
        if self.app_id is not None:
            query.append(Player.app_id == self.app_id)
        if self.club_id is not None:
            query.append(In(Player.club_ids, [self.club_id]))
        if self.country_code is not None:
            query.append(Player.country_code == self.country_code)
        if self.platform_id is not None:
            query.append(Player.platform_id == self.platform_id.value)
        if self.text_search is not None:
            query.append(
                Or(
                    RegEx(Player.player_id, self.text_search, "i"),
                    RegEx(Player.table_id, self.text_search, "i"),
                )
            )

        # Balance filters
        currency_field_map = {
            CurrencyType.USD: Player.balance.usd,
            CurrencyType.GOLD: Player.balance.gold,
            CurrencyType.DIAMOND: Player.balance.diamond,
        }

        if self.currency is not None:
            field = currency_field_map.get(self.currency)
            if self.balance_gte is not None:
                query.append(field >= self.balance_gte)
            if self.balance_lte is not None:
                query.append(field <= self.balance_lte)

        return query
