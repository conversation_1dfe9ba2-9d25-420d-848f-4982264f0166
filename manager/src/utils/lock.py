from redis.asyncio.cluster import RedisCluster
from redis.asyncio import Redis

from src.utils.settings import settings
from src.utils.logging import logger


class RedisLockException(Exception):
    pass


class RedisLock:
    def __init__(self):
        if settings.env == "local":
            self.redis = Redis(host=settings.redis_host, port=settings.redis_port)
        else:
            self.redis = RedisCluster(host=settings.redis_host, port=settings.redis_port)
        self.prefix = "lock:"
        self.classname = "RedisLock"

    async def lock(self, key, expiration=10):
        logger.info(self.classname, f"Trying to acquire lock {key}")
        result = await self.redis.set(self.prefix + key, 1, ex=expiration, nx=True)
        if not result:
            raise RedisLockException(f"Could not acquire lock {key}")

    async def release(self, key):
        logger.info(self.classname, f"Releasing lock {key}")
        await self.redis.delete(self.prefix + key)

    async def extend(self, key, timeout=10):
        logger.debug(self.classname, f"Extending lock {key}")
        await self.redis.expire(self.prefix + key, timeout)

    async def is_locked(self, key):
        return await self.redis.exists(self.prefix + key)
