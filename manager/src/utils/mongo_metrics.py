from pymongo.monitoring import CommandListener, CommandStartedEvent
from src.utils.logging import logger


# Custom command listener to count queries
class QueryCounter(CommandListener):
    """
    A MongoDB command listener that counts and logs the number of queries executed.
    Can be used like this: client = AsyncIOMotorClient(..., event_listeners=[QueryCounter()])
    """

    def __init__(self):
        self.query_count = 0

    def started(self, event: CommandStartedEvent):
        # Increment query count for each command
        self.query_count += 1
        logger.debug(
            "mongo debug", f"Query {self.query_count}: {event.command_name} - {event.command}"
        )

    def succeeded(self, event):
        pass

    def failed(self, event):
        pass
