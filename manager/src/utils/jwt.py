from typing import Dict, List, Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt, jwk, JWTError
from pydantic import BaseModel
from starlette.requests import Request
from starlette.status import HTTP_403_FORBIDDEN

# JWK: JSON Web Key
JWK = Dict[str, str]


class JWKS(BaseModel):
    """JWKS: JSON Web Key Set"""

    keys: List[JWK]


class JWTAuthorizationCredentials(BaseModel):
    """JWTAuthorizationCredentials represents the JWT token and its components"""

    jwt_token: str
    header: Dict[str, str]
    claims: Dict[str, str]
    signature: str
    message: str


class JWTBearer(HTTPBearer):
    """JWTBearer is a subclass of HTTPBearer that verifies JWT tokens every request"""

    def __init__(self, jwks: JWKS, auto_error: bool = True):
        super().__init__(auto_error=auto_error)

        # Create a dictionary of JWKs with the key ID as the key
        self.kid_to_jwk = {jwk["kid"]: jwk for jwk in jwks.keys}

    def verify_jwk_token(self, jwt_credentials: JWTAuthorizationCredentials) -> bool:
        try:
            # Try to get the public key from the dictionary of JWKs
            public_key = self.kid_to_jwk[jwt_credentials.header["kid"]]
        except KeyError:
            raise HTTPException(
                status_code=HTTP_403_FORBIDDEN, detail="JWK public key not found"
            )

        # Construct the public key. The algorithm should a part of the public key.
        key = jwk.construct(public_key)
        decoded_signature = jwt.decode(jwt_credentials.signature.encode())
        return key.verify(jwt_credentials.message.encode(), decoded_signature)

    async def __call__(
        self, request: Request
    ) -> Optional[HTTPAuthorizationCredentials]:
        credentials: HTTPAuthorizationCredentials = await super().__call__(request)

        if credentials:
            # Check if token does not contain the Bearer scheme
            if credentials.scheme.lower() != "bearer":
                raise HTTPException(
                    status_code=HTTP_403_FORBIDDEN,
                    detail="Invalid authentication scheme",
                )

            jwt_token = credentials.credentials

            message, signature = jwt_token.rsplit(".", 1)

            # Try to decode the JWT token
            try:
                jwt_credentials = JWTAuthorizationCredentials(
                    jwt_token=jwt_token,
                    header=jwt.get_unverified_header(jwt_token),
                    claims=jwt.get_unverified_claims(jwt_token),
                    signature=signature,
                    message=message,
                )
            except JWTError:
                raise HTTPException(
                    status_code=HTTP_403_FORBIDDEN, detail="JWK invalid"
                )

            # Verify the JWT token
            if not self.verify_jwk_token(jwt_credentials):
                raise HTTPException(
                    status_code=HTTP_403_FORBIDDEN, detail="JWK invalid"
                )

            return jwt_credentials
