from typing import Optional
from UnleashClient import UnleashClient
from src.utils.settings import settings
from src.utils.logging import logger
import json

name = "feature_flags"


class FeatureFlagClient:
    _instance = None
    _client: Optional[UnleashClient] = None

    def initialize_client(self):
        if not settings.enable_feature_flags:
            logger.info(name, "Feature flags are disabled, skipping client initialization")
            return
        try:
            self._client = UnleashClient(
                url=settings.unleash_api_url,
                app_name="manager",
                custom_headers={
                    "Authorization": settings.unleash_api_token
                }
            )
            self._client.initialize_client()
            logger.info(name, "Unleash client initialized successfully")
        except Exception as e:
            logger.error(name, "Failed to initialize Unleash client", e)
            raise

    def is_initialized(self) -> bool:
        return self._client is not None and self._client.is_initialized

    def get_flag(self, flag_key: str, default, context_dict: dict | None = None):
        """
        Example usage:
        self.get_flag("manager-stuck-job-timeout", 300)  # returns 3600
        self.get_flag("manager-stuck-job-timeout", 300, { "bot_type": "play" })  # returns 3600
        self.get_flag("manager-stuck-job-timeout", 300, { "bot_type": "scan" })  # returns 60, because has separate strategy in UI
        self.get_flag("flag_not_created_in_ui", 300)  # returns 300

        """
        if not self.is_initialized():
            logger.debug(name, "Client not initialized, returning default")
            return default
        try:
            result = self._client.get_variant(flag_key, context_dict)
            if not result.get("enabled"):
                logger.debug(name, f"Flag {flag_key} is not enabled, returning default")
                return default

            match result["payload"].get("type"):
                case "string":
                    return result["payload"].get("value", default)
                case "number":
                    return float(result["payload"].get("value", default))
                case "json":
                    return json.loads(result["payload"].get("value", default))
                case _:
                    logger.warning(name, f"Unknown flag type for {flag_key}, returning default")
                    return default

        except Exception as e:
            logger.error(name, f"Error evaluating flag {flag_key}", e)
            return default

    def close(self):
        if self._client:
            self._client.destroy()
            self._client = None


class MockFeatureFlagClient:
    def initialize_client(self):
        logger.info('MockFeatureFlagClient', "initialized mock client, Unleash API URL is not set")

    def is_initialized(self) -> bool:
        return True

    def get_flag(self, flag_key: str, default, context_dict: dict | None = None):
        logger.debug('MockFeatureFlagClient', f"returning default for flag {flag_key}")
        return default

    def close(self):
        pass


# Global instance
feature_flags = FeatureFlagClient() if settings.unleash_api_url else MockFeatureFlagClient()
