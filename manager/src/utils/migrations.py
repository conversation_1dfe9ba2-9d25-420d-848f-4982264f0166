from datetime import datetime, timezone, timedelta

from beanie import BulkWriter
from beanie.operators import Or

from src.utils.enums import PlatformType, AppType
from src.db.models import RegistrationAutomationConfig, Launch, Player, Ticket
from src.utils.logging import logger


async def add_platform_to_players():
    name = "tasks.add_platform_to_players"
    logger.debug(name, "Adding platform_id to players...")

    async with BulkWriter() as bulk_writer:
        players = await Player.find(
            Or(
                Player.app_id == AppType.R4.value,
                Player.platform_id == None,  # noqa: E711
            )
        ).to_list()
        for player in players:
            if player.app_id == AppType.R4.value:
                # R4 is the only app that uses RWPK platform
                player.platform_id = PlatformType.RWPK.value

            elif player.platform_id is None:
                # Default to WPK platform if not set
                player.platform_id = PlatformType.WPK.value
                await player.save()

        logger.debug(name, f"Updated {len(players)} players with platform_id")
        await bulk_writer.commit()


async def create_registration_automation_config():
    name = "tasks.create_registration_automation_config"
    logger.debug(name, "Checking if registration configuration exists...")
    config = await RegistrationAutomationConfig.find().first_or_none()
    if not config:
        logger.info(name, "Registration configuration not found. Creating default configuration...")
        config = RegistrationAutomationConfig(config_id=1, is_enabled=False, min_delay_sec=30, max_delay_sec=60)
        await config.insert()


async def delete_old_launches():
    name = "tasks.delete_old_launches"

    threshold_date = datetime.now(timezone.utc) - timedelta(days=30)
    old_launches = await Launch.find(Launch.created_at < threshold_date).count()

    logger.debug(name, f"Deleting {old_launches} old launches")

    await Launch.find(Launch.created_at < threshold_date).delete()


async def set_player_allowed_games_from_app_id():
    name = "tasks.set_player_allowed_games_from_app_id"
    logger.debug(name, "Setting allowed games for players based on app_id...")

    async with BulkWriter() as bulk_writer:
        players = await Player.find(
            Player.allowed_games == [],
        ).to_list()
        for player in players:
            if player.app_id and not player.allowed_games:
                player.allowed_games = [AppType(player.app_id)]
                await player.save()

        logger.debug(name, f"Updated {len(players)} players with allowed games from app_id")
        await bulk_writer.commit()


async def fix_invalid_ticket_data():
    """
    Find and fix players with invalid ticket data in their balance.

    This function:
    1. Finds players with tickets that have invalid structures
    2. Attempts to fix or remove invalid tickets
    3. Updates the players in the database
    """
    name = "utils.fix_invalid_ticket_data"
    logger.info(name, "Starting ticket data migration...")

    players = await Player.find(
        Player.balance.tickets != None,  # noqa: E711
        Player.balance.tickets != [],
    ).to_list()

    players_to_update = []
    total_invalid_tickets = 0

    for player in players:
        original_tickets = player.balance.tickets
        valid_tickets = []
        invalid_tickets = []

        for ticket in original_tickets:
            if _is_valid_ticket(ticket):
                valid_tickets.append(ticket)
            else:
                invalid_tickets.append(ticket)
                total_invalid_tickets += 1

        if invalid_tickets:
            logger.warning(name, f"Player {player.player_id} has {len(invalid_tickets)} invalid tickets: {invalid_tickets}")

            player.balance.tickets = valid_tickets
            player.need_balance_update = True
            players_to_update.append(player)

    if players_to_update:
        logger.info(name, f"Updating {len(players_to_update)} players to remove {total_invalid_tickets} invalid tickets")

        async with BulkWriter():
            for player in players_to_update:
                await player.save()

        logger.info(name, "Ticket data migration completed successfully")
    else:
        logger.info(name, "No invalid ticket data found")


def _is_valid_ticket(ticket) -> bool:
    try:
        if isinstance(ticket, Ticket):
            return True

        if isinstance(ticket, dict):
            has_ticket_id = "ticket_id" in ticket
            has_tool_id = "tool_id" in ticket
            has_invalid_fields = "amount" in ticket

            return has_ticket_id and has_tool_id and not has_invalid_fields

        return False
    except Exception:
        return False
