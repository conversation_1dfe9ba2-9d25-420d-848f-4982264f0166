from datetime import datetime, timezone, timedelta

from beanie import BulkWriter
from beanie.operators import Or, In

from src.utils.enums import PlatformType, AppType
from src.db.models import RegistrationAutomationConfig, Launch, Player
from src.utils.enums import PlayerStatus
from src.utils.logging import logger


async def add_platform_to_players():
    name = "tasks.add_platform_to_players"
    logger.debug(name, "Adding platform_id to players...")

    async with BulkWriter() as bulk_writer:
        players = await Player.find(
            Or(
                Player.app_id == AppType.R4.value,
                Player.platform_id == None  # noqa: E711
            )
        ).to_list()
        for player in players:
            if player.app_id == AppType.R4.value:
                # R4 is the only app that uses RWPK platform
                player.platform_id = PlatformType.RWPK.value

            elif player.platform_id is None:
                # Default to WPK platform if not set
                player.platform_id = PlatformType.WPK.value
                await player.save()

        logger.debug(name, f"Updated {len(players)} players with platform_id")
        await bulk_writer.commit()


async def create_registration_automation_config():
    name = "tasks.create_registration_automation_config"
    logger.debug(name, "Checking if registration configuration exists...")
    config = await RegistrationAutomationConfig.find().first_or_none()
    if not config:
        logger.info(name, "Registration configuration not found. Creating default configuration...")
        config = RegistrationAutomationConfig(
            config_id=1, is_enabled=False, min_delay_sec=30, max_delay_sec=60
        )
        await config.insert()


async def finish_all_old_unifinished_launches():
    name = "tasks.finish_all_old_launches"
    logger.debug(name, "Finishing all old launches...")

    async with BulkWriter() as bulk_writer:
        # Before we introduced Launch.finished, we marked launches as finished by setting updated_at
        old_launches = await Launch.find(
            Launch.updated_at != None,  # noqa: E711
            Or(
                Launch.finished == False,  # noqa: E712
                Launch.finished == None  # noqa: E711
            ),
        ).to_list()

        for launch in old_launches:
            launch.finished = True
            await launch.save()

        logger.debug(name, f"Marked {len(old_launches)} old launches with updated_at as finished")

        # There can be still very old launches without updated_at, and without active bot
        potentially_unfinished_launches = await Launch.find(
            Launch.updated_at == None,  # noqa: E711
        ).to_list()

        player_ids = [launch.player_id for launch in potentially_unfinished_launches]
        if player_ids:
            idle_players = await Player.find(
                In(Player.player_id, player_ids),
                Player.status == PlayerStatus.IDLE.value,
            ).to_list()

            idle_player_ids = [player.player_id for player in idle_players]

            finished_unmarked_launches = await Launch.find(
                Launch.updated_at == None,  # noqa: E711
                In(Launch.player_id, idle_player_ids)
            ).to_list()

            for launch in finished_unmarked_launches:
                launch.finished = True
                launch.updated_at = datetime.now(timezone.utc)
                await launch.save()

        logger.debug(name, f"Marked {len(finished_unmarked_launches)} launches without updated_at for idle players as finished")

        await bulk_writer.commit()


async def delete_old_launches():
    name = "tasks.delete_old_launches"
    logger.debug(name, "Deleting old launches...")

    threshold_date = datetime.now(timezone.utc) - timedelta(days=90)
    old_launches = await Launch.find(Launch.created_at < threshold_date).count()

    logger.debug(name, f"Deleting {old_launches} old launches")

    await Launch.find(Launch.created_at < threshold_date).delete()

    logger.debug(name, "Old launches deleted successfully")


async def set_player_allowed_games_from_app_id():
    name = "tasks.set_player_allowed_games_from_app_id"
    logger.debug(name, "Setting allowed games for players based on app_id...")

    async with BulkWriter() as bulk_writer:
        players = await Player.find(
            Player.allowed_games == [],
        ).to_list()
        for player in players:
            if player.app_id and not player.allowed_games:
                player.allowed_games = [AppType(player.app_id)]
                await player.save()

        logger.debug(name, f"Updated {len(players)} players with allowed games from app_id")
        await bulk_writer.commit()
