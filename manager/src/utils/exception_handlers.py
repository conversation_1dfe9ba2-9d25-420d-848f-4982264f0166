# exception_handlers.py
from fastapi import Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import ORJSONResponse
from starlette.status import HTTP_422_UNPROCESSABLE_ENTITY


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    try:
        # This covers standard fast api errors
        errors = exc.errors()
        error_message = "; ".join(
            f"{'.'.join(map(str, err['loc']))}: {err['msg']}" for err in errors
        )
    except Exception:
        # Fallback in case of unexpected error structure
        error_message = str(exc)

    return ORJSONResponse(
        status_code=HTTP_422_UNPROCESSABLE_ENTITY, content={"error": error_message}
    )
