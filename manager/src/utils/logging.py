import logging
import functools
import time


DEFAULT_LOG_FORMAT = "%(levelname)-8s %(asctime)s %(name)-20s %(message)s"


class Logger:
    def __init__(self) -> None:
        self.logger = logging.getLogger("manager-api")
        self.logger.setLevel(logging.DEBUG)
        self.formatter = logging.Formatter(DEFAULT_LOG_FORMAT)

        # Create console handler
        self.ch = logging.StreamHandler()
        self.ch.setLevel(logging.DEBUG)
        self.ch.setFormatter(self.formatter)
        self.logger.addHandler(self.ch)

    def info(self, name: str, message: str):
        self.logger.name = name
        self.logger.info(message)

    def debug(self, name: str, message: str):
        self.logger.name = name
        self.logger.debug(message)

    def error(self, name: str, message: str, exception_str: str | Exception = "No exception provided"):
        self.logger.name = name
        if isinstance(exception_str, Exception):
            exception_str = str(exception_str)
        self.logger.error(f"{message} - {exception_str}")

    def warning(self, name: str, message: str):
        self.logger.name = name
        self.logger.warning(message)


def setup_uvicorn_logging():
    uvicorn_log_handler = logging.getLogger("uvicorn.access").handlers[0]
    uvicorn_log_handler.setLevel(logging.INFO)
    uvicorn_log_handler.setFormatter(logging.Formatter(DEFAULT_LOG_FORMAT))


def log_duration(func):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_ts = time.time()
        result = await func(*args, **kwargs)
        dur = time.time() - start_ts
        logger.info(func.__name__, f"execution_duration={dur:.3f}s")
        return result

    return wrapper


logger = Logger()
