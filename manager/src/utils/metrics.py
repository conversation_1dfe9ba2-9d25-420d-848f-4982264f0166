import time
from prometheus_client import Counter, Histogram
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request, Response


REQUEST_LATENCY = Histogram(
    "manager_api_request_latency_seconds", "API response time", ["method", "endpoint"]
)

REQUEST_COUNT = Counter(
    "manager_api_requests_total",
    "Total API requests (success + errors)",
    ["method", "endpoint", "status_code"],
)


ENDPOINTS_TO_IGNORE = {"/metrics", "/docs", "/openapi.json", "/redoc"}


class MetricsMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.perf_counter()

        # Normalized endpoint
        route = request.scope.get("route")
        endpoint = getattr(route, "path", request.url.path)
        method = request.method

        # Exclude certain endpoints from metrics
        if endpoint.removeprefix("/api") in ENDPOINTS_TO_IGNORE:
            return await call_next(request)

        try:
            response: Response = await call_next(request)
        except Exception as exc:
            process_time = time.perf_counter() - start_time
            REQUEST_LATENCY.labels(method=method, endpoint=endpoint).observe(process_time)
            REQUEST_COUNT.labels(method=method, endpoint=endpoint, status_code=500).inc()
            raise exc from None

        # Latency
        process_time = time.perf_counter() - start_time
        REQUEST_LATENCY.labels(method=method, endpoint=endpoint).observe(process_time)

        # Update counters
        REQUEST_COUNT.labels(
            method=method, endpoint=endpoint, status_code=response.status_code
        ).inc()

        return response
