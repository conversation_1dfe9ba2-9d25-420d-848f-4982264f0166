import aiohttp
from src.utils.logging import logger


async def form_post(url, data):
    async with aiohttp.ClientSession() as session:
        form_data = aiohttp.FormData()
        logger.debug("form_post", f"Posting data to {url}")
        for key, value in data.items():
            form_data.add_field(key, value)
        async with session.post(url, data=form_data) as response:
            response_json = await response.json()
            logger.debug(
                "form_post",
                f"Response status: {response.status} - {response_json}",
            )
            return response_json


async def post(url, data):
    async with aiohttp.ClientSession() as session:
        logger.debug("post", f"Posting data to {url}")
        async with session.post(url, json=data) as response:
            response_json = await response.json()
            logger.debug(
                "post",
                f"Response status: {response.status} - {response_json}",
            )
            return response_json


async def get(url, params=None):
    async with aiohttp.ClientSession() as session:
        logger.debug("get", f"Getting data from {url} with params: {params}")
        async with session.get(url, params=params) as response:
            response_json = await response.json()
            logger.debug(
                "get",
                f"Response status: {response.status} - {response_json}",
            )
            return response_json
