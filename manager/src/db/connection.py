from beanie import init_beanie, Document
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase

from src.utils.settings import Settings
from src.utils.logging import logger

from src.db.models import (
    AutoStartConfig,
    AutoStartAction,
    Player,
    Table,
    Tournament,
    Launch,
    FutureLaunch,
    FutureTournamentCheck,
    MultiflightConfiguration,
    TournamentConfiguration,
    IPPoolConfiguration,
    TablesAutomationConfig,
    TaskLaunch,
    RegistrationAutomationConfig,
    PendingRegistration,
)

document_models = [
    AutoStartConfig,
    AutoStartAction,
    Player,
    Table,
    Tournament,
    Launch,
    FutureLaunch,
    FutureTournamentCheck,
    MultiflightConfiguration,
    TournamentConfiguration,
    IPPoolConfiguration,
    TablesAutomationConfig,
    TaskLaunch,
    RegistrationAutomationConfig,
    PendingRegistration,
]


async def init_db(settings: Settings) -> AsyncIOMotorClient:
    name = "db.connection.init_db"
    logger.info(name, "Initializing database connection...")
    mongo_ssl_configuration = (
        f"ssl=true&ssl_ca_certs={settings.mongo_ssl_cert}&"
        if not settings.mongo_ssl_disabled
        else ""
    )
    if settings.mongo_user and settings.mongo_password:
        connection_string = (
            f"mongodb://{settings.mongo_user}:{settings.mongo_password}"
            f"@{settings.mongo_host}:{settings.mongo_port}/"
            f"?{mongo_ssl_configuration}tls=true&retryWrites=false"
        )
    else:
        connection_string = (
            f"mongodb://{settings.mongo_host}:{settings.mongo_port}/"
            f"?{mongo_ssl_configuration}retryWrites=false"
        )

    client = AsyncIOMotorClient(connection_string)
    database = client[settings.db_name]

    await init_collections(database, document_models)

    await init_beanie(
        database=database,
        document_models=document_models,
    )

    logger.info(name, "Database connection initialized!")

    return client


async def init_collections(database: AsyncIOMotorDatabase, document_models: list[Document]):
    collections = await database.list_collection_names()
    for document_model in document_models:
        if document_model.Settings.name not in collections:
            await create_collection(database, document_model)


async def create_collection(database: AsyncIOMotorDatabase, document_model: Document):
    name = "db.connection.create_collection"

    logger.info(name, f"Creating collection for {document_model.Settings.name}...")

    await database.create_collection(document_model.Settings.name)
