import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parents[3]))  # Add the path to the 'src' module

from beanie import free_fall_migration

from src.db.models import (
    GamingConfiguration,
    App,
    Player,
    UserID,
    Table,
)


class Forward:
    @free_fall_migration(
        document_models=[
            App,
            GamingConfiguration,
            Player,
            UserID,
            Table,
        ]
    )
    async def seed_data(self, session):
        app = App(app_id=1, name="Test App")
        await app.insert()

        gaming_configuration = GamingConfiguration(
            game="Texas Hold'em",
            blinds=[1, 2],
            ante=False,
            straddle=False,
        )
        await gaming_configuration.insert()

        player = Player(
            player_id=1,
            app_id=app.app_id,
            enabled=True,
            status="active",
            bot_id=1,
            table_id=20,
        )
        await player.insert()

        table = Table(
            table_id="table1",
            app_id=app.app_id,
            gaming_configuration=gaming_configuration,
            currency="USD",
            empty_seats=5,
        )
        await table.insert()


class Backward: ...
