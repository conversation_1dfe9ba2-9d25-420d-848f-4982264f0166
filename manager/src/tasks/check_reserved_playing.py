from datetime import datetime, timedelta, timezone
from beanie.operators import In
from src.db.models import FutureLaunch, Player
from src.services.bots import BotService
from src.utils.enums import PlayerStatus
from src.utils.logging import logger


async def check_reserved_playing():
    """
    Checks players and turns off players that are reserved for a game.
    """
    name = "tasks.check_reserved_playing"
    # Find future launches that needs to be checked
    max_booked_launch_time = datetime.now(timezone.utc) + timedelta(hours=1)
    future_launches = await FutureLaunch.find(
        FutureLaunch.starting_time <= max_booked_launch_time
    ).to_list()
    future_launches_dict = {launch.player_id: launch for launch in future_launches}

    # Find active players that are not idle
    active_players = await Player.find(
        In(Player.player_id, [launch.player_id for launch in future_launches]),
        Player.status != PlayerStatus.IDLE.value,
    ).to_list()

    logger.info(
        name,
        f"Found {len(future_launches)} future launches and {len(active_players)} active players.",
    )

    # Check each active player against their future launch
    for player in active_players:
        future_launch = future_launches_dict.get(player.player_id)
        player_runs_on_proper_tournament = (
            player.app_id == future_launch.app_id and player.table_id == future_launch.tournament_id
        )
        if player_runs_on_proper_tournament:
            # This player is running properly
            pass
        else:
            reason = f"Player {player.player_id} is reserved for a game and has to be stopped. Table ID: {player.table_id}, Bot ID: {player.bot_id}"
            logger.info(name, reason)
            await BotService.stop_bot(player.bot_id, reason=reason)
