from pydantic import BaseModel
from src.utils.enums import TournamentStatus
from src.db.models import AutoStartConfig, Tournament, MultiflightConfiguration
from src.services.multi_flight_configuration import MultiflightConfigurationService
from src.services.tournaments import TournamentService
from src.utils.logging import logger

from typing import List


class TournamentData(BaseModel):
    tournament_id: int
    app_id: int
    adjusted_game_pool: float = 0.0


# Temporarily we agree to use this approximate tournament count value to calculate adjusted_game_pool
DAY_ONE_ADJUSTED_GAME_POOL = 10.0
name = "run_multiflight_check"


async def run_multiflight_check():
    """
    For every enabled tournament AutoStartConfig:
    For all multiflight tournaments from this app_id:
    - Get all day-1 tournaments with adjusted_game_pool == 0
    - Set adjusted_game_pool = DAY_ONE_ADJUSTED_GAME_POOL
    - Upsert MultiflightConfiguration with day-1 and day-2 tournament ids
    """
    logger.debug(name, "starting...")
    configs = await AutoStartConfig.find(
        AutoStartConfig.enabled == True,  # noqa: E712
    ).to_list()

    for config in configs:
        await run_preprocess_multiflight_tournaments(config)
    else:
        logger.debug(name, "No active multi-flight configs found")


async def run_preprocess_multiflight_tournaments(config: AutoStartConfig):
    multiflight_tournaments = await get_multiflight_tournaments(config.app_id)
    mf_ids = list(set(tournament.multi_flight_id for tournament in multiflight_tournaments))

    logger.info(name, f"processing multi-flight tournaments {mf_ids} for app_id: {config.app_id}")

    for mf_id in mf_ids:
        await process_multiflight_configuration(config, mf_id)


async def process_multiflight_configuration(config: AutoStartConfig, multiflight_id: int):
    name = f"process_multiflight_configuration:{multiflight_id}"

    logger.info(name, f'Processing multi-flight tournament {multiflight_id}')

    day_one_tournaments = await get_all_multiflight_tournaments_day_one(config.app_id, multiflight_id)
    day_one_tournament_ids = [t.tournament_id for t in day_one_tournaments]

    logger.info(name, f"day-1 total tournaments found: {day_one_tournament_ids}")

    # If adjusted_game_pool > 0  - we consider this tournament to be processed
    unprocessed_tournaments = [t for t in day_one_tournaments if t.adjusted_game_pool == 0]
    if not unprocessed_tournaments:
        return

    new_adjusted_game_pool: float = DAY_ONE_ADJUSTED_GAME_POOL

    for tournament in unprocessed_tournaments:
        logger.info(name,
                    f'processing tournament_id {tournament.tournament_id}, '
                    f'setting adjusted_game_pool {new_adjusted_game_pool}')

        await TournamentService.update_tournament_configurations(
            app_id=tournament.app_id,
            tournament_ids=[str(tournament.tournament_id)],
            adjusted_game_pool=new_adjusted_game_pool,
            adjusted_game_pool_updated_manually=False
        )

    logger.info(name, f'day-1 tournaments processed: {day_one_tournament_ids}')

    day_two_id = None
    day_two_tournaments = await get_multiflight_tournaments(config.app_id, multiflight_id, 1)
    if day_two_tournaments:
        day_two_id = day_two_tournaments[0].tournament_id
        logger.info(name, f'found day-2 tournament id {day_two_id}')
    if len(day_two_tournaments) > 1:
        logger.error(name, f'found more than one day-2 tournament {day_two_tournaments}')

    conf = await MultiflightConfigurationService.upsert(
        MultiflightConfiguration(
            app_id=config.app_id,
            multi_flight_id=multiflight_id,
            day_one_tournament_ids=[str(id) for id in day_one_tournament_ids],
            day_two_tournament_id=day_two_id
        )
    )
    logger.info(name, f'configuration created/updated: {conf}')


async def get_multiflight_tournaments(
    app_id: int,
    multi_flight_id: int | None = None,
    multi_flight_level: int | None = None
):
    query = [
        Tournament.app_id == app_id,
        Tournament.game_mode == 2,
        Tournament.registration_fee > 0,
        Tournament.status < TournamentStatus.Running.value,
    ]

    if multi_flight_id is None:
        # get all
        query.append(Tournament.multi_flight_id > 0)
    else:
        query.append(Tournament.multi_flight_id == multi_flight_id)

    if multi_flight_level is not None:
        query.append(Tournament.multi_flight_level == multi_flight_level)

    db_tournaments = await TournamentService.get_tournaments_version_aware(query=query)
    return db_tournaments


async def get_all_multiflight_tournaments_day_one(app_id: int, multi_flight_id: int) -> List[TournamentData]:
    tournaments = await get_multiflight_tournaments(app_id, multi_flight_id, 0)
    tournament_ids = [t.tournament_id for t in tournaments]

    game_pool_data = await TournamentService.get_adjusted_game_pool_multiple(app_id, tournament_ids)

    def get_tournament_data(tournament: Tournament):
        tournament_data = TournamentData.model_validate(tournament.model_dump())

        tournament_config = next((
            conf for conf in game_pool_data
            if conf.app_id == tournament.app_id and int(conf.tournament_id) == tournament_data.tournament_id
        ), None)

        tournament_data.adjusted_game_pool = tournament_config.game_pool if tournament_config else 0
        return tournament_data

    updated_tournaments = [get_tournament_data(t) for t in tournaments]
    return updated_tournaments
