from datetime import datetime, timezone, timedelta
import random


from src.schemas.requests import RegisterPlayerRequest
from src.services.player_registration_service import PlayerRegistrationService
from src.db.models import RegistrationAutomationConfig, PendingRegistration
from src.utils.enums import RegistrationStatus
from src.utils.logging import logger

task_name = "tasks.process_pending_registration"


async def process_pending_registration(reg_service: PlayerRegistrationService):
    # Get the config for registration automation
    config = await RegistrationAutomationConfig.find().first_or_none()
    if config.is_enabled is False:
        return

    if config.next_registration_time and config.next_registration_time.replace(
        tzinfo=timezone.utc
    ) > datetime.now(timezone.utc):
        return

    # Get not launched registrations
    pending_registration = await PendingRegistration.find(
        PendingRegistration.status == RegistrationStatus.PENDING.value
    ).first_or_none()

    if not pending_registration:
        return
    pending_registration.status = RegistrationStatus.PROCESSING.value
    await pending_registration.save()

    # Try to register the user
    await try_to_register_user(
        reg_service=reg_service,
        pending_registration=pending_registration,
    )

    # Update the config
    config.last_registration_time = datetime.now(timezone.utc)
    new_interval = random.randint(config.min_delay_sec, config.max_delay_sec)
    config.next_registration_time = datetime.now(timezone.utc) + timedelta(seconds=new_interval)
    await config.save()


def prepare_register_request(pending_registration: PendingRegistration) -> RegisterPlayerRequest:
    request_data = pending_registration.extra_data or {}
    request_data = request_data | pending_registration.model_dump(
        exclude_unset=True, exclude={"id", "extra_data", "updated_at", "error", "status"}
    )

    register_request = RegisterPlayerRequest(**request_data)
    return register_request


async def try_to_register_user(
    reg_service: PlayerRegistrationService,
    pending_registration: PendingRegistration,
):
    register_request = prepare_register_request(pending_registration)

    try:
        register_response = await reg_service.register_player(request=register_request)
    except Exception as e:
        pending_registration.error = str(e)
        pending_registration.status = RegistrationStatus.FAILED.value
        logger.error(
            task_name,
            f"Failed to register user with register id: {pending_registration.pending_registration_id}, "
            f"Exception during registration: {pending_registration.error}",
        )
        await pending_registration.save()
        return

    # Handle logical error in response
    if register_response.error:
        pending_registration.error = register_response.exception
        pending_registration.status = RegistrationStatus.FAILED.value
        logger.error(
            task_name,
            f"Failed to register user with register id: {pending_registration.pending_registration_id}, "
            f"Error: {pending_registration.error}",
            register_response.exception,
        )
    else:
        logger.info(
            task_name,
            f"Successfully registered user with register id: {pending_registration.pending_registration_id}",
        )
        pending_registration.status = RegistrationStatus.SUCCESS.value
        pending_registration.player_id = register_response.player_id
        pending_registration.user_id = register_response.user_id
    await pending_registration.save()
