import random
from collections import Counter
from datetime import datetime, timedelta, timezone
from zoneinfo import ZoneInfo
from typing import Optional

from beanie.operators import In, Or

from src.db.models import (
    AutoStartConfig,
    AutoStartAction,
    FutureLaunch,
    Player,
    Tournament,
    Launch,
    MultiflightConfiguration,
    TournamentConfiguration,
)
from src.mq.bullmq_manager import mq_manager
from src.schemas.requests import Seat
from src.services.bots import BotService
from src.services.tournaments import TournamentService
from src.services.players import PlayerService
from src.utils.enums import AppType, BotType, CurrencyType, TournamentStatus
from src.utils.feature_flags import feature_flags
from src.utils.logging import log_duration, logger
from src.utils.settings import settings


CoverBeforeStartRand = 0.1
# Definition of coverage in levels relative to time remaining
CoverLateRegLevels = {1: 0.3, 2: 0.6, 3: 0.8, 4: 1.0}
CoverLateRegRand = {1: 0.15, 2: 0.15, 3: 0.15, 4: 0.05}

GUARANTEE_LIMIT_GOLD = 80_000.0
MAX_BOT_PRIZE_POOL_UNDER_GUARANTEE_LIMIT = 0.25
MAX_BOT_PRIZE_POOL_OVER_GUARANTEE_LIMIT = 0.35
GUARANTEE_LIMIT_GOLD_REG_FEE_THRESHOLD = 100 * 8


@log_duration
async def run_overlay_filler():
    name = "overlay_filler"
    configs = await AutoStartConfig.all().to_list()

    logger.info(name, f"{'='*10} Running overlay filler for {len(configs)} configs {'='*10}")
    for config in configs:
        name = f"overlay_filler.{config.app_id}"
        if not config.enabled:
            continue

        non_initialized_jobs = await get_non_initialized_jobs_count(config.app_id)
        if non_initialized_jobs > 5:
            logger.info(
                name,
                f"Too many non-initialized bots ({non_initialized_jobs}). Skipping...",
            )
            continue

        not_started_tournaments = await get_not_started_tournaments(config)
        logger.info(
            name,
            f"Found {len(not_started_tournaments)} not started tournaments",
        )

        if config.cover_before_start > 0:
            for tournament in not_started_tournaments:
                await process_not_started_tournament(tournament, config)
        else:
            logger.info(name, f"Skip processing not started tournaments, {config.cover_before_start=}")

        late_registration_tournaments = await get_late_registration_tournaments(config)
        logger.info(
            name,
            f"Found {len(late_registration_tournaments)} late registration tournaments",
        )
        for tournament in late_registration_tournaments:
            await process_late_registration_tournament(tournament, config)


async def get_non_initialized_jobs_count(app_id: AppType) -> int:
    active_jobs = await mq_manager.get_jobs(states=["active"])

    result = 0
    for job in active_jobs:
        bot_type = job.data.get("type")
        job_app_id = job.data.get("appId")
        bot_not_initialized = not isinstance(job.progress, dict) or "updatedAt" not in job.progress

        if bot_type == BotType.PLAY.value and bot_not_initialized and job_app_id == app_id:
            result = result + 1

    return result


def level_multiplier(value, multiplier):
    scaled_value = value * multiplier
    fractional_part = scaled_value % 1
    return (int(fractional_part * multiplier) + 1) / multiplier


async def get_not_started_tournaments(config: AutoStartConfig):
    return await get_tournaments(config, Tournament.starting_time)


async def get_late_registration_tournaments(config: AutoStartConfig):
    return await get_tournaments(config, Tournament.late_registration_time)


def _tournament_query(config: AutoStartConfig, time_field):
    now = datetime.now(ZoneInfo("UTC"))

    return [
        Tournament.starting_time <= now + timedelta(minutes=config.check_before_start_min),
        time_field > now,
        Tournament.app_id == config.app_id,
        Tournament.game_mode == 2,
        Tournament.registration_fee > 0,
        Tournament.overlay > 0,
        Tournament.status < TournamentStatus.Running.value,
        Tournament.game_pool >= config.min_prize_pool,
    ]


async def get_tournaments(config: AutoStartConfig, time_field: Optional[datetime]):
    query = _tournament_query(config, time_field)

    # Searching for non-multi-flight or day one tournaments
    query.append(Tournament.multi_flight_level == 0 or Tournament.multi_flight_level is None)

    db_tournaments = await TournamentService.get_tournaments_version_aware(query=query)
    return db_tournaments


async def is_scheduling(app_id: int, tournament_id: str, check_interval_sec: int):
    now = datetime.now(ZoneInfo("UTC"))
    earliest_allowed_start_time = now - timedelta(seconds=check_interval_sec)

    # If last action was finished after the earliest allowed time, we should not schedule
    last_action_too_recent = await AutoStartAction.find(
        AutoStartAction.app_id == app_id,
        AutoStartAction.tournament_id == tournament_id,
        Or(
            AutoStartAction.finished > now,
            AutoStartAction.started > earliest_allowed_start_time
        )
    ).exists()

    return last_action_too_recent


async def process_not_started_tournament(tournament: Tournament, config: AutoStartConfig):
    name = f"overlay_filler.process_not_started_tournament:{tournament.tournament_id}"

    time_remaining = (
        tournament.starting_time.replace(tzinfo=ZoneInfo("UTC"))
        - datetime.now(ZoneInfo("UTC"))
    ).total_seconds()
    intervals_remaining = max(time_remaining / config.check_interval_sec, 1)

    coverage = (
        config.cover_before_start
        * (1 + random.uniform(-CoverBeforeStartRand, CoverBeforeStartRand))
        / intervals_remaining
    )
    logger.debug(
        name,
        f"Processing tournament. {coverage:.3f=} {time_remaining:.3f=} {intervals_remaining:.3f=}",
    )
    await process_tournament(tournament, config, coverage)


async def process_late_registration_tournament(tournament: Tournament, config: AutoStartConfig):
    name = f"overlay_filler.process_late_registration_tournament:{tournament.tournament_id}"

    if tournament.starting_time.replace(tzinfo=timezone.utc) > datetime.now(timezone.utc):
        logger.warning(name, "Tournament has not started yet")
        return

    remaining_registration_time = tournament.late_registration_time.replace(tzinfo=timezone.utc) - datetime.now(timezone.utc)
    late_registration_duration = tournament.late_registration_time - tournament.starting_time

    time_proportion = 1 - (remaining_registration_time / late_registration_duration)
    cover_level = int(time_proportion * len(CoverLateRegLevels)) + 1
    cover_level = max(1, min(cover_level, len(CoverLateRegLevels)))  # clamp to valid range

    prev_coverage = CoverLateRegLevels.get(cover_level - 1, 0)
    curr_coverage = CoverLateRegLevels.get(cover_level) - prev_coverage

    # calculate intervals multiplier: map remaining time to total time / interval length / number of levels
    multiplier = (
        late_registration_duration.total_seconds()
        / config.check_interval_sec
        / len(CoverLateRegLevels)
    )
    intervals_multiplier = level_multiplier(time_proportion, multiplier)

    coverage = (
        (config.cover_before_start + config.cover_late_registration)
        * (prev_coverage + curr_coverage * intervals_multiplier)
        * (1 + random.uniform(-CoverLateRegRand[cover_level], CoverLateRegRand[cover_level]))
    )

    if curr_coverage >= coverage:
        logger.debug(name, "Tournament has enough coverage, skipping")
        return

    logger.debug(
        name,
        f"Processing tournament. {curr_coverage=:.3f} {coverage=:.3f} "
        f"{time_proportion=:.3f} {intervals_multiplier=:.3f}",
    )
    await process_tournament(tournament, config, coverage)


async def process_tournament(tournament: Tournament, config: AutoStartConfig, desired_coverage: float):
    name = f"overlay_filler.process_tournament:{tournament.tournament_id}"

    if desired_coverage > 1:
        logger.warning(name, f'{desired_coverage:.3f=} exceeds 1, clamping')
        desired_coverage = 1

    if await is_scheduling(tournament.app_id, tournament.tournament_id, config.check_interval_sec):
        logger.debug(name, 'Already scheduling')
        return None

    tournament_config = await TournamentConfiguration.find_one(
        TournamentConfiguration.app_id == tournament.app_id,
        TournamentConfiguration.tournament_id == tournament.tournament_id,
    )
    game_pool = await calc_game_pool(tournament, tournament_config)
    overlay = game_pool - tournament.registration_fee * tournament.joined_count
    covered = (game_pool - overlay) / game_pool

    players_to_add = await calc_players_to_add(
        tournament,
        config,
        desired_coverage,
        covered,
        game_pool,
    )

    if players_to_add <= 0:
        logger.debug(name, "Enough players")
        return

    (min_delay, max_delay) = _calc_bot_start_delays(players_to_add, tournament_config, config)

    added_count = await fill_overlay(
        tournament,
        tournament_config,
        players_to_add,
        min_delay,
        max_delay,
        config.check_interval_sec,
        config.reentry_probability
    )

    if added_count == 0:
        logger.warning(name, f"No players added, tried to add {players_to_add} players")
    else:
        logger.info(name, f"Added {added_count} of {players_to_add} players")


def _calc_bot_start_delays(
    players_to_add: int,
    tournament_config: TournamentConfiguration | None,
    config: AutoStartConfig
) -> tuple[int, int]:
    check_interval_sec = config.check_interval_sec
    # choose min/max delays from tournament_config or fall back to auto-start config
    bot_min_delay_sec = (
        tournament_config.scheduling_min_delay_sec
        if tournament_config and tournament_config.scheduling_min_delay_sec is not None
        else config.bot_min_delay_sec
    )
    bot_max_delay_sec = (
        tournament_config.scheduling_max_delay_sec
        if tournament_config and tournament_config.scheduling_max_delay_sec is not None
        else config.bot_max_delay_sec
    )

    max_players_in_check_interval = check_interval_sec / ((bot_min_delay_sec + bot_max_delay_sec) / 2)
    if players_to_add >= max_players_in_check_interval or players_to_add == 1:
        return (bot_min_delay_sec, bot_max_delay_sec)

    # first player starts without delay, that's why we have -1 here
    max_delay = check_interval_sec // (players_to_add - 1)
    min_delay = int(max_delay * bot_min_delay_sec / bot_max_delay_sec)
    return (min_delay, max_delay)


async def calc_game_pool(tournament: Tournament, tournament_config: TournamentConfiguration | None):
    # If we have adjusted_game_pool set - using it
    if tournament_config and tournament_config.game_pool > 0:
        return tournament_config.game_pool

    else:
        mfconfig = await MultiflightConfiguration.find_one(
            In(MultiflightConfiguration.day_one_tournament_ids, [tournament.tournament_id]),
            MultiflightConfiguration.app_id == tournament.app_id,
        )
        if mfconfig:
            return tournament.game_pool / len(mfconfig.day_one_tournament_ids)

    return tournament.game_pool


async def calc_players_to_add(tournament: Tournament, config: AutoStartConfig, desired_coverage: float, covered: float, calculated_game_pool: float):
    name = f'overlay_filler.calc_players_to_add:{tournament.tournament_id}'

    if covered >= desired_coverage:
        logger.info(name, f"Enough players, "
                    f"{covered:.3f=}, {desired_coverage:.3f=}, {calculated_game_pool:.3f=}")
        return 0

    # We can't use `Launch.finished == True`` because for late registration tournaments some bots possibly already finished playing at this tournament
    already_added_players_count = await Launch.find(
        Launch.app_id == tournament.app_id,
        Launch.table_id == tournament.tournament_id,
        Launch.created_at >= tournament.starting_time.replace(tzinfo=timezone.utc) - timedelta(minutes=config.check_before_start_min),
    ).count()

    # not the adjusted, not recalculated for multiflight, but original tournament game pool
    max_players_to_add = calc_max_players_to_add(
        tournament.game_pool,
        tournament.registration_fee,
        CurrencyType(tournament.currency)
    )

    if max_players_to_add <= already_added_players_count:
        logger.debug(name, f"Already added max amount of players"
                     f"{already_added_players_count=} {max_players_to_add=}")
        return 0

    desired_players_to_add = calc_desired_players_to_add(
        desired_coverage,
        covered,
        calculated_game_pool,
        tournament.registration_fee,
        config.schedule_min_players,
    )

    players_to_add = min(desired_players_to_add, max_players_to_add - already_added_players_count)
    logger.debug(name,
                 f'Will add {players_to_add} players, {desired_players_to_add=}, {max_players_to_add=}, '
                 f'{already_added_players_count=}, {tournament.game_pool=}, {calculated_game_pool=:.3f}')

    return players_to_add


def calc_desired_players_to_add(desired_coverage: float, covered: float, game_pool: float, registration_fee: float, schedule_min_players: int):
    total_players_to_add = game_pool * (desired_coverage - covered) / registration_fee
    players_to_add = max(int(total_players_to_add), schedule_min_players)
    return players_to_add


def calc_max_players_to_add(game_pool: float, registration_fee: float, currency: CurrencyType):
    """JIRA: ARMS-268"""
    guarantee_gold = PlayerService.convert_currency(game_pool, currency, CurrencyType.GOLD)
    registration_fee_gold = PlayerService.convert_currency(registration_fee, currency, CurrencyType.GOLD)

    if guarantee_gold >= GUARANTEE_LIMIT_GOLD and registration_fee_gold <= GUARANTEE_LIMIT_GOLD_REG_FEE_THRESHOLD:
        max_total_buyin_share = MAX_BOT_PRIZE_POOL_OVER_GUARANTEE_LIMIT
    elif currency == CurrencyType.GOLD:
        max_total_buyin_share = feature_flags.get_flag('mtt-automation-max-prize-pool-share-under80k-gold', 0.30)
    elif currency == CurrencyType.USD:
        max_total_buyin_share = feature_flags.get_flag('mtt-automation-max-prize-pool-share-under10k-usd', 0.25)
    else:
        raise ValueError(f"calc_max_players_to_add: Unsupported currency type: {currency}")

    max_total_buyin = guarantee_gold * max_total_buyin_share
    return int(max_total_buyin / registration_fee_gold)


async def fill_overlay(
    tournament: Tournament,
    tournament_config: TournamentConfiguration | None,
    num_players: int,
    min_delay: int,
    max_delay: int,
    delay_limit: int,
    reentry_probability: float = 0,
) -> int:
    name = f"overlay_filler.fill_overlay:{tournament.tournament_id}"
    now = datetime.now(ZoneInfo("UTC"))
    bot_start_delay = 0

    allowed_tools_ids = extract_tools_ids(tournament.sign_up_options)

    players = await find_players_for_tournament(tournament, now, num_players, allowed_tools_ids)

    scheduled = 0
    for player in players:
        if scheduled >= num_players:
            break

        ticket_id = _get_player_tool_id_for_tournament(tournament.tournament_id, player, allowed_tools_ids)

        can_reenter = (
            tournament_config.max_reentry_count > 0 and random.random() < reentry_probability
            if tournament_config else False
        )
        seat = Seat(
            table_id=tournament.tournament_id,
            player=player,
            ticket_id=ticket_id,
            can_reenter_tournament=can_reenter,
            app_id=tournament.app_id,
        )

        try:
            await BotService.start_bot(seat, BotType.PLAY, bot_start_delay)
            logger.info(name, f"Started bot for player {player.player_id}, {can_reenter=}, {ticket_id=}")

        except ValueError as e:
            logger.error(name, f"Failed to start play bot for player {player.player_id}", e)
            continue

        scheduled += 1

        bot_start_delay += random.uniform(min_delay, max_delay)
        if bot_start_delay > delay_limit:
            break

    if scheduled > 0:
        await AutoStartAction(
            app_id=tournament.app_id,
            tournament_id=tournament.tournament_id,
            number_of_players=scheduled,
            started=now,
            finished=now + timedelta(seconds=bot_start_delay),
        ).insert()

    return scheduled


def extract_tools_ids(sign_up_options: str) -> list[int] | None:
    """
    Extract the last numeric segment after the final ':' from each comma-separated token.

    Examples:
        "gold,tool,specific:mtt:a92:1754,a92:1593" -> [1754, 1593]
        ":1234:5678" -> [5678]
    """
    if not sign_up_options:
        return None

    ticket_fetching_enabled = feature_flags.get_flag("ticket-fetching-enabled", False)

    if not ticket_fetching_enabled:
        return None

    tool_ids: list[int] = []
    for token in sign_up_options.split(","):
        token = token.strip()
        if not token:
            continue
        parts = token.split(":")
        last = parts[-1].strip()
        if last.isdigit():
            tool_ids.append(int(last))

    return tool_ids or None


async def find_players_for_tournament(
    tournament: Tournament,
    start_date: datetime,
    num_players: int = None,
    allowed_tools_ids: list[int] = None,
) -> list[Player]:
    name = f"overlay_filler.find_players_for_tournament:{tournament.tournament_id}"

    start_time = start_date.time()

    booked_players = await get_booked_player_ids(
        tournament,
        start_date,
    )

    players = await PlayerService.find_players_for_tournament(
        tournament.app_id,
        CurrencyType(tournament.currency),
        tournament.registration_fee,
        start_time,
        allowed_tools_ids,
        booked_players,
    )

    players = [
        player for player in players
        if player.is_in_play_time_range(start_time)
    ]

    reentering_player_ids = await get_reentering_players(tournament)

    booked_reentering_player_ids = set(booked_players) & set(reentering_player_ids)

    if booked_reentering_player_ids:
        logger.debug(name, f"Reenter candidates that are booked: {booked_reentering_player_ids}")

    reentering_player_ids = [player_id for player_id in reentering_player_ids if player_id not in booked_players]

    players = _apply_sorting(players, allowed_tools_ids, reentering_player_ids)

    if num_players:
        players = players[:num_players]

    await print_reenter_status(reentering_player_ids, [p.player_id for p in players], tournament.tournament_id)

    logger.debug(name, f"Found {len(players)} players")
    return players


async def get_booked_player_ids(tournament: Tournament, start_date: datetime) -> list[str]:
    # players who already played (and maybe reentered) this tournament
    booked_players = await get_players_already_played(tournament)

    # players booked for other games
    max_booked_launch_time = start_date + timedelta(hours=settings.no_launch_range_hours)
    future_launches = await FutureLaunch.find(
        FutureLaunch.app_id == tournament.app_id,
        FutureLaunch.starting_time <= max_booked_launch_time,
    ).to_list()
    booked_players += [future_launch.player_id for future_launch in future_launches]

    # players who are already in final
    mfconfig = await MultiflightConfiguration.find_one(
        In(MultiflightConfiguration.day_one_tournament_ids, [tournament.tournament_id]),
        MultiflightConfiguration.app_id == tournament.app_id,
    )
    if mfconfig:
        participated_launches = await FutureLaunch.find(
            FutureLaunch.app_id == tournament.app_id,
            FutureLaunch.tournament_id == mfconfig.day_two_tournament_id
        ).to_list()
        booked_players += [future_launch.player_id for future_launch in participated_launches]

    return booked_players


async def get_players_already_played(tournament: Tournament) -> list[str]:
    # Get players who already played this tournament (excluding reentries if max_reentry_count is set)

    tournament_config = await TournamentConfiguration.find_one(
        TournamentConfiguration.app_id == tournament.app_id,
        TournamentConfiguration.tournament_id == tournament.tournament_id,
    )

    launches = await Launch.find(
        Launch.app_id == tournament.app_id,
        Launch.table_id == tournament.tournament_id
    ).to_list()

    if tournament_config and tournament_config.max_reentry_count > 0:
        player_counts = Counter(launch.player_id for launch in launches)
        # we use `>` because first launch does not count as reentry - its a regular entry
        return [player_id for player_id, count in player_counts.items() if count > tournament_config.max_reentry_count]
    else:
        return [launch.player_id for launch in launches]


async def get_reentering_players(tournament: Tournament) -> list[str]:
    name = f'overlay_filler.get_reentering_players:{tournament.tournament_id}'
    earliest_launch_time = tournament.starting_time.replace(tzinfo=timezone.utc)

    config = await AutoStartConfig.find_one(
        AutoStartConfig.app_id == tournament.app_id
    )
    if config:
        earliest_launch_time = tournament.starting_time.replace(tzinfo=timezone.utc) - timedelta(minutes=config.check_before_start_min)

    launches = await Launch.find(
        Launch.app_id == tournament.app_id,
        Launch.table_id == tournament.tournament_id,
        Launch.created_at >= earliest_launch_time,
        Launch.can_reenter_tournament == True,  # noqa: E712
    ).to_list()

    if not launches:
        return []

    players = list(set(launch.player_id for launch in launches))
    logger.debug(name, f'Found players that can reenter: {players}')

    return players


async def print_reenter_status(reentering_player_ids: list[str], potential_player_ids: list[str], tournament_id):
    # TMP for checking reentering logic, can be removed later
    name = f'overlay_filler.print_reenter_status:{tournament_id}'

    not_reentering_player_ids = set(potential_player_ids) - set(reentering_player_ids)
    logger.debug(name, f"{len(not_reentering_player_ids)} not reentering players found")

    not_reentering_players = await Player.find(
        In(Player.player_id, list(not_reentering_player_ids))
    ).to_list()
    for p in not_reentering_players:
        if p.table_id == tournament_id:
            logger.debug(name, f"Player {p.player_id} is still playing")
        else:
            logger.debug(name, f"Player {p.player_id} can reenter, but filtered out, {p.status=}, {p.table_id=}")

    reentering_player_ids = set(reentering_player_ids) & set(potential_player_ids)
    logger.debug(name, f"{reentering_player_ids=}")


def _get_player_tool_id_for_tournament(tournament_id: str, player: Player, allowed_tools_ids: list[int]) -> Optional[str]:
    if not player.balance or not player.balance.tickets or not allowed_tools_ids:
        return None

    matching_ticket = PlayerService.find_matching_ticket_id(player.balance.tickets, allowed_tools_ids)

    if matching_ticket:
        logger.debug(f"Found matching ticket {matching_ticket} for player {player.player_id} in tournament {tournament_id}")

    return str(matching_ticket) if matching_ticket else None


def _has_matching_tickets(player: Player, allowed_tools_ids: list[int]) -> bool:
    """Check if player has tickets matching any of the allowed tool IDs."""
    if not allowed_tools_ids or not player.balance or not player.balance.tickets:
        return False

    player_tool_ids = {ticket.tool_id for ticket in player.balance.tickets}
    allowed_set = set(allowed_tools_ids)
    return bool(player_tool_ids & allowed_set)


def _apply_sorting(players: list[Player], allowed_tools_ids: list[int], reentering_player_ids: list[str]) -> list[Player]:
    players = sorted(
        players,
        key=lambda p: _has_matching_tickets(p, allowed_tools_ids),
        reverse=True,
    )
    players = sorted(players, key=lambda p: p.player_id in reentering_player_ids, reverse=True)
    return players
