from datetime import datetime, <PERSON><PERSON><PERSON>
from zoneinfo import ZoneInfo
from typing import List

from bullmq import Job

from src.db.models import FutureLaunch, FutureTournamentCheck
from src.utils.logging import log_duration, logger
from src.mq.bullmq_manager import mq_manager
from src.services.players import PlayerService, PlayerToUpdate
from src.services.bots import BotService, LaunchToUpdate
from src.utils.enums import PlayerStatus, BotType, TournamentAppTypeValues
from src.schemas.responses import PlayerBalanceResponse, TicketResponse
from src.utils.feature_flags import feature_flags
from src.utils.settings import settings


WPK_SUSPENDED_ERROR = "Your account has been suspended."


@log_duration
async def update_player_states():
    players_to_update: List[PlayerToUpdate] = []

    active_players_to_update = await get_player_updates_for_active_jobs()
    players_to_update.extend(active_players_to_update)

    (
        finished_players_to_update,
        completed_launches_to_update,
        future_launches,
        future_tournament_checks,
    ) = await get_player_updates_for_finished_jobs()
    players_to_update.extend(finished_players_to_update)

    await PlayerService.update_players(players_to_update)
    await BotService.finish_launches(completed_launches_to_update)
    await BotService.create_future_launches(future_launches)
    await BotService.create_future_tournament_checks(future_tournament_checks)


async def get_player_updates_for_active_jobs() -> List[PlayerToUpdate]:
    name = "tasks.get_player_updates_for_active_jobs"

    active_jobs = await mq_manager.get_jobs(states=["active"])

    players_to_update = []
    for active_job in active_jobs:
        # limit progress string length for logging
        progress_str = str(active_job.progress)[:200] + (
            "..." if len(str(active_job.progress)) > 200 else ""
        )

        logger.debug(name, f"Processing active job id={active_job.id}: {progress_str}")

        # check if job is overdue
        try:
            actual_overdue_timeout = get_actual_job_overdue_timeout(active_job)
        except Exception as e:
            error = "Failed to check if job is overdue"
            logger.error(name, error, e)
            await BotService.stop_bot(bot_id=active_job.id, reason=error)

        if actual_overdue_timeout > 0:
            reason = f"Bot {active_job.id} is not responding for {actual_overdue_timeout} sec"
            logger.error(name, reason)
            await BotService.stop_bot(bot_id=active_job.id, reason=reason)

        else:
            update = get_player_update_from_active_job(active_job)
            if update:
                players_to_update.append(update)

    return players_to_update


def get_player_update_from_active_job(active_job: Job) -> PlayerToUpdate | None:
    name = "tasks.get_player_update"

    if not isinstance(active_job.progress, dict):
        return None

    player_id = active_job.data["playerId"]
    player_to_update = PlayerToUpdate(
        status=PlayerStatus.PENDING.value,
        player_id=player_id,
        bot_id=active_job.id,  # restore bot_id from job id if it's somehow lost
    )

    progress_data: dict = active_job.progress.get("data", {})
    if "updatedAt" in progress_data:
        player_to_update.updated_at = datetime.fromtimestamp(
            progress_data.get("updatedAt") / 1000, ZoneInfo("UTC")
        )

    bot_type = active_job.data["type"]
    if bot_type == BotType.SCAN.value:
        player_to_update.status = PlayerStatus.SCANNING.value
        return player_to_update

    if bot_type in [BotType.BALANCE.value, BotType.CHECK.value, BotType.TRANSFER.value]:
        return None

    if "tables" in progress_data:
        # happens when PLAY bot sends table data, will be processed in update_tables_and_tournaments
        return None

    try:
        status: str = progress_data.get("status")
        player_to_update.status = PlayerStatus(status.upper()).value
    except (ValueError, AttributeError):
        logger.error(name, f"Invalid player status {status} in the job {active_job.id}")
        return None

    try:
        stats: dict = progress_data.get("stats")

        if not isinstance(stats, dict):
            return player_to_update

        if "handsPlayed" in stats:
            player_to_update.hands_played = int(stats["handsPlayed"])
        if "totalBuyIn" in stats:
            player_to_update.total_buy_in = int(stats["totalBuyIn"])
        if "lastBuyIn" in stats:
            player_to_update.last_buy_in = int(stats["lastBuyIn"])
        if "rebuyCount" in stats:
            player_to_update.rebuy_count = int(stats["rebuyCount"])
        if "stack" in stats:
            player_to_update.stack = float(stats["stack"])
        if "chips" in stats:
            player_to_update.chips = float(stats["chips"])
        if "rank" in stats:
            player_to_update.rank = int(stats["rank"])

    except (KeyError, AttributeError):
        logger.debug(name, f"No stats in the progress in the job {active_job.id}, progress={active_job.progress}")
        return None

    return player_to_update


def get_actual_job_overdue_timeout(active_job: Job) -> int:

    feature_flag_context = {
        "job_type": active_job.data.get("type")
    }
    overdue_timeout = feature_flags.get_flag("manager-stuck-job-timeout", settings.job_stall_timeout, feature_flag_context)

    if isinstance(active_job.progress, int):
        # Job not initialized yet
        return 0

    try:
        updated_at = datetime.fromtimestamp(
            active_job.progress.get("updatedAt", 0) / 1000, ZoneInfo("UTC")
        )
    except AttributeError:
        # job has not been initialized yet
        return 0

    how_long_ago: timedelta = datetime.now(ZoneInfo("UTC")) - updated_at

    return max(how_long_ago.seconds - overdue_timeout, 0)


async def get_player_updates_for_finished_jobs() -> tuple[
    list[PlayerToUpdate], list[LaunchToUpdate], list[FutureLaunch], list[FutureTournamentCheck]
]:
    name = "tasks.get_player_updates_for_finished_jobs"

    completed_jobs = await mq_manager.get_jobs(states=["completed", "failed"])

    players_to_update = []
    launches_to_update = []
    future_launches = []
    future_tournament_checks = []
    for completed_job in completed_jobs:

        stop_reason = completed_job.data.get("stopReason") or completed_job.failedReason
        stop_reason = stop_reason.replace("\n", " ") if stop_reason else None
        logger.debug(name, f"Processing completed job id={completed_job.id}: {stop_reason}")

        player_id = completed_job.data.get("playerId")
        launch_id = completed_job.data.get("launchId")
        table_id = completed_job.data.get("tableId")
        app_id = completed_job.data.get("appId")

        if launch_id:
            launches_to_update.append(LaunchToUpdate(launch_id=launch_id, stop_reason=stop_reason))

        bot_type = BotType(completed_job.data.get("type"))
        match bot_type:
            case BotType.PLAY:
                if app_id in TournamentAppTypeValues:
                    next_check = FutureTournamentCheck(
                        player_id=player_id, tournament_id=table_id, app_id=app_id
                    )
                    future_tournament_checks.append(next_check)

            case BotType.CHECK:
                if completed_job.progress.get("data", {}).get("registered", False):
                    next_launch = FutureLaunch(
                        player_id=player_id,
                        tournament_id=table_id,
                        app_id=app_id,
                        starting_time=None,
                    )
                    future_launches.append(next_launch)
            case _:
                pass

        player_to_update = get_player_update_from_completed_job(completed_job)

        if stop_reason and WPK_SUSPENDED_ERROR in stop_reason:
            logger.warning(name, f"Player {player_to_update.player_id} suspended")
            player_to_update.status = PlayerStatus.SUSPENDED.value
            player_to_update.enabled = False

        player_to_update.last_error = stop_reason
        players_to_update.append(player_to_update)

        # remove job from Redis so it won't be processed again
        await completed_job.remove()
    return players_to_update, launches_to_update, future_launches, future_tournament_checks


def get_player_update_from_completed_job(completed_job: Job) -> PlayerToUpdate:
    player_to_update = PlayerToUpdate(
        status=PlayerStatus.IDLE.value,
        player_id=completed_job.data["playerId"],
        launch_id=None,
        bot_id=None,
        table_id=None,
        should_stop=True,
        bot_type=None,
        need_balance_update=False,
        strategy_profile=None,
    )

    bot_type = BotType(completed_job.data.get("type"))
    match bot_type:
        case BotType.BALANCE:
            balance = get_balance_from_job_progress(completed_job)
            if balance:
                player_to_update.balance = balance

        case BotType.PLAY:
            player_to_update.need_balance_update = True

        case BotType.TRANSFER:
            player_to_update.need_balance_update = True

    return player_to_update


def get_balance_from_job_progress(completed_job: Job) -> PlayerBalanceResponse | None:
    name = "tasks.get_balance_from_job_progress"
    try:
        balance: dict = completed_job.progress.get("data").get("balance")
        balance_response = PlayerBalanceResponse(
            diamond=balance.get("diamond", 0),
            gold=balance.get("gold", 0),
            usdt=balance.get("usdt", 0),
            usd=balance.get("usd", 0),
            tickets=map(lambda t: TicketResponse(ticket_id=t["ticketId"], tool_id=t["toolId"]), balance.get("tickets", [])),
        )
        return balance_response
    except (KeyError, AttributeError):
        logger.warning(
            name,
            f"No balance details in job {completed_job.id}, progress={completed_job.progress}",
        )
        return None
