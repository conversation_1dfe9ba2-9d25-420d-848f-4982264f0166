from dataclasses import dataclass

from src.utils.lock import RedisLock
from src.utils.logging import logger


async def touch_lock(lock_manager: RedisLock, key: str):
    await lock_manager.extend(key, timeout=30)


async def stupid_task():
    name = "tasks.stupid_task"
    logger.debug(name, "Scheduler tick")


async def check_lock_and_start_scheduler(lock_manager: RedisLock, key: str, scheduler):
    name = "tasks.check_lock_and_start_scheduler"
    if scheduler.is_running_jobs:
        return
    if await lock_manager.is_locked(key):
        pass
        # logger.debug(name, "Scheduler is locked, waiting...")
    else:
        logger.info(name, "Scheduler lock is free, trying to acquire it and start the scheduler")
        await scheduler.start()


@dataclass
class FakeJob:
    id: str
    progress: dict
    player_id: str
