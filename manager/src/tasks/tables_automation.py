import random

from src.utils.settings import settings
from src.services.ip_pool_service import IPPoolConfigurationService
from src.db.models import FutureLaunch, IPPoolConfiguration, TablesAutomationConfig, Table, Launch, Player
from src.services.bots import BotService
from src.services.players import PlayerService
from src.utils.enums import AppType, CurrencyType, PlayerStatus, BotType
from src.utils.logging import log_duration, logger

from src.schemas.requests import Seat

from datetime import datetime, timedelta, time, timezone
from zoneinfo import ZoneInfo
from beanie.operators import In, NotIn, Or


"""
Steps for tables automation
 - find enabled automations
 - find tables
 - find players
 - add them to the game
 - remove them from the game
"""

BUY_IN_MULTIPLIERS = [100, 200, 300]
R1_SB_COEFFICIENT = 0.5
R1_STRADDLE_COEFFICIENT = 2
R2_SB_COEFFICIENT = 0.5

MIN_HANDS_PLAYED = 60
MAX_HANDS_PLAYED = 90

MIN_HANDS_PLAYED_ZOOM = 120
MAX_HANDS_PLAYED_ZOOM = 150


async def run_tables_automation():
    name = "tasks.tables_automation"

    enabled_configs = await TablesAutomationConfig.find(TablesAutomationConfig.is_enabled == True).to_list()  # noqa: E712
    logger.info(name, f"{'='*10} Running tables automation for {len(enabled_configs)} configs. {'='*10}")

    for config in enabled_configs:
        now = datetime.now(timezone.utc)

        # At first stop bots, then start new bots, otherwise we can stop a just-started bot
        next_stop = config.stop_last_update.astimezone(timezone.utc) + timedelta(minutes=config.stop_interval)
        if next_stop < now:
            tables_bots_stopped = await stop_scheduled_tables(config)
            if tables_bots_stopped > 0:
                config.stop_last_update = now

        next_start = config.start_last_update.astimezone(timezone.utc) + timedelta(minutes=config.start_interval)
        if next_start < now:
            tables_started = await start_scheduled_tables(config)
            if tables_started > 0:
                config.start_last_update = now

        await config.save()


@log_duration
async def start_scheduled_tables(config: TablesAutomationConfig):
    tables_started_count = 0

    tables = await get_matching_tables(config)
    for table in tables:
        if config.app_id == AppType.R7.value:
            if await try_start_zoom_bots(table, config):
                tables_started_count += 1
        else:
            if await try_start_bot(table, config):
                tables_started_count += 1

    return tables_started_count


async def try_start_bot(table: Table, config: TablesAutomationConfig) -> bool:
    """ Not ZOOM tables only """
    name = f"tasks.tables_automation.start:{config.app_id}_{config.config_id}_{table.table_id}"

    if config.app_id == AppType.R7.value:
        logger.error(name, "try_start_bot called for R7 table, should call try_start_zoom_bots")
        return False

    table_big_blind = table.gaming_configuration.blinds[1]
    current_time = datetime.now(ZoneInfo("UTC")).time()

    existing_players = await Player.find(
        Player.table_id == table.table_id,
        In(Player.status, [PlayerStatus.PLAYING.value, PlayerStatus.PENDING.value])
    ).count()

    if config.max_bots_per_table and existing_players >= config.max_bots_per_table:
        logger.debug(name, f"Table already has {existing_players=}, {config.max_bots_per_table=}")
        return False

    buy_in_multiplier = random.choice(config.buy_in_multipliers or BUY_IN_MULTIPLIERS)
    player = await find_free_player_for_table(table, current_time, buy_in_multiplier)
    if not player:
        logger.debug(name, f"No free player found, {table_big_blind=}, {table.currency.value=}")
        return False

    return await start_table_bot(player, table, config, buy_in_multiplier)


async def try_start_zoom_bots(table: Table, config: TablesAutomationConfig) -> bool:
    name = f"tasks.tables_automation.start_zoom:{config.app_id}_{config.config_id}_{table.table_id}"

    if config.app_id != AppType.R7.value:
        logger.error(name, "try_start_zoom_bots called for non-R7 table, should call try_start_bot")
        return False

    table_big_blind = table.gaming_configuration.blinds[1]
    current_time = datetime.now(ZoneInfo("UTC")).time()

    existing_players = await Player.find(
        Player.table_id == table.table_id,
        In(Player.status, [PlayerStatus.PLAYING.value, PlayerStatus.PENDING.value])
    ).count()

    real_players = (table.players_total - existing_players) or 1
    bots_to_real_ratio = existing_players / real_players * 100

    if config.zoom_allowed_bots_percent and bots_to_real_ratio >= config.zoom_allowed_bots_percent:
        logger.debug(name, f"Table {table.table_id} already has {existing_players=} "
                     f"/ {real_players=}, > {config.zoom_allowed_bots_percent=}%")
        return False

    max_bots_allowed = (real_players * config.zoom_allowed_bots_percent) // 100

    bots_to_start = max_bots_allowed - existing_players
    bots_to_start = min(config.zoom_start_bots_per_interval, bots_to_start)
    logger.debug(name, f"Table {table.table_id} can start up to {bots_to_start} bots")

    started_bots = 0
    for _ in range(bots_to_start):
        buy_in_multiplier = random.choice(config.buy_in_multipliers or BUY_IN_MULTIPLIERS)
        player = await find_free_player_for_table(table, current_time, buy_in_multiplier)
        if not player:
            logger.debug(name, f"Table {table.table_id}: No free player found, "
                         f"{table_big_blind=}, {table.currency.value=}")
            break

        delay = random.randint(0, config.start_interval * 2 * 60)
        if await start_table_bot(player, table, config, buy_in_multiplier, delay_seconds=delay):
            started_bots += 1

    return started_bots > 0


async def start_table_bot(
    player: Player,
    table: Table,
    config: TablesAutomationConfig,
    buy_in_multiplier: int,
    delay_seconds: int = 0
) -> bool:
    name = f"tasks.tables_automation.start:{config.app_id}_{config.config_id}_{table.table_id}"

    seat = Seat(
        table_id=table.table_id,
        player=player,
        app_id=table.app_id,
        buy_in_multiplier=buy_in_multiplier,
        max_rebuy_count=config.max_rebuy_count,
        rebuy_threshold=config.table_rebuy_threshold
    )

    # Diamond tables seem to not support buyout
    if table.currency == CurrencyType.GOLD:
        seat.withdraw_amount = config.withdraw_amount
        seat.withdraw_threshold = config.withdraw_threshold

    if config.strategy_profile:
        seat.strategy_profile = get_strategy_profile(config)

    if config.club_id:
        seat.club_id = config.club_id

    try:
        bot_response = await BotService.start_bot(
            seat, BotType.PLAY, config_id=config.config_id, delay=delay_seconds
        )
        logger.info(name,
                    f"Started bot, player_id: {player.player_id}, {buy_in_multiplier=}, "
                    f"table: {table.model_dump_json(exclude='id')}")

        launch_id = bot_response.player.launch_id
        launch = await Launch.find_one(Launch.launch_id == launch_id)
        if launch and not launch.soft_max_hands_played:
            launch.soft_max_hands_played = random.randint(MIN_HANDS_PLAYED, MAX_HANDS_PLAYED)

            if launch.app_id == AppType.R7.value:
                launch.soft_max_hands_played = random.randint(MIN_HANDS_PLAYED_ZOOM, MAX_HANDS_PLAYED_ZOOM)

            logger.info(name,
                        f"Updated Launch for player {player.player_id}, "
                        f"{launch.launch_id=}, {table.table_id=}, "
                        f"{launch.soft_max_hands_played=}")
            await launch.save()
    except ValueError as e:
        logger.error(name, f"Failed to start bot for player {player.player_id}", e)
        return False

    return True


def get_strategy_profile(config: TablesAutomationConfig) -> str:
    # Get current strategy profile percentage for running players and launched byt this config
    strategy_choices = config.strategy_profile.keys()
    strategy_weights = config.strategy_profile.values()

    return random.choices(
        population=list(strategy_choices),
        weights=list(strategy_weights),
        k=1
    )[0]


async def stop_scheduled_tables(config: TablesAutomationConfig):
    name = f"tasks.tables_automation.stop:{config.app_id}_{config.config_id}"

    players_to_remove = await find_players_to_stop(config)

    if config.app_id == AppType.R7.value:
        return await stop_zoom_bots(players_to_remove)

    tables_bots_stopped = set()
    for player in players_to_remove:
        # We should remove only 1 bot at a time from a table
        if player.table_id in tables_bots_stopped:
            logger.info(name,
                        f"Skip stopping bot for player {player.player_id}, already removed somebody from this table, "
                        f"will try next scheduler tick")
            continue

        if player.bot_type != BotType.PLAY.value:
            logger.warning(name, f"Player {player.player_id} with table_id {player.table_id} has bot_type {player.bot_type}, expected 'play'")
            continue

        logger.info(name,
                    f"Stopping player {player.player_id}. hands_played: {player.hands_played}, "
                    f"stack: {player.stack}, rebuy_count: {player.rebuy_count}, total_buy_in: {player.total_buy_in}")

        try:
            await BotService.stop_bot(player.bot_id, "Scheduled stop by automation")
        except Exception as e:
            logger.error(name, f"Failed to stop bot for player {player.player_id}", e)

        tables_bots_stopped.add(player.table_id)

    return len(tables_bots_stopped)


@log_duration
async def stop_zoom_bots(players_to_stop: list[Player]):
    """Zoom can stop multiple bots at a time from the same table with delay"""
    name = "tasks.tables_automation.stop_zoom_bots"

    for player in players_to_stop:
        if not player.bot_id:
            logger.error(name, f"Player {player.player_id} with {player.status=} has no bot_id")
            continue

        if player.bot_type != BotType.PLAY.value:
            logger.warning(name, f"Player {player.player_id} with {player.table_id=}"
                           f" has {player.bot_type=}, expected 'play'")
            continue

    for player in players_to_stop:
        # to spread players stopping. If random time for different players is the same, it's ok
        delay_seconds = random.randint(0, 120)

        logger.info(name,
                    f"Stopping player {player.player_id}. {player.hands_played=}, {player.stack=}, "
                    f"{player.rebuy_count=}, {player.total_buy_in=}, {delay_seconds=}")

        try:
            await BotService.stop_bot(
                player.bot_id,
                reason="Scheduled stop by tables automation",
                delay_seconds=delay_seconds,
            )
        except Exception as e:
            logger.error(name, f"Failed to stop bot for player {player.player_id}", e)

    return len(players_to_stop)


async def get_matching_tables(config: TablesAutomationConfig) -> list[Table]:
    name = f"tasks.tables_automation.get_matching_tables:{config.app_id}_{config.config_id}"

    currency_values = [c.value for c in config.currencies]
    big_blinds_float = [float(bb) for bb in config.big_blinds]

    tables_query = Table.find(
        Table.app_id == config.app_id,
        Table.gaming_configuration.ante == config.ante,
        In(Table.currency, currency_values),
        In(Table.gaming_configuration.room_mode, config.room_modes),
        In(Table.gaming_configuration.blinds[1], big_blinds_float),
        Table.empty_seats != 0,  # only tables with free seats, for Zoom empty_seats is always negative
    )

    if config.club_id:
        tables_query = tables_query.find(
            Table.club_id == config.club_id
        )

    matching_tables = await tables_query.to_list()

    if config.occupancy_threshold > 0:
        # check if all not empty tables are full enough and then start only one new table
        all_non_empty_tables_are_full = all(
            table.players_total >= config.occupancy_threshold for table in matching_tables if table.players_total > 0
        )
        if all_non_empty_tables_are_full:
            # Start a new table
            starting_table = next((  # first table that is not full enough
                table for table in matching_tables
                if table.players_total >= config.players_count[0]
                and table.players_total <= config.players_count[1]
                and table.players_total < config.occupancy_threshold
            ), None)
            if starting_table:
                logger.info(name, "Starting bot on new table")
                return [starting_table]
            else:
                logger.info(name, "No empty tables found to start bots on")
        else:
            logger.info(name, "Not all tables are full enough to start bots on new table")

        return []

    matching_tables = [
        t for t in matching_tables
        if t.players_total >= config.players_count[0]
        and t.players_total <= config.players_count[1]
    ]

    if config.tables_affected_percentage:
        """
        We might not want to start bots on every table even if it suits all the params
        This filter removes tables by their table_id, example:
        table.table_id = "123456"
        config.tables_affected_percentage = 40  # 40%
        int(table.table_id) % 100 = 56  # > 40, so won't ever have bots on the table
        try-catch is needed for tests (they use table_id='t1') and in case of unsupported table_id from worker
        """
        try:
            matching_tables = [
                t for t in matching_tables
                if (int(t.table_id) % 100) < config.tables_affected_percentage
            ]
        except ValueError:
            logger.error(name, f"Invalid table_id format for filtering: {config.tables_affected_percentage=}, filter not applied")

    logger.info(name, f"Found {len(matching_tables)} tables to start bots on")
    return matching_tables


async def find_free_player_for_table(table: Table, play_time: time, buy_in_multiplier: int) -> Player | None:
    name = "tasks.tables_automation.find_free_player"

    big_blind_cents = table.gaming_configuration.blinds[1]
    logger.debug(name, f"Searching for players, big_blind:{big_blind_cents}, "
                 f"currency:{table.currency.value}, buy_in_multiplier:{buy_in_multiplier}")

    min_buy_in = buy_in_multiplier * big_blind_cents / 100

    query = PlayerService.build_eligible_player_query(
        table.app_id, play_time, table.currency, min_buy_in
    )

    if table.club_id:
        query.append(In(Player.club_ids, [table.club_id]))

    potential_players = await Player.find(*query).to_list()

    players_already_on_table = await Player.find(
        Player.table_id == table.table_id,
    ).to_list()

    potential_players = await filter_players_using_same_ip(potential_players, players_already_on_table)

    max_booked_launch_time = datetime.now(timezone.utc) + timedelta(hours=settings.no_launch_range_hours)

    booked_players = await FutureLaunch.find(
        FutureLaunch.starting_time <= max_booked_launch_time,
        In(FutureLaunch.player_id, [p.player_id for p in potential_players]),
    ).to_list()
    booked_player_ids = {launch.player_id for launch in booked_players}

    players = [
        player for player in potential_players
        if player.is_in_play_time_range(play_time)
        and player.player_id not in booked_player_ids
    ]

    player = random.choice(players) if players else None
    return player


async def filter_players_using_same_ip(players: list[Player], players_already_on_table: list[Player]) -> list[Player]:
    """
    Remove players that share the same IP configuration with players already on the table.
    Allows only one player per IP configuration on the table and one player without IP configuration.
    """
    if not players:
        return []

    if not players_already_on_table:
        return players

    player_ids = [player.player_id for player in players]
    players_on_table_ids = [player.player_id for player in players_already_on_table]

    # filter out duplicates
    filtered_players = [p for p in players if p.player_id not in players_on_table_ids]

    ip_pools_used_on_table = await IPPoolConfigurationService.get_multiple_configurations_for_players(players_on_table_ids)
    used_ip_pool_ids = [conf.ip_conf_id for conf in ip_pools_used_on_table]

    allowed_ip_pools = await IPPoolConfiguration.find(
        In(IPPoolConfiguration.assigned_player_ids, player_ids),
        NotIn(IPPoolConfiguration.ip_conf_id, used_ip_pool_ids),
    ).to_list()

    allowed_player_ids = set()
    for conf in allowed_ip_pools:
        for player_id in conf.assigned_player_ids:
            allowed_player_ids.add(player_id)

    filtered_players = [
        player for player in players
        if player.player_id in allowed_player_ids
    ]

    return filtered_players


@log_duration
async def find_players_to_stop(config: TablesAutomationConfig) -> list[Player]:
    name = f"tasks.tables_automation.find_players_to_stop:{config.app_id}_{config.config_id}"

    unfinished_launches = await Launch.find(
        Launch.config_id == config.config_id,
        Launch.finished == False,  # noqa: E712
    ).to_list()
    if not unfinished_launches:
        return []

    player_ids = [launch.player_id for launch in unfinished_launches]

    active_players = await Player.find(
        Player.status != PlayerStatus.IDLE.value,
        Or(
            Player.bot_type == BotType.PLAY.value,
            Player.bot_type == None,  # noqa: E711
        ),
        In(Player.player_id, player_ids)
    ).to_list()

    active_tables = await Table.find(
        Table.app_id == config.app_id,
        In(Table.table_id, [player.table_id for player in active_players])
    ).to_list()

    players_to_stop: list[Player] = []
    for player in active_players:
        if not player.bot_id:
            logger.error(name, f"Player {player.player_id} with status '{player.status}' has no bot_id")
            continue
        if not player.bot_type and not player.bot_id.startswith(BotType.PLAY.value):
            logger.error(name, f"Non-play bot {player.bot_id} for player {player.player_id} missing bot_type")
            continue

        launch = next(launch for launch in unfinished_launches if launch.player_id == player.player_id)

        hands_played_hard_limit = MAX_HANDS_PLAYED_ZOOM if launch.app_id == AppType.R7 else MAX_HANDS_PLAYED
        max_hands_played = launch.soft_max_hands_played or hands_played_hard_limit
        if player.hands_played > max_hands_played:
            logger.info(name, f"Will stop bot {player.bot_id}, hands_played: {player.hands_played} > {max_hands_played}")
            players_to_stop.append(player)

        elif config.max_rebuy_count and player.rebuy_count > config.max_rebuy_count:
            logger.info(name, f"Will stop bot {player.bot_id}, rebuy_count: {player.rebuy_count} >= {config.max_rebuy_count}")
            players_to_stop.append(player)

        if not player.table_id:
            logger.warning(name, f"Player {player.player_id} has no table_id, but has an active PLAY job, CHECK THIS")
            continue

        table = next((t for t in active_tables if t.table_id == player.table_id), None)
        if table is None:
            logger.warning(name, f"Table {player.table_id} not found (probably syncing)")
            continue

        elif table.players_total <= config.min_players_count_to_stop:
            logger.info(name, f"Will stop bot {player.bot_id}, players_total: "
                        f"{table.players_total} <= {config.min_players_count_to_stop} (min_players_to_stop)")
            players_to_stop.append(player)

        elif table.players_total >= config.max_players_count_to_stop:
            logger.info(name, f"Will stop bot {player.bot_id}, players_total: "
                        f"{table.players_total} >= {config.max_players_count_to_stop} (max_players_to_stop)")
            players_to_stop.append(player)

    logger.debug(name, f"Players to stop: {[p.player_id for p in players_to_stop]}")

    return players_to_stop
