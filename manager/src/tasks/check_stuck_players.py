from datetime import datetime, timedelta, timezone

from beanie.operators import NotIn, Or

from src.services.players import PlayerService, PlayerToUpdate
from src.utils.enums import PlayerStatus
from src.db.models import Player
from src.mq.bullmq_manager import mq_manager
from src.utils.logging import logger
from src.utils.settings import settings


async def check_stuck_players():
    name = "tasks.check_stuck_players"

    # Get all jobs from Redis
    existing_jobs = await mq_manager.get_jobs()
    existing_job_player_ids = set()
    for job in existing_jobs:
        player_id = job.data.get("playerId")
        if player_id:
            existing_job_player_ids.add(player_id)

    max_updated_at = datetime.now(timezone.utc) - timedelta(seconds=settings.job_stall_timeout)

    players = await Player.find(
        Player.enabled == True,  # noqa: E712
        NotIn(Player.player_id, existing_job_player_ids),
        Or(
            NotIn(Player.bot_id, ["", None]),
            NotIn(Player.status, [PlayerStatus.IDLE.value, PlayerStatus.PENDING.value]),
        ),
        Player.updated_at != None,  # noqa: E711
        Player.updated_at < max_updated_at,
    ).to_list()

    players_to_update = []
    for player in players:
        logger.warning(name, f"Job {player.bot_id} not found in MQ. Resetting player {player.player_id} from {player.status} to IDLE (no job found)")
        players_to_update.append(PlayerToUpdate(
            status=PlayerStatus.IDLE.value,
            player_id=player.player_id,
            bot_id="",
            table_id="",
            bot_type="",
            last_error="No job found, resetting state",
            need_balance_update=False,
        ))
    if players_to_update:
        await PlayerService.update_players(players_to_update)
        logger.warning(name, f"Reset {len(players_to_update)} stuck players to IDLE")
    else:
        logger.info(name, "No stuck players found")
