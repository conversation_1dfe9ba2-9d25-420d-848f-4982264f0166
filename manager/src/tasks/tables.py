from datetime import datetime
from zoneinfo import ZoneInfo
import json

from src.mq.bullmq_manager import mq_manager
from src.utils.lock import RedisLock
from src.utils.logging import log_duration, logger
from src.services.players import PlayerService
from src.services.tables import TableService, TableUpdate, IncomingTableDataDict
from src.services.tournaments import TournamentService, TournamentUpdate


@log_duration
async def update_tables_and_tournaments(redis_lock: RedisLock = None):
    name = "tasks.update_tables_and_tournaments"
    scan_players = await PlayerService.get_all_scan_players()
    for player in scan_players:
        job = await mq_manager.get_job(player.bot_id)
        if job is None:
            logger.error(name, f"Scan job not found for player {player.player_id}")
            continue

        if isinstance(job.progress, int):
            # task still not initialized
            continue

        try:
            data = job.progress.get("data", [])
        except Exception as e:
            logger.error(name, f"Failed to get tables data for player {player.player_id}", e)
            continue

        if not data:
            continue

        date_updated = datetime.fromtimestamp(job.progress.get("updatedAt") // 1000, ZoneInfo("UTC")).replace(microsecond=0)

        try:
            if "tables" in data or "tournaments" in data:
                if "tables" in data:
                    if "club_id" in data:
                        await update_tables(data.get("tables"), data.get("club_id"))
                    else:
                        await update_tables(data.get("tables"))
                if "tournaments" in data:
                    await update_tournaments(data.get("tournaments"), date_updated)
            elif 'status' in data:
                # expected format for running jobs
                pass
            else:
                logger.error(name, f"Wrong data format: data={data}")
        except Exception as e:
            logger.error(
                name,
                f"Failed to update tables and tournaments for player {player.player_id}",
                e,
            )


async def update_tables(tables_data: list[IncomingTableDataDict], club_id: int = None):
    name = "tables.update_tables"
    # prepare tables
    tables = []

    for table in tables_data:
        tables.append(
            TableUpdate.from_worker_data(table)
        )

    if tables or club_id:
        await TableService.drop_and_insert_tables(tables, club_id)
        logger.info(name, f"Updated {len(tables)} tables")


async def update_tournaments(tournaments_data: list[dict], date_updated: datetime):
    name = "tables.update_tournaments"
    logger.info(name, "Updating tournaments for all apps")
    # prepare tables
    tournaments: list[TournamentUpdate] = []

    if not date_updated:
        date_updated = datetime.now(ZoneInfo("UTC")).replace(microsecond=0)
    latest_tournament = await TournamentService.get_latest_tournament()
    time_delta = date_updated - latest_tournament.date_updated.replace(tzinfo=ZoneInfo("UTC"))

    if time_delta.total_seconds() < 1:
        # Skip updating tournaments if the date_updated is almost the same
        logger.info(
            name,
            f"Skipping tournaments update, same date_updated: {date_updated}",
        )
        return

    for tournament in tournaments_data:
        try:
            tournaments.append(
                TournamentUpdate(
                    tournament_id=str(tournament["id"]),
                    tournament_name=tournament["tournamentName"],
                    tournament_name_eng=json.loads(tournament["tournamentNameEng"]).get("En", ""),
                    starting_time=datetime.strptime(
                        tournament["startingTime"], "%Y-%m-%dT%H:%M:%S.%fZ"
                    ),
                    late_registration_time=datetime.strptime(
                        tournament["lateRegistrationTime"], "%Y-%m-%dT%H:%M:%S.%fZ"
                    )
                    if "lateRegistrationTime" in tournament
                    else None,
                    seats_per_table=tournament["seats"],
                    registration_fee=tournament["regFee"],
                    service_fee=tournament["srvFee"],
                    game_pool=tournament["gamePool"],
                    overlay=tournament["overlay"],
                    currency=tournament["displayCurrency"],
                    registered_count=tournament["registeredCount"],
                    joined_count=tournament["joinedCount"],
                    status=tournament["status"],
                    mtt_mode=tournament["mttMode"],
                    tournament_mode=tournament["tournamentMode"],
                    game_mode=tournament["gameMode"],
                    is_satellite_mode=tournament["isSatelliteMode"],
                    sign_up_options=tournament["signUpOptions"],
                    multi_flight_id=tournament["multiFlightId"],
                    multi_flight_level=tournament["multiFlightLevel"],
                    app_id=tournament["appId"],
                    date_updated=date_updated,
                )
            )
        except Exception as e:
            logger.error(name, f"Failed to update tournament {tournament.get('id')}", e)

    await TournamentService.drop_and_insert_tournaments(
        tournaments, date_updated=date_updated
    )

    await TournamentService.auto_adjust_game_pools([t.tournament_id for t in tournaments])

    logger.info(name, f"Updated {len(tournaments)} tournaments")
