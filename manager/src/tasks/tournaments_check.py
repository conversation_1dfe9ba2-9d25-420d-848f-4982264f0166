from beanie.operators import In

from src.db.models import FutureLaunch, FutureTournamentCheck, MultiflightConfiguration, Player
from src.schemas.requests import Seat
from src.services.bots import BotService
from src.utils.enums import BotType, PlayerStatus
from src.utils.logging import logger, log_duration


@log_duration
async def start_tournaments_checks():
    """ Process unchecked FutureTournamentCheck entries until started one bot """
    name = "tasks.start_tournaments_checks"

    busy_players = set()
    processed = False

    unchecked_checks = await FutureTournamentCheck.find(
        FutureTournamentCheck.checked == False,  # noqa: E712
    ).to_list()
    if not unchecked_checks:
        return

    players = await Player.find(
        In(Player.player_id, [check.player_id for check in unchecked_checks]),
        Player.status == PlayerStatus.IDLE.value,
    ).to_list()

    unchecked_tournament_ids = list(set(check.tournament_id for check in unchecked_checks))
    mf_configs = await MultiflightConfiguration.find(
        In(MultiflightConfiguration.day_one_tournament_ids, unchecked_tournament_ids)
    ).to_list()

    while not processed:
        check = unchecked_checks.pop()
        if not check:
            break

        if check.player_id in busy_players:
            logger.debug(name, f"Player {check.player_id} is busy, skipping")
            continue

        player = next((p for p in players if p.player_id == check.player_id), None)
        if not player:
            logger.error(name, f"Player {check.player_id} not found, marking check as done")
            check.checked = True
            await check.save()
            continue

        config = next((
            c for c in mf_configs
            if c.app_id == check.app_id
            and check.tournament_id in c.day_one_tournament_ids
        ), None)

        if not config:
            logger.warning(name, f"MultiflightConfiguration not found for {check.tournament_id=}")
            check.checked = True
            await check.save()
            continue

        if await FutureLaunch.find(
            FutureLaunch.player_id == check.player_id,
            FutureLaunch.tournament_id == config.day_two_tournament_id,
            FutureLaunch.app_id == check.app_id
        ).exists():
            logger.info(name, f"Player {check.player_id} already registered to {config.day_two_tournament_id=}")
            check.checked = True
            await check.save()
            continue

        logger.info(name, f"Starting check bot for {check.player_id=} tournament_id={config.day_two_tournament_id}")
        seat = Seat(table_id=config.day_two_tournament_id, player=player, app_id=config.app_id)

        try:
            await BotService.start_bot(seat=seat, bot_type=BotType.CHECK)
        except ValueError as e:
            logger.error(name, f"Failed to start check bot for player {player.player_id}", e)

        check.checked = True
        await check.save()

        processed = True

    await FutureTournamentCheck.find(
        FutureTournamentCheck.checked == True,  # noqa: E712
    ).delete()
