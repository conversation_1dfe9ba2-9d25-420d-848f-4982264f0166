from typing import Awaitable

from redis.asyncio import Redis
from redis.asyncio.cluster import RedisCluster


class HackedRedisCluster(Redis):
    def __init__(self, host: str, port: int, db: int):
        super().__init__(host=host, port=port, db=db)
        self.redis_cluster: RedisCluster = RedisCluster(
            host=host, port=port, decode_responses=True
        )

    async def evalsha(
        self, sha: str, numkeys: int, *keys_and_args: str
    ) -> Awaitable[str] | str:
        return await self.redis_cluster.evalsha(sha, numkeys, *keys_and_args)

    async def hgetall(self, name: str) -> Awaitable[dict]:
        return await self.redis_cluster.hgetall(name)

    def register_script(self, script: str) -> Awaitable[str]:
        return self.redis_cluster.register_script(script)
