import pytest
from unittest.mock import AsyncMock, patch

from fastapi.testclient import TestClient
from src.main import app
from src.db.models import Player, TaskLaunch
from src.utils.enums import PlayerStatus
from src.tasks.transfer import start_transfer_bot

client = TestClient(app)


@pytest.mark.asyncio
@patch("src.services.bots.mq_manager")
async def test_transfer_balance_success(mock_mq_manager, mock_db):
    await Player(player_id="p1", app_id=89, enabled=True, status=PlayerStatus.IDLE, platform_id=98, balance={"gold": 200}).save()

    payload = {
        "player_ids": ["p1"],
        "app_id": 89,
        "receiver_id": 666,
        "receiver_username": "receiver",
        "keep_amount": 50,
        "currency": "GOLD",
    }
    response = client.post("/players/transfer", json=payload)

    assert response.status_code == 200 or response.status_code == 204

    task = await TaskLaunch.find_one(
        TaskLaunch.player_id == "p1",
        TaskLaunch.type == "transfer",
        TaskLaunch.receiver_id == 666,
        TaskLaunch.transfer_amount == 150,  # 200 - 50
        TaskLaunch.currency == "GOLD"
    )
    assert task is not None

    mock_mq_manager.add_job = AsyncMock()
    mock_mq_manager.update_job = AsyncMock()
    await start_transfer_bot()

    player = await Player.find_one(Player.player_id == "p1")
    assert player.bot_id == "transfer:98:p1"

    assert mock_mq_manager.add_job.call_count == 1

    _, args = mock_mq_manager.add_job.call_args
    assert args["data"]["currency"] == "GOLD"
    assert args["data"]["transferAmount"] == 150
    assert args["job_id"] == "transfer:98:p1"
    assert args["job_type"] == "transfer"
    assert args["attempts"] == 2
