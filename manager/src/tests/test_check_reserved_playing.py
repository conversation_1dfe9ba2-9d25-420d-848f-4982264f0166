import pytest
from datetime import datetime, timezone
from unittest.mock import AsyncMock

from src.tasks.check_reserved_playing import check_reserved_playing  # adjust import
from src.utils.enums import PlayerStatus


@pytest.mark.asyncio
async def test_no_future_launches(monkeypatch):
    # Patch FutureLaunch.find().to_list() to return empty
    monkeypatch.setattr(
        "src.tasks.check_reserved_playing.FutureLaunch.find",
        lambda *a, **kw: AsyncMock(to_list=AsyncMock(return_value=[])),
    )
    # Patch Player.find().to_list() to return empty
    monkeypatch.setattr(
        "src.tasks.check_reserved_playing.Player.find",
        lambda *a, **kw: AsyncMock(to_list=AsyncMock(return_value=[])),
    )
    # Patch stop_bot
    stop_bot = AsyncMock()
    monkeypatch.setattr("src.tasks.check_reserved_playing.BotService.stop_bot", stop_bot)

    await check_reserved_playing()

    stop_bot.assert_not_called()


@pytest.mark.asyncio
async def test_player_on_correct_tournament(monkeypatch):
    # Fake future launch
    future_launch = type(
        "FutureLaunch",
        (),
        {
            "player_id": "p1",
            "app_id": "app1",
            "tournament_id": "t1",
            "starting_time": datetime.now(timezone.utc),
        },
    )()

    # Fake player matching that launch
    player = type(
        "Player",
        (),
        {
            "player_id": "p1",
            "app_id": "app1",
            "table_id": "t1",
            "status": PlayerStatus.PLAYING.value,
            "bot_id": "b1",
        },
    )()

    monkeypatch.setattr(
        "src.tasks.check_reserved_playing.FutureLaunch.find",
        lambda *a, **kw: AsyncMock(to_list=AsyncMock(return_value=[future_launch])),
    )
    monkeypatch.setattr(
        "src.tasks.check_reserved_playing.Player.find",
        lambda *a, **kw: AsyncMock(to_list=AsyncMock(return_value=[player])),
    )
    stop_bot = AsyncMock()
    monkeypatch.setattr("src.tasks.check_reserved_playing.BotService.stop_bot", stop_bot)

    await check_reserved_playing()

    stop_bot.assert_not_called()


@pytest.mark.asyncio
async def test_player_mismatch_launch(monkeypatch):
    # Fake future launch
    future_launch = type(
        "FutureLaunch",
        (),
        {
            "player_id": "p1",
            "app_id": "app1",
            "tournament_id": "t1",
            "starting_time": datetime.now(timezone.utc),
        },
    )()

    # Fake player that mismatches
    player = type(
        "Player",
        (),
        {
            "player_id": "p1",
            "app_id": "appX",  # different app
            "table_id": "t999",  # wrong table
            "status": PlayerStatus.PLAYING.value,
            "bot_id": "b1",
        },
    )()

    monkeypatch.setattr(
        "src.tasks.check_reserved_playing.FutureLaunch.find",
        lambda *a, **kw: AsyncMock(to_list=AsyncMock(return_value=[future_launch])),
    )
    monkeypatch.setattr(
        "src.tasks.check_reserved_playing.Player.find",
        lambda *a, **kw: AsyncMock(to_list=AsyncMock(return_value=[player])),
    )
    stop_bot = AsyncMock()
    monkeypatch.setattr("src.tasks.check_reserved_playing.BotService.stop_bot", stop_bot)

    await check_reserved_playing()

    stop_bot.assert_called_once()
    args, kwargs = stop_bot.call_args
    assert args[0] == "b1"
    assert "reserved for a game" in kwargs["reason"]
