import pytest
from unittest.mock import patch
from fastapi.testclient import TestClient
from src.main import app
from src.db.models import Player

client = TestClient(app)


@pytest.mark.asyncio
async def test_transfer_balance_success(mock_db):
    await Player(player_id="p1", app_id=1, enabled=True, status="idle", platform_id=98, balance={"gold": 200}).save()
    await Player(player_id="p2", app_id=1, enabled=True, status="idle", platform_id=98, balance={"gold": 100}).save()

    payload = {
        "player_ids": ["p1"],
        "receiver_id": 2,
        "receiver_username": "receiver",
        "keep_amount": 50,
        "currency": "GOLD",
    }
    response = client.post("/players/transfer", json=payload)
    assert response.status_code == 200 or response.status_code == 204


def test_transfer_balance_no_players(mock_db):
    payload = {
        "player_ids": ["notfound"],
        "receiver_id": 2,
        "receiver_username": "receiver",
        "keep_amount": 50,
        "currency": "GOLD",
    }
    response = client.post("/players/transfer", json=payload)
    assert response.status_code == 200 or response.status_code == 204


@pytest.mark.asyncio
@patch("src.services.worker_service.WorkerService.get_user_id_from_worker")
@patch("src.services.platforms.wptgo.WptgoService.change_avatar")
async def test_change_avatar_success(wptgo_change_avatar_mock, worker_get_user_id_mock, mock_db):
    await Player(player_id="test_player_id", app_id=1, enabled=True, status="idle", platform_id=101, balance={"gold": 200}).save()

    worker_get_user_id_mock.return_value = "test_user_id"
    wptgo_change_avatar_mock.return_value = {
        "avatarType": 2,
        "avatarUrl": "http://34.126.94.45:2631/avatar_41792_1754578510.png?avatarType=Custom"
    }

    payload = {
        "avatarBase64": "test_avatar_base64",
        "imgExt": "png",
    }
    response = client.post("/players/test_player_id/change_avatar", json=payload)
    assert response.status_code == 200
    assert response.json()["data"]["avatar_url"]
    assert response.json()["data"]["avatar_changed"]
