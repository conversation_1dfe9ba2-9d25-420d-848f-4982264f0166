import setup  # noqa: F401

# from bot_manager_interfaces.enums import AppType, PlatformType
# from bot_manager_interfaces import UserData


def test_app_type():
    return
    # assert AppType.DIAMOND == 82
    # assert AppType.MTT == 83
    # assert AppType.R4 == 84
    # assert AppType.SPLASH == 85
    # assert AppType.SPLASH_DIAMOND == 86
    # assert AppType.ZOOM == 87
    # assert AppType.SHORTDECK == 89


def test_user_data():
    return
    # user_data = UserData(
    #     platform_id=PlatformType.RWPK,
    #     user_id=1234567890,
    #     username="test_user",
    #     country_code="US",
    #     area_code="123",
    #     phone_num="1234567890",
    #     email="<EMAIL>",
    # )
    # assert user_data.platform_id == PlatformType.RWPK
    # assert user_data.user_id == 1234567890
    # assert user_data.username == "test_user"
    # assert user_data.country_code == "US"
    # assert user_data.area_code == "123"
    # assert user_data.phone_num == "1234567890"
    # assert user_data.email == "<EMAIL>"
