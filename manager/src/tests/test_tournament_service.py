import pytest
from datetime import datetime, timedelta

from src.db.models import Tournament, TournamentConfiguration, Player
from src.services.tournaments import TournamentService
from src.utils.enums import CurrencyType


def test_build_tournament_response():
    tournament = Tournament(
        tournament_id="t1",
        tournament_name="Tournament 1",
        tournament_name_eng="Tournament 1",
        starting_time=datetime.now() + timedelta(minutes=30),
        seats_per_table=99,
        registration_fee=42.0,
        currency="USD",
        app_id=1,
        game_pool=69.0
    )
    player = Player(
        player_id='1',
        app_id=69,
        enabled=True,
        status='whatever',
        rank=3,
        chips=42,
    )

    response = TournamentService.build_tournament_response(tournament, None, [player])
    assert response.highest_rank == 3
    assert response.highest_chips == 42
    assert response.overlay_percentage == pytest.approx(42/69)

    config = TournamentConfiguration(
        app_id=42,
        tournament_id="t1",
        game_pool=40,
        game_pool_updated_manually=True
    )

    response = TournamentService.build_tournament_response(tournament, config, [player])
    assert response.overlay_percentage == 42/40
    assert response.adjusted_game_pool_updated_manually

    config.game_pool = 0
    response = TournamentService.build_tournament_response(tournament, config, [player])
    assert response.overlay_percentage == 42/69


@pytest.mark.asyncio
async def test_update_ajdusted_game_pools(mock_db):
    await Tournament.insert_many([
        Tournament(
            tournament_id="t1",
            tournament_name="Tournament 1",
            starting_time=datetime.now() + timedelta(minutes=30),
            seats_per_table=99,
            registration_fee=42,
            currency="USD",
            app_id=1,
            game_pool=69
        ),
        Tournament(
            tournament_id="t2",
            tournament_name="Tournament 2",
            starting_time=datetime.now() + timedelta(minutes=30),
            seats_per_table=99,
            registration_fee=42,
            currency="USD",
            app_id=1,
            game_pool=666
        ),
    ])
    await TournamentConfiguration(
        app_id=1,
        tournament_id='t1',
        game_pool=42
    ).save()

    await TournamentService.update_tournament_configurations(
        app_id=1,
        tournament_ids=['t1', 't2'],
        adjusted_game_pool=13,
        adjusted_game_pool_updated_manually=True
    )

    tournaments = await Tournament.find_all().to_list()
    assert await TournamentService.get_adjusted_game_pool(tournaments[0]) == (13, True)
    assert await TournamentService.get_adjusted_game_pool(tournaments[1]) == (13, True)


@pytest.mark.asyncio
async def test_auto_adjust_game_pools_sets_override_for_small_game_pool(mock_db):
    # Tournament with game_pool < threshold, no config exists
    t = await Tournament(
        tournament_id="t1", tournament_name="T1", starting_time=datetime.now(),
        seats_per_table=9, registration_fee=10.0, currency=CurrencyType.USD.value, app_id=1, game_pool=1.0
    ).save()
    await TournamentService.auto_adjust_game_pools([t.tournament_id])
    config = await TournamentConfiguration.find_one(TournamentConfiguration.tournament_id == "t1")
    assert config is not None
    assert config.game_pool == 10


@pytest.mark.asyncio
async def test_auto_adjust_game_pools_does_not_override_large_game_pool(mock_db):
    # Tournament with game_pool > threshold, no config exists
    t = await Tournament(
        tournament_id="t2", tournament_name="T2", starting_time=datetime.now(),
        seats_per_table=9, registration_fee=10.0, currency=CurrencyType.USD.value, app_id=1, game_pool=100000.0
    ).save()
    await TournamentService.auto_adjust_game_pools([t.tournament_id])
    config = await TournamentConfiguration.find_one(TournamentConfiguration.tournament_id == "t2")
    # Should not create config for large game_pool
    assert config is None


@pytest.mark.asyncio
async def test_auto_adjust_game_pools_respects_manual_override(mock_db):
    # Tournament with small game_pool, config exists and is manually updated
    t = await Tournament(
        tournament_id="t3", tournament_name="T3", starting_time=datetime.now(),
        seats_per_table=9, registration_fee=10.0, currency=CurrencyType.USD.value, app_id=1, game_pool=1.0
    ).save()
    await TournamentConfiguration(
        tournament_id="t3", app_id=1, game_pool=123, game_pool_updated_manually=True
    ).save()
    await TournamentService.auto_adjust_game_pools([t.tournament_id])
    config = await TournamentConfiguration.find_one(TournamentConfiguration.tournament_id == "t3")
    # Should not override manually updated config
    assert config.game_pool == 123
    assert config.game_pool_updated_manually


@pytest.mark.asyncio
async def test_auto_adjust_game_pools_overrides_non_manual_config(mock_db):
    # Tournament with small game_pool, config exists and is not manually updated
    t = await Tournament(
        tournament_id="t4", tournament_name="T4", starting_time=datetime.now(),
        seats_per_table=9, registration_fee=10.0, currency=CurrencyType.USD.value, app_id=1, game_pool=400.0  # < 499
    ).save()
    await TournamentConfiguration(
        tournament_id="t4", app_id=1, game_pool=123, game_pool_updated_manually=False
    ).save()
    await TournamentService.auto_adjust_game_pools([t.tournament_id])
    config = await TournamentConfiguration.find_one(TournamentConfiguration.tournament_id == "t4")
    assert config.game_pool == 10
    assert not config.game_pool_updated_manually
