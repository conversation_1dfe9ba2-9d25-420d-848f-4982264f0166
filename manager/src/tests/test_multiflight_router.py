from fastapi.testclient import TestClient

from src.main import app

client = TestClient(app)


def test_add_tournament_checks(mock_db):
    payload = {
        "checks": [
            {
                "player_id": "b4c94edd-47fd-412a-9f9a-ad8dbb881567",
                "tournament_id": "123456",
                "app_id": 83,
            },
            {
                "player_id": "601a1f24-3806-4078-b7db-b80327aa7e66",
                "tournament_id": "654321",
                "app_id": 83,
            },
        ]
    }
    response = client.post("/multiflight/tournament_checks", json=payload)
    assert response.status_code == 201
    response_data = response.json()
    assert response_data["code"] == 201
    data = response_data["data"]
    assert data[0]["player_id"] == "b4c94edd-47fd-412a-9f9a-ad8dbb881567"
    assert data[1]["player_id"] == "601a1f24-3806-4078-b7db-b80327aa7e66"
