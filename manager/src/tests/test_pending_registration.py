import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta, timezone

from src.db.models import PendingRegistration
from src.services.player_registration_service import PlayerRegistrationService
from src.tasks.registration_automation import process_pending_registration, prepare_register_request


@pytest.mark.asyncio
@patch("src.tasks.registration_automation.try_to_register_user")
@patch("src.db.models.RegistrationAutomationConfig.find")
@patch("src.db.models.PendingRegistration.find")
async def test_process_pending_registration_sets_next_time(
    mock_pending_find,
    mock_config_find,
    mock_try_to_register_user,
):
    now = datetime.now(timezone.utc)

    # Mock config.find().first_or_none()
    config_mock = MagicMock()
    config_mock.is_enabled = True
    config_mock.next_registration_time = now - timedelta(seconds=1)
    config_mock.min_delay_sec = 10
    config_mock.max_delay_sec = 20
    config_mock.save = AsyncMock()
    mock_config_find.return_value.first_or_none = AsyncMock(return_value=config_mock)

    # Mock PendingRegistration.find().first_or_none()
    pending_mock = MagicMock()
    pending_mock.status = "pending"
    pending_mock.save = AsyncMock()
    mock_pending_find.return_value.first_or_none = AsyncMock(return_value=pending_mock)

    # Mock the registration service
    fake_service = AsyncMock(spec=PlayerRegistrationService)

    # Actual test call
    await process_pending_registration(fake_service)

    # Check the mock was called
    mock_try_to_register_user.assert_awaited_once()

    # Check the config was updated
    assert config_mock.save.called
    assert config_mock.next_registration_time > now


def test_prepare_register_request_success():
    from src.db.models import PendingRegistration
    from src.schemas.requests import RegisterPlayerRequest

    # Create a mock PendingRegistration object
    pending_registration = PendingRegistration(
        platform_id=98,
        app_id=1,
        account="test_account",
        username="test_user",
        country_code="US",
        phone_number="**********",
        extra_data={"some_key": "some_value"},
        password="test_password",
        area_code="+82",
        receiver_id=12345,
        receiver_username="receiver_user",
    )
    # Prepare the request
    request = prepare_register_request(pending_registration)
    # Check the request is correctly formed
    assert isinstance(request, RegisterPlayerRequest)
    assert request.platform_id == 98
    assert request.app_id == 1
    assert request.account == "test_account"
    assert request.username == "test_user"
    assert request.country_code == "US"
    assert request.phone_number == "**********"
    assert request.model_extra == {"some_key": "some_value"}


def test_optional_fields_in_pending_registration_response():

    # Create a mock PendingRegistration object
    pending_registration = PendingRegistration(
        platform_id=98,
        app_id=1,
        account="test_account",
        username="test_user",
        country_code="US",
        phone_number="**********",
        extra_data={"some_key": "some_value"},
        password="test_password",
        area_code="+82",
        receiver_id=12345,
        receiver_username="receiver_user",
    )

    # Create the response
    response = pending_registration.to_response()

    # Check that all fields are present
    assert response.platform_id == 98
    assert response.app_id == 1
    assert response.account == "test_account"
    assert response.username == "test_user"
    assert response.country_code == "US"
    assert response.phone_number == "**********"
    assert response.area_code == "+82"
    assert response.receiver_id == 12345
    assert response.receiver_username == "receiver_user"
    # Check that extra_data is correctly included
    assert response.some_key == "some_value"
