from uuid import uuid4

import pytest
import pytest_asyncio

from src.db.models import IPPoolConfiguration
from src.schemas.requests import IPPoolConfigurationCreateRequest
from src.services.ip_pool_service import IPPoolConfigurationService
from src.services.players import PlayerService
from src.utils.enums import PlayerStatus
from src.utils.settings import settings

from src.tests.fixtures import CnPlayer as Player


ip_conf_id1 = str(uuid4())
ip_conf_id2 = str(uuid4())
ip_conf_id3 = str(uuid4())
ip_conf_id4 = str(uuid4())
test_player_a_id = "playerA"
test_player_b_id = "playerB"
test_player_c_id = "playerC"
test_player_d_id = "playerD"
test_player_e_id = "playerE"
test_app_id1 = 82
test_app_id2 = 83
test_app_id3 = 84


@pytest_asyncio.fixture
async def mock_data(mock_db):
    settings.ip_pool_enabled = True

    # Cleanup before each test
    await IPPoolConfiguration.get_motor_collection().delete_many({})

    sample_data_ip_conf = [
        IPPoolConfiguration(
            ip_conf_id=ip_conf_id1,
            full_proxy_url="http://proxy1.example.com:8080",
            external_ip="*************",
            country_code="CN",
            assigned_player_ids=[test_player_a_id]  # app_id1
        ),
        IPPoolConfiguration(
            ip_conf_id=ip_conf_id2,
            full_proxy_url="http://proxy2.example.com:8080",
            external_ip="*************",
            country_code="CN",
            assigned_player_ids=[test_player_c_id]  # app_id2
        ),
        IPPoolConfiguration(
            ip_conf_id=ip_conf_id3,
            full_proxy_url="http://proxy3.example.com:8080",
            external_ip="*************",
            country_code="CN",
            assigned_player_ids=[]
        ),
        IPPoolConfiguration(
            ip_conf_id=ip_conf_id4,
            full_proxy_url="http://proxy4.example.com:8080",
            external_ip="*************",
            country_code="CN",
            assigned_player_ids=[]
        ),
    ]

    sample_players_data = [
        Player(
            player_id=test_player_a_id,
            app_id=test_app_id1,
            enabled=True,
            status=PlayerStatus.IDLE.value,
        ),
        Player(
            player_id=test_player_b_id,
            app_id=test_app_id1,
            enabled=True,
            status=PlayerStatus.IDLE.value,
        ),
        Player(
            player_id=test_player_c_id,
            app_id=test_app_id2,
            enabled=True,
            status=PlayerStatus.IDLE.value,
        ),
        Player(
            player_id=test_player_d_id,
            app_id=test_app_id2,
            enabled=True,
            status=PlayerStatus.IDLE.value,
        ),
        Player(
            player_id=test_player_e_id,
            app_id=test_app_id3,
            enabled=True,
            status=PlayerStatus.IDLE.value,
        )
    ]

    # Insert test data on database initialization
    await IPPoolConfiguration.insert_many(sample_data_ip_conf)
    await Player.insert_many(sample_players_data)


@pytest.mark.asyncio
async def test_get_configuration(mock_data):
    all_conf = await IPPoolConfigurationService.get_all()
    assert len(all_conf) == 4

    first_conf_id = all_conf[0].ip_conf_id
    first_conf = await IPPoolConfigurationService.get_by_id(first_conf_id)
    assert first_conf is not None
    assert first_conf.ip_conf_id == first_conf_id

    player = await Player.find_one(Player.player_id == test_player_a_id)

    ip_conf = await IPPoolConfigurationService.get_configuration_for_player(player)
    assert ip_conf is not None
    assert ip_conf.full_proxy_url == "http://proxy1.example.com:8080"
    assert ip_conf.external_ip == "*************"
    assert ip_conf.country_code == "CN"

    settings.ip_pool_enabled = False

    all_conf = await IPPoolConfigurationService.get_all()
    assert len(all_conf) == 0


@pytest.mark.asyncio
async def test_create(mock_data):
    full_proxy_url = "http://user5:<EMAIL>:1234"
    external_ip = "*************"
    ip_conf = await IPPoolConfigurationService.create(IPPoolConfigurationCreateRequest(
        full_proxy_url=full_proxy_url,
        external_ip=external_ip,
        country_code="ES"
    ))
    assert ip_conf is not None
    assert ip_conf.full_proxy_url == full_proxy_url
    assert ip_conf.external_ip == external_ip
    assert ip_conf.host == "proxy5.example.com"
    assert ip_conf.port == 1234
    assert ip_conf.username == "user5"
    assert ip_conf.password == "pass5"
    assert ip_conf.country_code == "ES"

    ip_conf = await IPPoolConfigurationService.get_by_id(ip_conf_id=ip_conf.ip_conf_id)
    assert ip_conf is not None
    assert ip_conf.full_proxy_url == full_proxy_url
    assert ip_conf.external_ip == external_ip
    assert ip_conf.host == "proxy5.example.com"
    assert ip_conf.port == 1234
    assert ip_conf.username == "user5"
    assert ip_conf.password == "pass5"
    assert ip_conf.country_code == "ES"

    # Duplicate
    with pytest.raises(Exception, match="already exists"):
        await IPPoolConfigurationService.create(IPPoolConfigurationCreateRequest(
            full_proxy_url=full_proxy_url,
            external_ip="*******",
            country_code="GB"
        ))
    # Duplicate
    with pytest.raises(Exception, match="already exists"):
        await IPPoolConfigurationService.create(IPPoolConfigurationCreateRequest(
            full_proxy_url="http://sdsdsd@sdsdsd:3030",
            external_ip=external_ip,
            country_code="GB"
        ))
    # Non existing country
    with pytest.raises(Exception, match="country_code"):
        await IPPoolConfigurationService.create(IPPoolConfigurationCreateRequest(
            full_proxy_url="http://sdsdsd@sdsdsd:3030",
            external_ip="*******",
            country_code="KK"
        ))

    # Testing deletion
    await IPPoolConfigurationService.delete(ip_conf_id=ip_conf.ip_conf_id)
    ip_conf_reloaded = await IPPoolConfigurationService.get_by_id(ip_conf_id=ip_conf.ip_conf_id)
    assert ip_conf_reloaded is None


@pytest.mark.asyncio
async def test_update(mock_data):
    external_ip = "*************"
    ip_conf = await IPPoolConfigurationService.update(ip_conf_id=ip_conf_id1, request=IPPoolConfigurationCreateRequest(
        external_ip=external_ip,
        full_proxy_url="http://proxy2.example.com:8080",
        country_code="UA"
    ))

    assert ip_conf is not None
    assert ip_conf.ip_conf_id == ip_conf_id1
    assert ip_conf.full_proxy_url == "http://proxy2.example.com:8080"
    assert ip_conf.external_ip == external_ip
    assert ip_conf.country_code == "UA"

    username = "user1"
    password = "pass"
    host = "host1"
    port = 1234
    full_proxy_url = f"http://{username}:{password}@{host}:{port}"
    ip_conf = await IPPoolConfigurationService.update(ip_conf_id=ip_conf_id1, request=IPPoolConfigurationCreateRequest(
        full_proxy_url=full_proxy_url,
        external_ip=external_ip,
        country_code="US"
    ))

    assert ip_conf is not None
    assert ip_conf.ip_conf_id == ip_conf_id1
    assert ip_conf.full_proxy_url == full_proxy_url
    assert ip_conf.external_ip == external_ip
    assert ip_conf.country_code == "US"
    assert ip_conf.username == username
    assert ip_conf.password == password
    assert ip_conf.host == host
    assert ip_conf.port == port


# ASSIGNMENT TESTS
@pytest.mark.asyncio
async def test_assign_max_players_1(mock_data):
    settings.max_players_per_ip = 1

    # Player D with test_app_id2 should be allowed to be assigned to ip_conf_id3 which is empty
    ip_conf = await IPPoolConfigurationService.assign_player_to_ip(player_id=test_player_d_id, ip_conf_id=ip_conf_id3)
    assert ip_conf is not None
    assert ip_conf.full_proxy_url == "http://proxy3.example.com:8080"
    assert ip_conf.external_ip == "*************"
    assert test_player_d_id in ip_conf.assigned_player_ids

    # test_player_b_id assign to ip_conf_id2 which has already a player
    with pytest.raises(Exception) as exc_info:
        await IPPoolConfigurationService.assign_player_to_ip(player_id=test_player_b_id, ip_conf_id=ip_conf_id1)

    assert "IP Pool configuration has already maximum possible players" in str(exc_info.value)


@pytest.mark.asyncio
async def test_assign_max_players_2(mock_data):
    settings.max_players_per_ip = 2
    # Player D with test_app_id2 should be allowed to be assigned to ip_conf_id2 which has test_player_c_id with test_app_id2
    with pytest.raises(Exception):
        await IPPoolConfigurationService.assign_player_to_ip(player_id="playerD", ip_conf_id=ip_conf_id2)

    # Player D with test_app_id2 should be allowed to be assigned to ip_conf_id1 which has test_player_a_id with test_app_id1
    ip_conf = await IPPoolConfigurationService.assign_player_to_ip(player_id=test_player_d_id, ip_conf_id=ip_conf_id1)
    assert ip_conf is not None
    assert ip_conf.full_proxy_url == "http://proxy1.example.com:8080"
    assert ip_conf.external_ip == "*************"
    assert test_player_d_id in ip_conf.assigned_player_ids
    assert test_player_a_id in ip_conf.assigned_player_ids

    # Hit the max players per ip
    with pytest.raises(Exception):
        await IPPoolConfigurationService.assign_player_to_ip(player_id=test_player_b_id, ip_conf_id=ip_conf_id1)


@pytest.mark.asyncio
async def test_assign_max_players_3(mock_data):
    settings.max_players_per_ip = 3

    # Player D with test_app_id2 should be allowed to be assigned to ip_conf_id1 which has test_player_a_id with test_app_id1
    ip_conf = await IPPoolConfigurationService.assign_player_to_ip(player_id=test_player_d_id, ip_conf_id=ip_conf_id1)
    assert ip_conf is not None
    assert ip_conf.full_proxy_url == "http://proxy1.example.com:8080"
    assert test_player_d_id in ip_conf.assigned_player_ids
    assert test_player_a_id in ip_conf.assigned_player_ids

    # Player E with test_app_id3 should be allowed to be assigned to ip_conf_id1 which has test_player_a_id with test_app_id1
    ip_conf = await IPPoolConfigurationService.assign_player_to_ip(player_id=test_player_e_id, ip_conf_id=ip_conf_id1)
    assert ip_conf is not None
    assert ip_conf.full_proxy_url == "http://proxy1.example.com:8080"
    assert test_player_e_id in ip_conf.assigned_player_ids


@pytest.mark.asyncio
async def test_assign_player_already_assigned_to_another_ip(mock_data):
    # First assign player to ip_conf_id3
    ip_conf = await IPPoolConfigurationService.assign_player_to_ip(player_id=test_player_b_id, ip_conf_id=ip_conf_id3)
    assert ip_conf is not None
    assert ip_conf.ip_conf_id == ip_conf_id3
    assert test_player_b_id in ip_conf.assigned_player_ids

    # Try to assign the same player to ip_conf_id4
    with pytest.raises(Exception) as exc_info:
        await IPPoolConfigurationService.assign_player_to_ip(player_id=test_player_b_id, ip_conf_id=ip_conf_id4)

    assert str(
        exc_info.value) == f"Player ({test_player_b_id}) is already assigned to another IP Pool configuration ({ip_conf_id3})"

    # Verify player is still assigned to ip_conf_id3
    ip_conf = await IPPoolConfigurationService.get_by_id(ip_conf_id3)
    assert ip_conf is not None
    assert test_player_b_id in ip_conf.assigned_player_ids

    # Verify player is not assigned to ip_conf_id4
    ip_conf = await IPPoolConfigurationService.get_by_id(ip_conf_id4)
    assert ip_conf is not None
    assert test_player_b_id not in ip_conf.assigned_player_ids

    # Unassign player from ip_conf_id3
    ip_conf = await IPPoolConfigurationService.unassign_player_from_ip(player_id=test_player_b_id,
                                                                       ip_conf_id=ip_conf_id3)
    assert ip_conf is not None
    assert test_player_b_id not in ip_conf.assigned_player_ids

    # Now assign player to ip_conf_id4
    ip_conf = await IPPoolConfigurationService.assign_player_to_ip(player_id=test_player_b_id, ip_conf_id=ip_conf_id4)
    assert ip_conf is not None
    assert ip_conf.ip_conf_id == ip_conf_id4
    assert test_player_b_id in ip_conf.assigned_player_ids

    # Verify player is no longer assigned to ip_conf_id3
    ip_conf = await IPPoolConfigurationService.get_by_id(ip_conf_id3)
    assert ip_conf is not None
    assert test_player_b_id not in ip_conf.assigned_player_ids


@pytest.mark.asyncio
async def test_unassign(mock_data):
    ip_conf = await IPPoolConfigurationService.unassign_player_from_ip(player_id=test_player_c_id,
                                                                       ip_conf_id=ip_conf_id2)
    assert ip_conf is not None
    assert ip_conf.full_proxy_url == "http://proxy2.example.com:8080"
    assert ip_conf.external_ip == "*************"
    assert ip_conf.assigned_player_ids == []


@pytest.mark.asyncio
async def test_players_data_extended(mock_data):
    players = await PlayerService.get_players(app_id=test_app_id1)
    assert len(players) == 2
    for player in players:
        if player.player_id == test_player_a_id:
            assert player.ip_conf_id == ip_conf_id1
            assert player.external_ip == "*************"
        elif player.player_id == test_player_b_id:
            assert player.ip_conf_id is None
            assert player.external_ip is None

    settings.ip_pool_enabled = False

    players = await PlayerService.get_players(app_id=test_app_id1)
    for player in players:
        assert player.ip_conf_id is None
        assert player.external_ip is None


@pytest.mark.asyncio
async def test_get_free_ip(mock_data):
    ip_conf = await IPPoolConfigurationService.get_free_ip(app_id=test_app_id1)
    assert ip_conf is not None
    assert ip_conf.ip_conf_id == ip_conf_id3 or ip_conf.ip_conf_id == ip_conf_id4

    ip_conf = await IPPoolConfigurationService.get_free_ip(app_id=test_app_id2)
    assert ip_conf is not None
    assert ip_conf.ip_conf_id == ip_conf_id3 or ip_conf.ip_conf_id == ip_conf_id4


@pytest.mark.asyncio
async def test_assign_player_to_new_free_ip_max_players_1(mock_data):
    settings.max_players_per_ip = 1

    ip_conf = await IPPoolConfigurationService.assign_player_to_new_free_ip(player_id=test_player_b_id,
                                                                            app_id=test_app_id1)
    assert ip_conf is not None
    assert ip_conf.ip_conf_id == ip_conf_id3 or ip_conf.ip_conf_id == ip_conf_id4

    ip_conf = await IPPoolConfigurationService.assign_player_to_new_free_ip(player_id=test_player_d_id,
                                                                            app_id=test_app_id2)
    assert ip_conf is not None
    assert ip_conf.ip_conf_id == ip_conf_id3 or ip_conf.ip_conf_id == ip_conf_id4

    ip_conf = await IPPoolConfigurationService.assign_player_to_new_free_ip(player_id=test_player_e_id,
                                                                            app_id=test_app_id3)
    assert ip_conf is None


@pytest.mark.asyncio
async def test_assign_player_to_new_free_ip_max_players_2(mock_data):
    settings.max_players_per_ip = 2

    # removing empty IP Pool configurations for proper testing
    await IPPoolConfiguration.find_one(IPPoolConfiguration.ip_conf_id == ip_conf_id3).delete()
    await IPPoolConfiguration.find_one(IPPoolConfiguration.ip_conf_id == ip_conf_id4).delete()

    ip_conf = await IPPoolConfigurationService.assign_player_to_new_free_ip(player_id=test_player_b_id,
                                                                            app_id=test_app_id1)
    assert ip_conf is not None
    assert ip_conf.ip_conf_id == ip_conf_id2

    ip_conf = await IPPoolConfigurationService.assign_player_to_new_free_ip(player_id=test_player_d_id,
                                                                            app_id=test_app_id2)
    assert ip_conf is not None
    assert ip_conf.ip_conf_id == ip_conf_id1

    ip_conf = await IPPoolConfigurationService.assign_player_to_new_free_ip(player_id=test_player_e_id,
                                                                            app_id=test_app_id3)
    assert ip_conf is None
