import setup  # noqa: F401
from src.services.platforms.wpk import WPKService


def test_someting():
    assert True


def test_wpk_prepare_data_for_registration():
    wpk = WPKService()
    data = wpk._prepare_data_for_registration("test", "test", "test", "CN")
    assert data["nickname"] == "test"
    assert data["password"] == "test"
    assert data.get("countryCode", False)
    assert data.get("phoneNum", False)
