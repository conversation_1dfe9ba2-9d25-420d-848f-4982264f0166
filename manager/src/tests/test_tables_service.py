import pytest
import pytest_asyncio

from src.db.models import Table
from src.services.tables import TableService, TableUpdate, IncomingTableDataDict
from src.tasks.tables import update_tables


@pytest_asyncio.fixture
async def mock_table_data() -> IncomingTableDataDict:
    return {
        "appId": 1,
        "tableId": "1476781",
        "gameType": "NLHE",
        "gameMode": 1,
        "roomMode": 0,
        "currency": "USD",
        "blinds": [10000, 20000, 40000],
        "straddle": True,
        "ante": 0,
        "playersCount": 0,
        "maxPlayers": 3,
        "leftSeats": 8,
        "tableName": "HLZ3726"
    }


@pytest_asyncio.fixture
async def mock_table_data_missing_properties() -> dict:
    return {
        "tableId": "1476782",
        "gameType": "PLO",
        # Missing: gameMode, roomMode, currency, blinds, straddle, ante, playersCount, maxPlayers, leftSeats, tableName
    }


@pytest.mark.asyncio
async def test_update_tables_with_players_count(mock_db, mock_table_data: IncomingTableDataDict):
    # Create TableUpdate with players_count
    table_update = TableUpdate.from_worker_data(mock_table_data)

    # Save table
    await TableService.drop_and_insert_tables([table_update])

    tables = await Table.find_all().to_list()
    assert len(tables) == 1
    table = tables[0]

    # Verify players_count is saved correctly
    assert table.gaming_configuration.game == mock_table_data["gameType"]
    assert table.gaming_configuration.blinds == mock_table_data["blinds"]
    assert table.gaming_configuration.ante == mock_table_data["ante"]
    assert table.gaming_configuration.game_mode == mock_table_data["gameMode"]
    assert table.gaming_configuration.room_mode == mock_table_data["roomMode"]
    assert table.gaming_configuration.straddle == mock_table_data["straddle"]
    assert table.currency == mock_table_data["currency"]
    assert table.players_total == mock_table_data["playersCount"]
    assert table.empty_seats == mock_table_data["leftSeats"]


@pytest.mark.asyncio
async def test_update_tables(mock_db, mock_table_data: IncomingTableDataDict):
    await update_tables([mock_table_data])

    tables = await Table.find_all().to_list()
    assert len(tables) == 1
    table = tables[0]
    assert table.gaming_configuration.game == mock_table_data["gameType"]
    assert table.gaming_configuration.blinds == mock_table_data["blinds"]
    assert table.gaming_configuration.ante == mock_table_data["ante"]
    assert table.gaming_configuration.game_mode == mock_table_data["gameMode"]
    assert table.gaming_configuration.room_mode == mock_table_data["roomMode"]


@pytest.mark.asyncio
async def test_update_tables_with_missing_properties(mock_db, mock_table_data_missing_properties: dict):
    # Attempt to create TableUpdate with missing properties should raise KeyError
    with pytest.raises(KeyError) as exc_info:
        TableUpdate.from_worker_data(mock_table_data_missing_properties)

    assert "KeyError" in str(exc_info.type)
