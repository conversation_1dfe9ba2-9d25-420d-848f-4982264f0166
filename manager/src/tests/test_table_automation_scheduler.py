from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch
from zoneinfo import ZoneInfo

import pytest
import pytest_asyncio

from src.utils.enums import BotType
from src.db.models import GamingConfiguration, Launch, Player, PlayerBalance, Table, TablesAutomationConfig, CurrencyType
from src.tasks.tables_automation import run_tables_automation


now = datetime.now(ZoneInfo("UTC"))
one_hour_ago = now - timedelta(hours=1)


@pytest_asyncio.fixture
async def mock_tables_automation_config_start():
    tables_automation_config_data = [
        TablesAutomationConfig(
            config_id=1,
            app_id=82,
            is_enabled=True,
            start_interval=5,
            start_last_update=one_hour_ago,
            stop_interval=3,
            stop_last_update=one_hour_ago,
            created_at=now,
            big_blinds=[2, 4, 8],
            room_modes=[0, 2],
            ante=12,
            players_count=[4, 7],
            currencies=[CurrencyType.DIAMOND]
        ),
        TablesAutomationConfig(
            config_id=2,
            app_id=81,
            is_enabled=True,
            start_interval=5,
            start_last_update=one_hour_ago,
            stop_interval=3,
            stop_last_update=one_hour_ago,
            created_at=now,
            big_blinds=[140, 280, 350],
            room_modes=[0, 2],
            ante=300,
            players_count=[4, 7],
            currencies=[CurrencyType.GOLD, CurrencyType.USD]
        ),
    ]
    await TablesAutomationConfig.insert_many(tables_automation_config_data)


@pytest_asyncio.fixture
async def mock_tables():
    """Fixture to insert mock Table data."""
    table_data = [
        Table(
            table_id="t1",
            table_name="Table 1",
            app_id=81,
            gaming_configuration=GamingConfiguration(
                game="game1",
                blinds=[1, 2, 4],
                ante=1,
                game_mode=1,
                room_mode=0,
                straddle=True
            ),
            currency="USD",
            players_total=5,
            empty_seats=3,
        ),
        Table(
            table_id="t2",
            table_name="Table 2",
            app_id=81,
            gaming_configuration=GamingConfiguration(
                game="game2",
                blinds=[1, 2, 8],
                ante=12,
                game_mode=1,
                room_mode=0,
                straddle=False
            ),
            currency="GOLD",
            players_total=5,
            empty_seats=3),
        Table(
            table_id="t3",
            table_name="Table 3",
            app_id=82,
            gaming_configuration=GamingConfiguration(
                game="game2",
                blinds=[1, 2],
                ante=12,
                game_mode=1,
                room_mode=0,
                straddle=False
            ),
            currency="DIAMOND",
            players_total=5,
            empty_seats=3),
        Table(
            table_id="t3",
            table_name="Table 3",
            app_id=82,
            gaming_configuration=GamingConfiguration(
                game="game2",
                blinds=[.1, .2],
                ante=1,
                game_mode=1,
                room_mode=0,
                straddle=False
            ),
            currency="DIAMOND",
            players_total=5,
            empty_seats=3),
    ]

    await Table.insert_many(table_data)


@pytest_asyncio.fixture
async def mock_players():
    def get_player(**kwargs):
        return Player(
            app_id=82,
            enabled=True,
            country_code="CN",
            **kwargs,
        )

    player_data = [
        get_player(
            player_id="1",
            status="IDLE",
            balance=PlayerBalance(gold=160000)
        ),
        get_player(
            player_id="2",
            status="PLAYING",
            balance=PlayerBalance(gold=9000),
            hands_played=195,
            bot_id="1",
            bot_type=BotType.PLAY.value,
            table_id="1",
            launch_id="la-1",
        ),
        get_player(
            player_id="3",
            status="PLAYING",
            balance=PlayerBalance(diamond=1000),
            hands_played=3,
            stack=0,
            bot_id="1",
            bot_type=BotType.PLAY.value,
            table_id="1",
            launch_id="la-2",
        ),
        get_player(
            player_id="5",
            status="IDLE",
            balance=PlayerBalance(diamond=1000),
        ),
        get_player(
            player_id="4",
            status="IDLE",
            balance=PlayerBalance(diamond=3)
        )
        ]
    await Player.insert_many(player_data)


@pytest_asyncio.fixture
async def mock_launch():
    """Fixture to insert mock Launch data."""
    launch_data = [
        Launch(
            launch_id="la-1",
            app_id=82,
            player_id="2",
            table_id="1",
            config_id=1,
            created_at=now
        ),
        Launch(
            launch_id="la-2",
            app_id=82,
            player_id="3",
            table_id="1",
            config_id=1,
            created_at=now
        ),
        ]
    await Launch.insert_many(launch_data)


@pytest.mark.asyncio
@patch('src.tasks.tables_automation.BotService')
async def test_run_tables_automation(
    mock_bot_service, mock_db, mock_tables_automation_config_start,
    mock_tables, mock_players, mock_launch
):
    mock_bot_service.start_bot = AsyncMock()
    mock_bot_service.stop_bot = AsyncMock()

    current_config = await TablesAutomationConfig.find_one({"config_id": 1})
    assert current_config is not None

    start_last_update_before = current_config.start_last_update
    stop_last_update_before = current_config.stop_last_update

    await run_tables_automation()

    mock_bot_service.start_bot.assert_called()
    assert mock_bot_service.start_bot.call_count == 1

    mock_bot_service.stop_bot.assert_called()
    assert mock_bot_service.stop_bot.call_count == 1

    config_after = await TablesAutomationConfig.find_one({"config_id": 1})

    assert config_after.start_last_update > start_last_update_before
    assert config_after.stop_last_update > stop_last_update_before


@pytest.mark.asyncio
@patch('src.tasks.tables_automation.BotService')
async def test_run_tables_automation_no_players(
    mock_bot_service, mock_db, mock_tables_automation_config_start,
    mock_tables, mock_launch
):
    mock_bot_service.start_bot = AsyncMock()
    mock_bot_service.stop_bot = AsyncMock()

    await run_tables_automation()

    mock_bot_service.start_bot.assert_not_called()
