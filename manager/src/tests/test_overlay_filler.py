from datetime import datetime, timedelta, time, timezone
from types import SimpleNamespace
from unittest.mock import AsyncMock, MagicMock, patch
from random import random

import pytest
import pytest_asyncio

from src.db.models import (
    AutoStartAction,
    AutoStartConfig,
    MultiflightConfiguration,
    PlayerBalance,
    Tournament,
    TournamentConfiguration,
    PlayTimeRange,
    Player,
    Launch,
    FutureLaunch,
    Ticket,
)
from src.utils.enums import CurrencyType, PlayerStatus
from src.tasks.overlay_filler import (
    _get_player_tool_id_for_tournament,
    calc_players_to_add,
    calc_desired_players_to_add,
    extract_tools_ids,
    fill_overlay,
    get_late_registration_tournaments,
    get_not_started_tournaments,
    is_scheduling,
    level_multiplier,
    process_late_registration_tournament,
    process_tournament,
    _calc_bot_start_delays,
    calc_max_players_to_add,
    find_players_for_tournament,
    get_players_already_played,
)

TournamentAppId = 84


@pytest_asyncio.fixture
async def mock_players():
    def get_player(**kwargs):
        return Player(
            app_id=TournamentAppId,
            enabled=True,
            country_code="CN",
            **kwargs
        )
    # Insert mock player data
    player_data = [
        get_player(
            player_id="1",
            status="PLAYING",
            balance=PlayerBalance(gold=100)
        ),
        get_player(
            player_id="2",
            status="IDLE",
            balance=PlayerBalance(gold=100)
        ),
        get_player(
            player_id="3",
            status="IDLE",
            balance=PlayerBalance(gold=1)
        ),
    ]
    await Player.insert_many(player_data)


@pytest.mark.asyncio
async def test_get_tournaments(mock_db):
    # Insert mock tournament data
    now = datetime.now(timezone.utc)
    tournament_data = [
        {
            "tournament_id": "1",
            "tournament_name": "Tournament 1",
            "tournament_name_eng": "Tournament 1 ENG",
            "starting_time": now - timedelta(minutes=30),
            "late_registration_time": now + timedelta(minutes=30),
            "seats_per_table": 9,
            "registration_fee": 10.0,
            "currency": "USD",
            "app_id": TournamentAppId,
            "date_updated": now,
            "status": 1,
            "mtt_mode": 1,
            "tournament_mode": 1,
            "game_mode": 2,
            "is_satellite_mode": True,
            "sign_up_options": "Option 1",
            "multi_flight_id": 0,
            "multi_flight_level": 0,
            "overlay": 100,
        },
        {
            "tournament_id": "2",
            "tournament_name": "Tournament 2",
            "tournament_name_eng": "Tournament 2 ENG",
            "starting_time": now + timedelta(minutes=30),
            "seats_per_table": 9,
            "registration_fee": 10.0,
            "currency": "USD",
            "app_id": TournamentAppId,
            "date_updated": now,
            "status": 1,
            "mtt_mode": 1,
            "tournament_mode": 1,
            "game_mode": 2,
            "is_satellite_mode": False,
            "sign_up_options": "Option 2",
            "multi_flight_id": 0,
            "multi_flight_level": 0,
            "overlay": 100,
        },
        {
            "tournament_id": "3",
            "tournament_name": "Tournament 3",
            "tournament_name_eng": "Tournament 3 ENG",
            "starting_time": now + timedelta(hours=2),
            "seats_per_table": 9,
            "registration_fee": 10.0,
            "currency": "USD",
            "app_id": TournamentAppId,
            "date_updated": now,
            "status": 1,
            "mtt_mode": 1,
            "tournament_mode": 1,
            "game_mode": 2,
            "is_satellite_mode": False,
            "sign_up_options": "Option 3",
            "multi_flight_id": 0,
            "multi_flight_level": 0,
            "overlay": 100,
        }
    ]
    await Tournament.insert_many([Tournament(**data) for data in tournament_data])

    config = AutoStartConfig(app_id=TournamentAppId, check_before_start_min=60)
    # Call the function to test
    tournaments = await get_not_started_tournaments(config)

    # Verify the results
    assert len(tournaments) == 1
    assert tournaments[0].tournament_id == "2"

    tournaments = await get_late_registration_tournaments(config)
    assert len(tournaments) == 1
    assert tournaments[0].tournament_id == "1"


@pytest.mark.asyncio
async def test_is_scheduling(mock_db):
    # Insert mock data
    now = datetime.now(timezone.utc)
    schedule_history = [
        AutoStartAction(
            app_id=TournamentAppId,
            tournament_id="1",
            number_of_players=1,
            started=now,
            finished=now + timedelta(minutes=30),
        ),
        AutoStartAction(
            app_id=TournamentAppId,
            tournament_id="2",
            number_of_players=1,
            started=now - timedelta(minutes=30),
            finished=now - timedelta(minutes=30),
        ),
    ]
    await AutoStartAction.insert_many(schedule_history)

    # Call the function to test
    assert await is_scheduling(app_id=TournamentAppId, tournament_id="1", check_interval_sec=60)

    assert not await is_scheduling(app_id=TournamentAppId, tournament_id="2", check_interval_sec=60)


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.BotService")
async def test_fill_overlay(mock_bot_service, mock_db, mock_players):
    # Mock the BotService start_bot method
    mock_bot_service.start_bot = AsyncMock()

    tournament = Tournament(
        tournament_id="1",
        tournament_name="Tournament 1",
        starting_time=datetime.now(timezone.utc),
        seats_per_table=9,
        registration_fee=10.0,
        currency="USD",
        app_id=TournamentAppId,
    )
    await fill_overlay(
        tournament,
        num_players=2,
        min_delay=1,
        max_delay=2,
        delay_limit=10,
    )

    # Verify the results
    mock_bot_service.start_bot.assert_called()
    assert mock_bot_service.start_bot.call_count == 1

    history = await AutoStartAction.find_one(AutoStartAction.tournament_id == "1")
    assert history is not None
    assert history.number_of_players == 1


@pytest.mark.asyncio
@patch('src.tasks.overlay_filler.BotService')
async def test_process_tournament(mock_bot_service, mock_db, mock_players):
    mock_bot_service.start_bot = AsyncMock()
    # Insert mock tournament data
    now = datetime.now(timezone.utc)
    tournament = await Tournament(
        tournament_id="1",
        tournament_name="Tournament 1",
        starting_time=now - timedelta(minutes=30),
        late_registration_time=now + timedelta(minutes=30),
        seats_per_table=9,
        registration_fee=10.0,
        currency="USD",
        app_id=TournamentAppId,
        game_pool=1000,
        overlay=100,
    ).save()

    await TournamentConfiguration.insert(TournamentConfiguration(
        tournament_id="1", app_id=TournamentAppId, game_pool=500.0))

    # Call the function to test
    config = AutoStartConfig(app_id=TournamentAppId, check_before_start_min=60, cover_before_start=0.5,
                             cover_late_registration=0.5, check_interval_sec=60)

    await process_tournament(tournament, config, 1)

    # Verify the results
    mock_bot_service.start_bot.assert_called()
    assert mock_bot_service.start_bot.call_count == 1

    history = await AutoStartAction.find_one(AutoStartAction.tournament_id == "1")
    assert history is not None
    assert history.number_of_players == 1


@pytest.mark.parametrize("desired_coverage, covered, game_pool, fee, min_to_add, expected", [
    (1, 0.5, 1000, 10, 1, 50),
    (1, 0.5, 1000, 100, 1, 5),
    (1, 0.99, 1000, 100, 1, 1),
])
def test_calc_desired_players_to_add(desired_coverage, covered, game_pool, fee, min_to_add, expected):
    assert calc_desired_players_to_add(desired_coverage, covered, game_pool, fee, min_to_add) == expected


@pytest.mark.parametrize("value, multiplier, expected", [
    (0.03, 3, 0.33), (0.07, 3, 0.33), (0.15, 3, 0.67), (0.22, 3, 0.67),
    (0.3, 3, 1), (0.5, 3, 0.67), (0.78, 3, 0.67), (0.99, 3, 1),
    (0.03, 4, 0.25), (0.07, 4, 0.5), (0.15, 4, 0.75), (0.22, 4, 1),
    (0.3, 4, 0.25), (0.5, 4, 0.25), (0.78, 4, 0.25), (0.99, 4, 1)
])
def test_level_multiplier(value, multiplier, expected):
    assert round(level_multiplier(value, multiplier), 2) == expected


@pytest.mark.asyncio
@patch('src.tasks.overlay_filler.calc_desired_players_to_add')
@patch('src.tasks.overlay_filler.calc_max_players_to_add')
async def test_calc_players_to_add(mock_calc_max_players_to_add, mock_calc_desired_players_to_add):
    mock_calc_desired_players_to_add.return_value = 50
    mock_calc_max_players_to_add.return_value = 20

    tournament = Tournament(
        tournament_id="t2",
        tournament_name="Tournament 2",
        starting_time=datetime.now(timezone.utc) + timedelta(minutes=30),
        seats_per_table=9,
        registration_fee=random(),
        currency="USD",
        app_id=1,
    )
    await tournament.save()
    config = AutoStartConfig(app_id=1, schedule_min_players=1)

    assert await calc_players_to_add(
        tournament, config, 100500, covered=random(), calculated_game_pool=random()
    ) == 20

    mock_calc_max_players_to_add.return_value = 200
    assert await calc_players_to_add(
        tournament, config, 100500, covered=random(), calculated_game_pool=random()
    ) == 50

    mock_calc_max_players_to_add.return_value = 10
    await Launch(
        table_id='t2',
        app_id=1,
        launch_id='whatever',
        player_id='whatever',
        config_id=999
    ).save()
    assert await calc_players_to_add(
        tournament, config, 100500, covered=random(), calculated_game_pool=random()
    ) == 9


@pytest.mark.parametrize(
    "players,interval,bot_min_delay,bot_max_delay,expected_min_delay,expected_max_delay",
    [
        (5, 300, 30, 90, 30,  90),
        (10, 300, 30, 90, 30,  90),
        (2, 300, 30, 90, 100, 300),
        (3, 300, 30, 90, 50, 150),
        (4, 300, 30, 90, 33, 100),
    ],
)
def test_calc_delays(players, interval, bot_min_delay, bot_max_delay, expected_min_delay, expected_max_delay):
    config = SimpleNamespace(app_id=1, check_interval_sec=interval, bot_min_delay_sec=bot_min_delay, bot_max_delay_sec=bot_max_delay)
    min_delay, max_delay = _calc_bot_start_delays(players, None, config)
    assert min_delay == expected_min_delay
    assert max_delay == expected_max_delay


def test_calc_delays_specific(mock_db):
    config = SimpleNamespace(
        app_id=1,
        check_interval_sec=30,
        bot_min_delay_sec=30,
        bot_max_delay_sec=60
    )
    tournament_config = TournamentConfiguration(
        app_id=1,
        tournament_id='1',
        game_pool=1,
        scheduling_min_delay_sec=10,
        scheduling_max_delay_sec=20
    )
    players_count = 9
    min_delay, max_delay = _calc_bot_start_delays(players_count, tournament_config, config)
    assert min_delay == 10
    assert max_delay == 20


@pytest.mark.parametrize(
    "players,interval,config_min_delay,config_max_delay,override_min_delay,override_max_delay,expected_min_delay,expected_max_delay",
    [
        # No tournament override -> use AutoStart config delays
        (1, 300, 30, 90, None, None, 30, 90),
        # Override only min
        (1, 300, 30, 90, 45, None, 45, 90),
        # Override only max
        (1, 300, 30, 90, None, 120, 30, 120),
        # Override both
        (1, 300, 30, 90, 40, 100, 40, 100),
    ],
)
def test_calc_delays_with_tournament_config_edge_cases(
    players, interval, config_min_delay, config_max_delay, override_min_delay, override_max_delay, expected_min_delay, expected_max_delay
):
    # preserve AutoStart config values
    config = SimpleNamespace(
        app_id=1,
        check_interval_sec=interval,
        bot_min_delay_sec=config_min_delay,
        bot_max_delay_sec=config_max_delay,
    )
    # override none or some scheduling values via tournament_config
    tournament_config = (
        SimpleNamespace(scheduling_min_delay_sec=override_min_delay, scheduling_max_delay_sec=override_max_delay)
        if (override_min_delay is not None or override_max_delay is not None)
        else None
    )

    min_delay, max_delay = _calc_bot_start_delays(players, tournament_config, config)
    assert min_delay == expected_min_delay
    assert max_delay == expected_max_delay


@pytest.mark.asyncio
@patch('src.tasks.overlay_filler.BotService')
@patch('src.tasks.overlay_filler.datetime')
async def test_fill_overlay_time_range(mock_datetime, mock_bot_service, mock_db):
    await Player(
        player_id="p1",
        app_id=81,
        status="IDLE",
        enabled=True,
        balance=PlayerBalance(gold=100),
        play_time_range=PlayTimeRange(
            start=time(15),
            end=time(23)
        )
    ).save()

    mock_bot_service.start_bot = AsyncMock()
    mock_datetime.now.return_value = datetime.now(timezone.utc).replace(hour=12)

    tournament = Tournament(
        tournament_id="t1",
        tournament_name="Tournament T1",
        starting_time=datetime.now(timezone.utc),
        seats_per_table=9,
        registration_fee=10.0,
        currency="USD",
        app_id=81,
    )
    await fill_overlay(
        tournament,
        num_players=2,
        min_delay=1,
        max_delay=2,
        delay_limit=10,
    )

    mock_bot_service.start_bot.assert_not_called()

    mock_datetime.now.return_value = datetime.now(timezone.utc).replace(hour=16)

    await fill_overlay(
        tournament,
        num_players=2,
        min_delay=1,
        max_delay=2,
        delay_limit=10,
    )
    assert mock_bot_service.start_bot.await_count == 1


@pytest.mark.asyncio
@patch('src.tasks.overlay_filler.BotService')
@patch('src.tasks.overlay_filler.datetime')
async def test_fill_overlay_time_range_over_midnight(mock_datetime, mock_bot_service, mock_db):
    await Player(
        player_id="p1",
        app_id=81,
        status="IDLE",
        enabled=True,
        balance=PlayerBalance(gold=100),
        play_time_range=PlayTimeRange(
            start=time(18),
            end=time(6)
        )
    ).save()

    mock_bot_service.start_bot = AsyncMock()
    mock_datetime.now.return_value = datetime.today().replace(hour=8)

    tournament = Tournament(
        tournament_id="t1",
        tournament_name="Tournament T1",
        starting_time=datetime.now(timezone.utc),
        seats_per_table=9,
        registration_fee=10.0,
        currency="USD",
        app_id=81,
    )
    await fill_overlay(
        tournament,
        num_players=2,
        min_delay=1,
        max_delay=2,
        delay_limit=10,
    )

    mock_bot_service.start_bot.assert_not_called()

    mock_datetime.now.return_value = datetime.today().replace(hour=20)

    await fill_overlay(
        tournament,
        num_players=2,
        min_delay=1,
        max_delay=2,
        delay_limit=10,
    )
    assert mock_bot_service.start_bot.call_count == 1


@patch('src.tasks.overlay_filler.feature_flags.get_flag')
def test_max_players(mock_get_flag):
    mock_get_flag.side_effect = lambda flag, default: default  # always return the default value

    assert calc_max_players_to_add(100, 1, CurrencyType.GOLD) == 30

    assert calc_max_players_to_add(10_000, 1, CurrencyType.GOLD) == 3000
    assert calc_max_players_to_add(80_000, 1, CurrencyType.GOLD) == 28000

    assert calc_max_players_to_add(9_999, 1, CurrencyType.USD) == 2499
    assert calc_max_players_to_add(10_000, 1, CurrencyType.USD) == 3500
    assert calc_max_players_to_add(10_001, 1, CurrencyType.USD) == 3500

    assert calc_max_players_to_add(10_000, 100, CurrencyType.GOLD) == 30
    assert calc_max_players_to_add(80_000, 100, CurrencyType.GOLD) == 280
    assert calc_max_players_to_add(100_000, 1000, CurrencyType.GOLD) == 30

    assert calc_max_players_to_add(10_000, 100, CurrencyType.USD) == 35
    assert calc_max_players_to_add(9_999, 100, CurrencyType.USD) == 24

    # For 10kUSD tournaments threshold is 35% if reg fee <= 100$, else 30%
    assert calc_max_players_to_add(10_000, 100, CurrencyType.USD) == int(0.35 * 10_000 / 100)
    assert calc_max_players_to_add(10_000, 200, CurrencyType.USD) == int(0.25 * 10_000 / 200)
    assert calc_max_players_to_add(80_000, 800, CurrencyType.GOLD) == int(0.35 * 80_000 / 800)
    assert calc_max_players_to_add(80_000, 1600, CurrencyType.GOLD) == int(0.3 * 80_000 / 1600)


@pytest.mark.asyncio
async def test_find_players_for_tournament(mock_db):
    def get_player(player_id, play_time_range=[15, 23], status=PlayerStatus.IDLE.value, **kwargs):
        return Player(
            player_id=player_id,
            status=status,
            app_id=1,
            enabled=True,
            country_code="CN",
            play_time_range=PlayTimeRange(start=time(play_time_range[0]), end=time(play_time_range[1])),
            **kwargs
        )
    await Player.insert_many([
        get_player("p1", play_time_range=[15, 23], balance=PlayerBalance(gold=100)),
        get_player("p2", play_time_range=[10, 23], balance=PlayerBalance(gold=1)),
        get_player("p3", play_time_range=[15, 23], balance=PlayerBalance(usd=100)),
    ])

    play_time = datetime.now(timezone.utc).replace(hour=16)

    tournament_gold = Tournament(
        tournament_id="t1",
        tournament_name="Tournament T1",
        starting_time=play_time,
        seats_per_table=9,
        registration_fee=10.0,
        currency=CurrencyType.GOLD,
        app_id=1,
    )
    # find gold player
    players = await find_players_for_tournament(
        tournament_gold,
        start_date=play_time,
    )
    assert len(players) == 2
    assert {p.player_id for p in players} == {"p1", "p3"}

    # p1 is in tournament
    await Launch(table_id="t1", app_id=1, launch_id="l1", player_id="p1", config_id=999).save()
    players = await find_players_for_tournament(
        tournament_gold,
        start_date=play_time,
    )
    assert {p.player_id for p in players} == {"p3"}

    # find by play time range
    tournament_gold_low_fee = Tournament(
        tournament_id="t1",
        tournament_name="Tournament T1",
        starting_time=play_time.replace(hour=12),
        seats_per_table=9,
        registration_fee=1.0,
        currency=CurrencyType.GOLD,
        app_id=1,
    )
    players = await find_players_for_tournament(
        tournament_gold_low_fee,
        start_date=play_time.replace(hour=12),
    )
    assert len(players) == 1
    assert players[0].player_id == "p2"

    # filter by existing future launch
    await FutureLaunch(tournament_id="t2", app_id=1, player_id="p2", starting_time=play_time + timedelta(minutes=10)).save()
    players = await find_players_for_tournament(
        tournament_gold_low_fee,
        start_date=play_time.replace(hour=12),
    )
    assert len(players) == 0

    # find by USD
    tournament_usd = Tournament(
        tournament_id="t1",
        tournament_name="Tournament T1",
        starting_time=play_time,
        seats_per_table=9,
        registration_fee=20.0,
        currency=CurrencyType.USD,
        app_id=1,
    )
    players = await find_players_for_tournament(
        tournament_usd,
        start_date=play_time,
    )
    assert len(players) == 1
    assert players[0].player_id == "p3"

    # filter by final tournament
    await MultiflightConfiguration(app_id=1, multi_flight_id=69, day_one_tournament_ids=["t1"], day_two_tournament_id="t3").save()
    await FutureLaunch(tournament_id="t3", app_id=1, player_id="p3", starting_time=datetime.now(timezone.utc) + timedelta(days=100500)).save()
    players = await find_players_for_tournament(
        tournament_usd,
        start_date=play_time,
    )
    assert len(players) == 0

    # filter by status not idle
    p3 = await Player.find_one(Player.player_id == "p3")
    p3.status = PlayerStatus.PLAYING.value
    await p3.save()
    players = await find_players_for_tournament(
        tournament_usd,
        start_date=play_time,
    )
    assert len(players) == 0


@pytest.mark.asyncio
async def test_find_players_for_tournament_with_tickets(mock_db):
    """Test that players with matching tickets are prioritized over balance-based players."""

    def get_player(player_id, play_time_range=[15, 23], status=PlayerStatus.IDLE.value, **kwargs):
        return Player(
            player_id=player_id,
            status=status,
            app_id=1,
            enabled=True,
            country_code="CN",
            play_time_range=PlayTimeRange(
                start=time(play_time_range[0]), end=time(play_time_range[1])
            ),
            **kwargs,
        )

    await Player.insert_many(
        [
            get_player(
                "p1",
                balance=PlayerBalance(
                    gold=100, tickets=[Ticket.model_construct(ticket_id=1754, tool_id=1754)]
                ),
            ),  # Has matching ticket
            get_player("p2", balance=PlayerBalance(gold=100)),  # Has balance only
            get_player(
                "p3",
                balance=PlayerBalance(
                    gold=100, tickets=[Ticket.model_construct(ticket_id=9999, tool_id=9999)]
                ),
            ),  # Has non-matching ticket
            get_player(
                "p4",
                balance=PlayerBalance(
                    gold=1, tickets=[Ticket.model_construct(ticket_id=1754, tool_id=1754)]
                ),
            ),  # Has matching ticket but insufficient balance
        ]
    )

    play_time = datetime.now(timezone.utc).replace(hour=16)

    tournament_with_tickets = Tournament(
        tournament_id="t1",
        tournament_name="Tournament T1",
        starting_time=play_time,
        seats_per_table=9,
        registration_fee=10.0,
        currency=CurrencyType.GOLD,
        app_id=1,
        sign_up_options="gold,tool,specific:mtt:a92:1754",  # Contains ticket ID 1754
    )

    players = await find_players_for_tournament(
        tournament_with_tickets,
        start_date=play_time,
        allowed_tools_ids=[1754],
    )

    # Should find players with tickets first, then players with balance
    # p1 and p4 have matching tickets, p2 has balance, p3 has non-matching ticket but balance
    assert len(players) == 4
    player_ids = [p.player_id for p in players]

    # Players with matching tickets should come first
    assert "p1" in player_ids[:2]  # p1 has matching ticket
    assert "p4" in player_ids[:2]  # p4 has matching ticket (even with insufficient balance)

    # Players with balance should come after
    assert "p2" in player_ids[2:]  # p2 has balance only
    assert "p3" in player_ids[2:]  # p3 has balance but non-matching ticket


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.feature_flags.get_flag")
async def test_extract_ticket_ids(mock_get_flag):
    """Test extracting ticket IDs from signUpOptions."""
    # Mock the feature flag to be enabled
    mock_get_flag.return_value = True

    assert extract_tools_ids("gold,tool,specific:mtt:a92:1754,a92:1593") == [
        1754,
        1593,
    ]

    assert extract_tools_ids("specific:mtt:a92:1754") == [1754]
    assert extract_tools_ids("a92:1593") == [1593]
    assert extract_tools_ids("1234") == [1234]

    assert extract_tools_ids("gold,specific:mtt:x99:5678,tool,b12:9999,1234") == [
        5678,
        9999,
        1234,
    ]

    assert extract_tools_ids("") is None
    assert extract_tools_ids(None) is None

    # Test when feature flag is disabled
    mock_get_flag.return_value = False
    assert extract_tools_ids("gold,tool,specific:mtt:a92:1754,a92:1593") is None

    # Re-enable for remaining tests
    mock_get_flag.return_value = True
    assert extract_tools_ids("gold,tool,other") is None  # No valid ticket IDs found
    assert extract_tools_ids("123,12345") == [123, 12345]  # Wrong number of digits


@pytest.mark.asyncio
async def test_find_matching_ticket_id():
    """Test finding matching ticket ID from player's tickets."""
    from src.services.players import PlayerService

    player_tickets = [
        Ticket(ticket_id=1754, tool_id=1754),
        Ticket(ticket_id=9999, tool_id=9999),
        Ticket(ticket_id=1234, tool_id=1234),
    ]

    # Test valid matches with list of allowed ticket IDs
    assert PlayerService.find_matching_ticket_id(player_tickets, [1754, 5555]) == 1754
    assert PlayerService.find_matching_ticket_id(player_tickets, [9999]) == 9999
    assert (
        PlayerService.find_matching_ticket_id(player_tickets, [1234, 1754, 9999]) == 1754
    )  # Returns first match

    # Test no matches
    assert PlayerService.find_matching_ticket_id(player_tickets, [1111, 2222]) is None
    assert PlayerService.find_matching_ticket_id([], [1754]) is None
    assert PlayerService.find_matching_ticket_id(None, [1754]) is None
    assert PlayerService.find_matching_ticket_id(player_tickets, []) is None
    assert PlayerService.find_matching_ticket_id(player_tickets, None) is None


@pytest.mark.asyncio
async def test_find_players_for_tournament_prioritizes_reentry(mock_db):
    """
    Players who have can_reenter_tournament=True in Launch should be prioritized in find_players_for_tournament.
    """
    # Setup: 2 players, both eligible, but one has can_reenter_tournament=True
    play_time = datetime.now(timezone.utc) - timedelta(minutes=10)
    t = await Tournament(
        tournament_id="t1",
        tournament_name="Tournament T1",
        starting_time=play_time,
        seats_per_table=9,
        registration_fee=10.0,
        currency=CurrencyType.GOLD,
        app_id=1,
    ).save()
    await TournamentConfiguration(tournament_id="t1", app_id=1, game_pool=100, max_reentry_count=2).save()
    await Player.insert_many([
        Player(player_id="p1", app_id=1, status=PlayerStatus.IDLE.value, enabled=True, balance=PlayerBalance(gold=100)),
        Player(player_id="p2", app_id=1, status=PlayerStatus.IDLE.value, enabled=True, balance=PlayerBalance(gold=100)),
    ])
    # p2 has a Launch with can_reenter_tournament=True
    await Launch(
        table_id="t1",
        app_id=1,
        launch_id="l1",
        player_id="p2",
        can_reenter_tournament=True
    ).save()
    # Both players are eligible, but p2 should be prioritized
    players = await find_players_for_tournament(t, play_time)
    assert len(players) == 2
    # p2 should come before p1
    assert players[0].player_id == "p2"


@pytest.mark.asyncio
@patch('src.tasks.overlay_filler.process_tournament')
async def test_process_late_registration_tournament_not_started_yet(mock_process_tournament, mock_db):
    tournament = Tournament(
        tournament_id="t1",
        tournament_name="Tournament T1",
        starting_time=datetime.now(timezone.utc) + timedelta(minutes=5),
        late_registration_time=datetime.now(timezone.utc) + timedelta(minutes=10),
        seats_per_table=9,
        registration_fee=10.0,
        currency=CurrencyType.GOLD,
        app_id=1,
    )
    await tournament.save()

    config = AutoStartConfig(
        app_id=1,
        check_before_start_min=1,
        cover_before_start=0.0,
        cover_late_registration=0.95,
        check_interval_sec=60
    )

    await process_late_registration_tournament(tournament, config)


@pytest.mark.asyncio
async def test_get_players_already_played_no_config(mock_db):
    """Should return all player_ids if no TournamentConfiguration exists."""
    t = await Tournament(
        tournament_id="t1", app_id=1, tournament_name="T1", starting_time=datetime.now(timezone.utc),
        seats_per_table=9, registration_fee=10.0, currency="USD"
    ).save()
    await Launch(table_id="t1", app_id=1, launch_id="l1", player_id="p1").save()
    await Launch(table_id="t1", app_id=1, launch_id="l2", player_id="p2").save()
    res = await get_players_already_played(t)
    assert set(res) == {"p1", "p2"}


@pytest.mark.asyncio
async def test_get_players_already_played_max_reentry_0(mock_db):
    """Should return all player_ids if max_reentry_count=0."""
    t = await Tournament(
        tournament_id="t2", app_id=1, tournament_name="T2", starting_time=datetime.now(timezone.utc),
        seats_per_table=9, registration_fee=10.0, currency="USD"
    ).save()
    await TournamentConfiguration(tournament_id="t2", app_id=1, game_pool=100, max_reentry_count=0).save()
    await Launch(table_id="t2", app_id=1, launch_id="l2", player_id="p1").save()
    res = await get_players_already_played(t)
    assert res == ["p1"]


@pytest.mark.asyncio
async def test_get_players_already_played_max_reentry_not_enough(mock_db):
    """Should return empty if no player reached max_reentry_count > 0."""
    t = await Tournament(
        tournament_id="t3", app_id=1, tournament_name="T3", starting_time=datetime.now(timezone.utc),
        seats_per_table=9, registration_fee=10.0, currency="USD"
    ).save()
    await TournamentConfiguration(tournament_id="t3", app_id=1, game_pool=100, max_reentry_count=2).save()
    await Launch(table_id="t3", app_id=1, launch_id="l1", player_id="p1").save()
    await Launch(table_id="t3", app_id=1, launch_id="l2", player_id="p1").save()
    # p1 has only 2 launches, needs > 2
    res = await get_players_already_played(t)
    assert res == []


@pytest.mark.asyncio
async def test_get_players_already_played_max_reentry_enough(mock_db):
    """Should return player_id if player has > max_reentry_count launches."""
    t = await Tournament(
        tournament_id="t4", app_id=1, tournament_name="T4", starting_time=datetime.now(timezone.utc),
        seats_per_table=9, registration_fee=10.0, currency="USD"
    ).save()
    await TournamentConfiguration(tournament_id="t4", app_id=1, game_pool=100, max_reentry_count=2).save()

    await Launch.insert_many([
        Launch(table_id="t4", app_id=1, launch_id="l1", player_id="p1"),
        Launch(table_id="t4", app_id=1, launch_id="l2", player_id="p1"),
        Launch(table_id="t4", app_id=1, launch_id="l3", player_id="p1"),
    ])
    # p1 has 3 launches, max_reentry_count=2, so p1 should be returned
    res = await get_players_already_played(t)
    assert res == ["p1"]


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.logger")
@patch("src.tasks.overlay_filler.extract_tools_ids")
@patch("src.services.players.PlayerService.find_matching_ticket_id")
async def test_get_player_ticket_for_tournament_calls_find_matching_ticket_id_when_conditions_met(
    mock_find_matching_ticket_id, mock_extract_tools_ids, mock_logger
):
    """Test that PlayerService.find_matching_ticket_id is called when all conditions are met."""
    # Arrange
    allowed_ticket_ids = [1754, 1593]
    mock_extract_tools_ids.return_value = allowed_ticket_ids
    mock_find_matching_ticket_id.return_value = 1754

    player_tickets = [1754, 9999]
    # Create a mock player object with the required attributes
    player = MagicMock()
    player.player_id = "test_player"
    player.balance = MagicMock()
    player.balance.tickets = player_tickets

    tournament_id = "test_tournament"

    # Act
    result = _get_player_tool_id_for_tournament(tournament_id, player, allowed_ticket_ids)

    # Assert
    mock_extract_tools_ids.assert_not_called()
    mock_find_matching_ticket_id.assert_called_once_with(player_tickets, allowed_ticket_ids)
    assert result == "1754"


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.extract_tools_ids")
@patch("src.services.players.PlayerService.find_matching_ticket_id")
async def test_get_player_ticket_for_tournament_no_call_when_sign_up_options_none(mock_find_matching_ticket_id, mock_extract_tools_ids):
    """Test that PlayerService.find_matching_ticket_id is NOT called when sign_up_options is None."""
    # Arrange
    # Create a mock player object with the required attributes
    player = MagicMock()
    player.player_id = "test_player"
    player.balance = MagicMock()
    player.balance.tickets = [1754, 9999]

    tournament_id = "test_tournament"

    # Act
    result = _get_player_tool_id_for_tournament(tournament_id, player, None)

    # Assert
    mock_extract_tools_ids.assert_not_called()
    mock_find_matching_ticket_id.assert_not_called()
    assert result is None


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.extract_tools_ids")
@patch("src.services.players.PlayerService.find_matching_ticket_id")
async def test_get_player_ticket_for_tournament_no_call_when_sign_up_options_empty(mock_find_matching_ticket_id, mock_extract_tools_ids):
    """Test that PlayerService.find_matching_ticket_id is NOT called when allowed_tools_ids is empty."""
    # Arrange
    # Create a mock player object with the required attributes
    player = MagicMock()
    player.player_id = "test_player"
    player.balance = MagicMock()
    player.balance.tickets = [1754, 9999]

    tournament_id = "test_tournament"

    # Act
    result = _get_player_tool_id_for_tournament(tournament_id, player, [])

    # Assert
    mock_extract_tools_ids.assert_not_called()
    mock_find_matching_ticket_id.assert_not_called()
    assert result is None


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.extract_tools_ids")
@patch("src.services.players.PlayerService.find_matching_ticket_id")
async def test_get_player_ticket_for_tournament_no_call_when_player_balance_none(mock_find_matching_ticket_id, mock_extract_tools_ids):
    """Test that PlayerService.find_matching_ticket_id is NOT called when player.balance is None."""
    # Arrange
    # Create a mock player object with balance set to None
    player = MagicMock()
    player.player_id = "test_player"
    player.balance = None

    tournament_id = "test_tournament"

    # Act
    result = _get_player_tool_id_for_tournament(tournament_id, player, [1754, 1593])

    # Assert
    mock_extract_tools_ids.assert_not_called()
    mock_find_matching_ticket_id.assert_not_called()
    assert result is None


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.extract_tools_ids")
@patch("src.services.players.PlayerService.find_matching_ticket_id")
async def test_get_player_ticket_for_tournament_no_call_when_player_tickets_none(mock_find_matching_ticket_id, mock_extract_tools_ids):
    """Test that PlayerService.find_matching_ticket_id is NOT called when player.balance.tickets is None."""
    # Arrange
    # Create a mock player object with tickets set to None
    player = MagicMock()
    player.player_id = "test_player"
    player.balance = MagicMock()
    player.balance.tickets = None

    tournament_id = "test_tournament"

    # Act
    result = _get_player_tool_id_for_tournament(tournament_id, player, [1754, 1593])

    # Assert
    mock_extract_tools_ids.assert_not_called()
    mock_find_matching_ticket_id.assert_not_called()
    assert result is None


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.extract_tools_ids")
@patch("src.services.players.PlayerService.find_matching_ticket_id")
async def test_get_player_ticket_for_tournament_no_call_when_allowed_ticket_ids_none(mock_find_matching_ticket_id, mock_extract_tools_ids):
    """Test that PlayerService.find_matching_ticket_id is NOT called when extract_tools_ids returns None."""
    # Arrange
    mock_extract_tools_ids.return_value = None  # extract_tools_ids returns None

    # Create a mock player object with the required attributes
    player = MagicMock()
    player.player_id = "test_player"
    player.balance = MagicMock()
    player.balance.tickets = [1754, 9999]

    tournament_id = "test_tournament"

    # Act
    result = _get_player_tool_id_for_tournament(tournament_id, player, None)

    # Assert
    mock_extract_tools_ids.assert_not_called()
    mock_find_matching_ticket_id.assert_not_called()
    assert result is None


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.extract_tools_ids")
@patch("src.services.players.PlayerService.find_matching_ticket_id")
async def test_get_player_ticket_for_tournament_no_call_when_allowed_ticket_ids_empty(mock_find_matching_ticket_id, mock_extract_tools_ids):
    """Test that PlayerService.find_matching_ticket_id is NOT called when extract_tools_ids returns empty list."""
    # Arrange
    mock_extract_tools_ids.return_value = []  # extract_tools_ids returns empty list

    # Create a mock player object with the required attributes
    player = MagicMock()
    player.player_id = "test_player"
    player.balance = MagicMock()
    player.balance.tickets = [1754, 9999]

    tournament_id = "test_tournament"

    # Act
    result = _get_player_tool_id_for_tournament(tournament_id, player, [])

    # Assert
    mock_extract_tools_ids.assert_not_called()
    mock_find_matching_ticket_id.assert_not_called()
    assert result is None


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.extract_tools_ids")
@patch("src.services.players.PlayerService.find_matching_ticket_id")
async def test_get_player_ticket_for_tournament_returns_none_when_no_matching_ticket(mock_find_matching_ticket_id, mock_extract_tools_ids):
    """Test that function returns None when PlayerService.find_matching_ticket_id returns None."""
    # Arrange
    allowed_ticket_ids = [1754, 1593]
    mock_extract_tools_ids.return_value = allowed_ticket_ids
    mock_find_matching_ticket_id.return_value = None  # No matching ticket found

    player_tickets = [9999, 8888]  # No matching tickets
    # Create a mock player object with the required attributes
    player = MagicMock()
    player.player_id = "test_player"
    player.balance = MagicMock()
    player.balance.tickets = player_tickets

    tournament_id = "test_tournament"

    # Act
    result = _get_player_tool_id_for_tournament(tournament_id, player, allowed_ticket_ids)

    # Assert
    mock_extract_tools_ids.assert_not_called()
    mock_find_matching_ticket_id.assert_called_once_with(player_tickets, allowed_ticket_ids)
    assert result is None


@pytest.mark.asyncio
@patch("src.tasks.overlay_filler.extract_tools_ids")
@patch("src.services.players.PlayerService.find_matching_ticket_id")
async def test_get_player_ticket_for_tournament_no_call_when_player_tickets_empty(mock_find_matching_ticket_id, mock_extract_tools_ids):
    """Test that PlayerService.find_matching_ticket_id is NOT called when player.balance.tickets is empty list."""
    # Arrange
    player_tickets = []  # Empty tickets list
    # Create a mock player object with the required attributes
    player = MagicMock()
    player.player_id = "test_player"
    player.balance = MagicMock()
    player.balance.tickets = player_tickets

    tournament_id = "test_tournament"

    # Act
    result = _get_player_tool_id_for_tournament(tournament_id, player, [1754, 1593])

    # Assert
    # Function should return early due to empty tickets list, so neither function should be called
    mock_extract_tools_ids.assert_not_called()
    mock_find_matching_ticket_id.assert_not_called()
    assert result is None
