from datetime import datetime, time
from zoneinfo import ZoneInfo

import pytest
from fastapi.testclient import TestClient

from src.db.models import Player
from src.routers.players import update_player_info
from src.schemas.requests import PlayerUpdateRequest

from src.main import app

client = TestClient(app)


@pytest.mark.asyncio
async def test_get_tournaments(mock_db):
    # Mock the data
    tournament_data = [
        {
            "tournament_id": "1",
            "tournament_name": "Tournament 1",
            "tournament_name_eng": "Tournament 1",
            "starting_time": datetime.now(ZoneInfo("UTC")),
            "seats_per_table": 8,
            "registration_fee": 10.0,
            "currency": "USD",
            "game_pool": 42.0,
            "app_id": 83,
            "status": 1,
            "mtt_mode": 1,
            "tournament_mode": 1,
            "game_mode": 2,
            "sign_up_options": "Option 1",
            "multi_flight_id": 0,
            "multi_flight_level": 0,
            "date_updated": datetime.now(ZoneInfo("UTC")),
        }
    ]
    await mock_db["tournaments"].insert_many(tournament_data)

    # Test for fetching tournaments
    response = client.get("/tournaments?app_id=83")
    body = response.json()
    assert response.status_code == 200
    assert len(body["data"]) == 1
    assert body["data"][0]["tournament_name"] == "Tournament 1"


@pytest.mark.asyncio
async def test_update_play_time(mock_db):
    await Player(
        player_id="p2",
        app_id=1,
        enabled=True,
        status='whatever',
    ).save()

    update_data = {
        'play_time_range': {
            'start': '12:45',
            'end': '04:20'
        }
    }
    await update_player_info('p2', PlayerUpdateRequest(**update_data))

    player = await Player.find(Player.player_id == "p2").first_or_none()

    assert player.play_time_range.end == time(4, 20)
