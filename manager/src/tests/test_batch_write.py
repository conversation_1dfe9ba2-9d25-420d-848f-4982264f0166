import pytest

from datetime import datetime, time, timedelta, UTC

from src.db.models import FutureLaunch, FutureTournamentCheck, Tournament, Player, PlayTimeRange
from src.services.bots import BotService
from src.utils.enums import PlayerStatus


@pytest.mark.asyncio
async def test_create_future_launches(mock_db):
    now = datetime.now(UTC)
    await Tournament.insert_many([
        Tournament(
            tournament_id="t1",
            tournament_name="Tournament 1",
            starting_time=now + timedelta(minutes=30),
            seats_per_table=9,
            registration_fee=10.0,
            currency="USD",
            app_id=1,
        ),
        Tournament(
            tournament_id="t2",
            tournament_name="Tournament 2",
            starting_time=now + timedelta(minutes=30),
            seats_per_table=9,
            registration_fee=10.0,
            currency="USD",
            app_id=1,
        ),
    ])
    await Player.insert_many([
        Player(
            player_id="p1",
            app_id=1,
            enabled=True,
            status=PlayerStatus.IDLE.value,
            play_time_range=PlayTimeRange(start=time.min, end=time.max)
        ),
        Player(
            player_id="p2",
            app_id=1,
            enabled=True,
            status=PlayerStatus.IDLE.value,
            play_time_range=PlayTimeRange(start=time.min, end=time.max)
        ),
    ])

    future_launches = [
        FutureLaunch(tournament_id="t1", player_id="p1", app_id=1, starting_time=None),
        FutureLaunch(tournament_id="t2", player_id="p2", app_id=1, starting_time=None),
        FutureLaunch(tournament_id="t1", player_id="p1", app_id=1, starting_time=None),
    ]
    await BotService.create_future_launches(future_launches)
    launches = await FutureLaunch.all().to_list()
    assert len(launches) == 2


@pytest.mark.asyncio
async def test_create_future_tournament_checks(mock_db):
    future_tournament_checks = [
        FutureTournamentCheck(tournament_id="t1", player_id="p1", app_id=1),
        FutureTournamentCheck(tournament_id="t2", player_id="p2", app_id=1)
    ]
    await BotService.create_future_tournament_checks(future_tournament_checks)
    checks = await FutureTournamentCheck.all().to_list()
    assert len(checks) == 2
