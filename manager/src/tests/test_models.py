from datetime import time
from src.db.models import Player, Play<PERSON><PERSON><PERSON><PERSON><PERSON>


def test_play_time_range():
    player = Player(
        player_id='1',
        app_id=69,
        enabled=True,
        status='whatever',
    )
    play_time_range = PlayTimeRange(
        start=time(13, 0, 0),
        end=time(22, 59, 0)
    )
    player.play_time_range = play_time_range

    assert not player.is_in_play_time_range(time(0, 0, 0))
    assert not player.is_in_play_time_range(time(12, 0, 0))
    assert not player.is_in_play_time_range(time(13, 0, 0))
    assert player.is_in_play_time_range(time(13, 0, 1))
    assert player.is_in_play_time_range(time(22, 58, 59))
    assert not player.is_in_play_time_range(time(22, 59, 0))
    assert not player.is_in_play_time_range(time(22, 59, 1))

    play_time_range_over_midnight = PlayTimeRange(
        start=time(22, 0, 0),
        end=time(3, 0, 0)
    )
    player.play_time_range = play_time_range_over_midnight

    assert player.is_in_play_time_range(time(0, 0, 0))
    assert player.is_in_play_time_range(time(2, 59, 0))
    assert not player.is_in_play_time_range(time(3, 0, 0))
    assert not player.is_in_play_time_range(time(3, 0, 1))
    assert not player.is_in_play_time_range(time(12, 0, 0))
    assert not player.is_in_play_time_range(time(21, 59, 0))
    assert not player.is_in_play_time_range(time(22, 0, 0))
    assert player.is_in_play_time_range(time(22, 0, 1))
    assert player.is_in_play_time_range(time(23, 0, 0))
