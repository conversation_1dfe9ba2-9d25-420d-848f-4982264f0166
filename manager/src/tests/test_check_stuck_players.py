import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import patch
from src.tasks.check_stuck_players import check_stuck_players
from src.db.models import Player
from src.utils.enums import PlayerStatus

old_time = datetime.now(timezone.utc) - timedelta(days=1)


class DummyJob:
    def __init__(self, player_id):
        self.data = {"playerId": player_id}


@pytest.mark.asyncio
@patch("src.tasks.check_stuck_players.mq_manager.get_jobs")
async def test_no_players(mock_get_jobs, mock_db):
    mock_get_jobs.return_value = []
    await check_stuck_players()
    # No assertion needed, just ensure no error


@pytest.mark.asyncio
@patch("src.tasks.check_stuck_players.mq_manager.get_jobs")
async def test_no_stuck_players(mock_get_jobs, mock_db):
    await Player(player_id="1", app_id=1, enabled=True, status=PlayerStatus.PLAYING.value).save()
    mock_get_jobs.return_value = [DummyJob("1")]

    await check_stuck_players()

    updated = await Player.find_one(Player.player_id == "1")
    assert updated.status == PlayerStatus.PLAYING.value


@pytest.mark.asyncio
@patch("src.tasks.check_stuck_players.mq_manager.get_jobs")
async def test_stuck_player_reset(mock_get_jobs, mock_db):
    await Player(player_id="2", app_id=1, enabled=True, status=PlayerStatus.PLAYING.value, updated_at=old_time).save()
    mock_get_jobs.return_value = [DummyJob("1")]

    await check_stuck_players()

    updated = await Player.find_one(Player.player_id == "2")
    assert updated.status == PlayerStatus.IDLE.value


@pytest.mark.asyncio
@patch("src.tasks.check_stuck_players.mq_manager.get_jobs")
async def test_multiple_stuck_players(mock_get_jobs, mock_db):
    await Player(player_id="2", app_id=1, enabled=True, status=PlayerStatus.PLAYING.value, updated_at=old_time).save()
    await Player(player_id="3", app_id=1, enabled=True, status=PlayerStatus.SCANNING.value, bot_id="something", updated_at=old_time).save()
    mock_get_jobs.return_value = [DummyJob("1")]

    await check_stuck_players()

    updated1 = await Player.find_one(Player.player_id == "2")
    updated2 = await Player.find_one(Player.player_id == "3")
    assert updated1.status == PlayerStatus.IDLE.value
    assert updated2.status == PlayerStatus.IDLE.value


@pytest.mark.asyncio
@patch("src.tasks.check_stuck_players.mq_manager.get_jobs")
async def test_player_in_active_jobs(mock_get_jobs, mock_db):
    await Player(player_id="5", app_id=1, enabled=True, status=PlayerStatus.PLAYING.value, updated_at=old_time).save()
    mock_get_jobs.return_value = [DummyJob("5")]

    await check_stuck_players()

    updated = await Player.find_one(Player.player_id == "5")
    assert updated.status == PlayerStatus.PLAYING.value


@pytest.mark.asyncio
@patch("src.tasks.check_stuck_players.mq_manager.get_jobs")
async def test_updated_at_changes_on_reset(mock_get_jobs, mock_db):
    old_time = datetime.now(timezone.utc) - timedelta(days=1)
    await Player(
        player_id="10", app_id=1, enabled=True, status=PlayerStatus.PLAYING.value,
        updated_at=old_time
    ).save()
    mock_get_jobs.return_value = [DummyJob("1")]

    await check_stuck_players()

    updated = await Player.find_one(Player.player_id == "10")
    assert updated.status == PlayerStatus.IDLE.value
    assert updated.updated_at.astimezone(timezone.utc) > old_time


@pytest.mark.asyncio
@patch("src.tasks.check_stuck_players.mq_manager.get_jobs")
async def test_does_not_update_if_updated_at_too_soon(mock_get_jobs, mock_db):
    just_now = datetime.now(timezone.utc)
    await Player(
        player_id="20", app_id=1, enabled=True, status=PlayerStatus.PLAYING.value,
        updated_at=just_now
    ).save()
    mock_get_jobs.return_value = [DummyJob("1")]

    await check_stuck_players()

    updated = await Player.find_one(Player.player_id == "20")
    assert updated.status == PlayerStatus.PLAYING.value
