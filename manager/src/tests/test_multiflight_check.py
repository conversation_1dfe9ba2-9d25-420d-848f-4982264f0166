from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

import pytest

from src.db.models import AutoStartConfig, MultiflightConfiguration, Tournament, TournamentConfiguration
from src.services.multi_flight_configuration import MultiflightConfigurationService
from src.services.tournaments import TournamentService
from src.tasks.multiflight_check import (
    DAY_ONE_ADJUSTED_GAME_POOL,
    get_all_multiflight_tournaments_day_one,
    get_multiflight_tournaments,
    run_preprocess_multiflight_tournaments,
)
from src.tests.test_overlay_filler import TournamentAppId


@pytest.mark.asyncio
async def test_update_adjusted_game_pool_successfully(mock_db):
    now = datetime.now(ZoneInfo("UTC"))
    test_multiflight_id1 = 123
    tournament_id = "8899"
    tournament_data = [{
            "tournament_id": tournament_id,
            "tournament_name": "Tournament 4",
            "tournament_name_eng": "MF1 Not Started",
            "starting_time": now + timedelta(minutes=30),
            "seats_per_table": 9,
            "registration_fee": 10.0,
            "currency": "USD",
            "app_id": TournamentAppId,
            "date_updated": now,
            "status": 1,
            "mtt_mode": 1,
            "tournament_mode": 1,
            "game_mode": 2,
            "is_satellite_mode": False,
            "sign_up_options": "Option 4",
            "multi_flight_id": test_multiflight_id1,
            "multi_flight_level": 0,
            "game_pool": 1000.0,
            "overlay": 100,
        }]
    await Tournament.insert_many([Tournament(**data) for data in tournament_data])
    tournament = await Tournament.find_one()
    adjusted_game_pool, adjusted_manually = await TournamentService.get_adjusted_game_pool(tournament)
    assert adjusted_game_pool == 0
    assert not adjusted_manually

    await TournamentService.update_tournament_configurations(
        app_id=TournamentAppId, tournament_ids=[tournament_id], adjusted_game_pool=576.23, adjusted_game_pool_updated_manually=True)
    new_game_pool, adjusted_manually = await TournamentService.get_adjusted_game_pool(tournament)
    assert new_game_pool == 576.23
    assert adjusted_manually


@pytest.mark.asyncio
async def test_create_mf_configuration(mock_db):
    await MultiflightConfigurationService.upsert(MultiflightConfiguration(
        app_id=81, multi_flight_id=123, day_one_tournament_ids=['1', '2', '3'], day_two_tournament_id='256'
    ))

    search = await MultiflightConfiguration.find(MultiflightConfiguration.multi_flight_id == 123).to_list()
    assert len(search) == 1
    assert set(search[0].day_one_tournament_ids) == {'1', '2', '3'}
    assert search[0].day_two_tournament_id == '256'

    # Updating again without day_two_tournament_id
    await MultiflightConfigurationService.upsert(MultiflightConfiguration(
        app_id=81, multi_flight_id=123, day_one_tournament_ids=['3', '4', '5']
    ))
    search = await MultiflightConfiguration.find(MultiflightConfiguration.multi_flight_id == 123).to_list()
    assert len(search) == 1
    assert set(search[0].day_one_tournament_ids) == {'1', '2', '3', '4', '5'}
    # Has not been rewritten
    assert search[0].day_two_tournament_id == '256'


@pytest.mark.asyncio
async def test_multi_flight(mock_db):
    # Preparation - Insert mock tournament data
    now = datetime.now(ZoneInfo("UTC"))
    test_multiflight_id1 = 123
    test_multiflight_id2 = 456
    tournament_data = [
        {
            "tournament_id": "4",
            "tournament_name": "Tournament 4",
            "tournament_name_eng": "MF1 Not Started",
            "starting_time": now + timedelta(minutes=30),
            "seats_per_table": 9,
            "registration_fee": 10.0,
            "currency": "USD",
            "app_id": TournamentAppId,
            "date_updated": now,
            "status": 1,
            "mtt_mode": 1,
            "tournament_mode": 1,
            "game_mode": 2,
            "is_satellite_mode": False,
            "sign_up_options": "Option 4",
            "multi_flight_id": test_multiflight_id1,
            "multi_flight_level": 0,
            "game_pool": 3000,
            "overlay": 100,
        },
        {
            "tournament_id": "5",
            "tournament_name": "Tournament 5",
            "tournament_name_eng": "MF1 Late Reg",
            "starting_time": now - timedelta(minutes=30),
            "late_registration_time": now + timedelta(minutes=30),
            "seats_per_table": 9,
            "registration_fee": 10.0,
            "currency": "USD",
            "app_id": TournamentAppId,
            "date_updated": now,
            "status": 1,
            "mtt_mode": 1,
            "tournament_mode": 1,
            "game_mode": 2,
            "is_satellite_mode": True,
            "sign_up_options": "Option 5",
            "multi_flight_id": test_multiflight_id1,
            "multi_flight_level": 0,
            "game_pool": 3000,
            "overlay": 100,
        },
        {
            "tournament_id": "6",
            "tournament_name": "Tournament 6",
            "tournament_name_eng": "MF2 Not Started",
            "starting_time": now + timedelta(minutes=30),
            "seats_per_table": 9,
            "registration_fee": 10.0,
            "currency": "USD",
            "app_id": TournamentAppId,
            "date_updated": now,
            "status": 1,
            "mtt_mode": 1,
            "tournament_mode": 1,
            "game_mode": 2,
            "is_satellite_mode": True,
            "sign_up_options": "Option 6",
            "multi_flight_id": test_multiflight_id2,
            "multi_flight_level": 0,
            "game_pool": 6000,
            "overlay": 100,
        },
        {
            "tournament_id": "7",
            "tournament_name": "Tournament 7",
            "tournament_name_eng": "MF2 Late Reg",
            "starting_time": now - timedelta(minutes=30),
            "late_registration_time": now + timedelta(minutes=30),
            "seats_per_table": 9,
            "registration_fee": 10.0,
            "currency": "USD",
            "app_id": TournamentAppId,
            "date_updated": now,
            "status": 1,
            "mtt_mode": 1,
            "tournament_mode": 1,
            "game_mode": 2,
            "is_satellite_mode": True,
            "sign_up_options": "Option 7",
            "multi_flight_id": test_multiflight_id2,
            "multi_flight_level": 0,
            "game_pool": 6000,
            "overlay": 100,
        },
    ]
    await Tournament.insert_many([Tournament(**data) for data in tournament_data])
    # prepare Config
    config = AutoStartConfig(app_id=TournamentAppId, check_before_start_min=60)

    app_id = config.app_id

    mf_id1_tournaments = await get_multiflight_tournaments(app_id=app_id, multi_flight_id=test_multiflight_id1)
    assert len(mf_id1_tournaments) == 2
    assert set([t.tournament_id for t in mf_id1_tournaments]) == {"4", "5"}

    mf_id2_tournaments2 = await get_multiflight_tournaments(app_id=app_id, multi_flight_id=test_multiflight_id2)
    assert len(mf_id2_tournaments2) == 2
    assert set([t.tournament_id for t in mf_id2_tournaments2]) == {"6", "7"}

    await run_preprocess_multiflight_tournaments(config)

    mf1_tournaments = await Tournament.find(Tournament.multi_flight_id == test_multiflight_id1).to_list()
    for tournament in mf1_tournaments:
        assert tournament.multi_flight_id == test_multiflight_id1
        game_pool, updated_manually = await TournamentService.get_adjusted_game_pool(tournament)
        assert game_pool == DAY_ONE_ADJUSTED_GAME_POOL
        assert not updated_manually

    conf_mf1 = await MultiflightConfiguration.find_one(MultiflightConfiguration.multi_flight_id == test_multiflight_id1)
    assert conf_mf1 is not None
    assert set(conf_mf1.day_one_tournament_ids) == {"4", "5"}

    mf2_tournaments = await Tournament.find(Tournament.multi_flight_id == test_multiflight_id2).to_list()
    for tournament in mf2_tournaments:
        assert tournament.multi_flight_id == test_multiflight_id2
        game_pool, updated_manually = await TournamentService.get_adjusted_game_pool(tournament)
        assert game_pool == DAY_ONE_ADJUSTED_GAME_POOL
        assert not updated_manually

    conf_mf2 = await MultiflightConfiguration.find_one(MultiflightConfiguration.multi_flight_id == test_multiflight_id2)
    assert conf_mf2 is not None
    assert set(conf_mf2.day_one_tournament_ids) == {"6", "7"}

    # Adding one more tournament for
    tournament_data = [{
        # Day1
        "tournament_id": "8",
        "tournament_name": "Tournament 8",
        "tournament_name_eng": "tournament_name_eng",
        "starting_time": now - timedelta(minutes=30),
        "late_registration_time": now + timedelta(minutes=30),
        "seats_per_table": 9,
        "registration_fee": 10.0,
        "currency": "USD",
        "app_id": TournamentAppId,
        "date_updated": now,
        "status": 1,
        "mtt_mode": 1,
        "tournament_mode": 1,
        "game_mode": 2,
        "is_satellite_mode": False,
        "sign_up_options": "Option 8",
        "multi_flight_id": test_multiflight_id2,
        "multi_flight_level": 0,
        "game_pool": 9000,
        "overlay": 100,
    },
        {
        # Day2
        "tournament_id": "9",
        "tournament_name": "Tournament 9",
        "tournament_name_eng": "tournament_name_eng",
        "starting_time": now - timedelta(minutes=30),
        "late_registration_time": now + timedelta(minutes=30),
        "seats_per_table": 9,
        "registration_fee": 10.0,
        "currency": "USD",
        "app_id": TournamentAppId,
        "date_updated": now,
        "status": 1,
        "mtt_mode": 1,
        "tournament_mode": 1,
        "game_mode": 2,
        "is_satellite_mode": False,
        "sign_up_options": "Option 8",
        "multi_flight_id": test_multiflight_id2,
        "multi_flight_level": 1,
        "game_pool": 9000,
        "overlay": 100,
    }]
    await Tournament.insert_many([Tournament(**data) for data in tournament_data])

    # Processing again
    await run_preprocess_multiflight_tournaments(config)

    # Checking configuration
    conf_mf2 = await MultiflightConfiguration.find_one(MultiflightConfiguration.multi_flight_id == test_multiflight_id2)
    assert conf_mf2 is not None
    # All elements present
    assert set(conf_mf2.day_one_tournament_ids) == {"6", "7", "8"}
    assert conf_mf2.day_two_tournament_id == "9"

    # Checking adjusted game_pool
    tournaments = await Tournament.find(Tournament.multi_flight_id == test_multiflight_id2).to_list()
    adjusted_game_pool, updated_manually = await TournamentService.get_adjusted_game_pool(tournaments[0])
    assert adjusted_game_pool == DAY_ONE_ADJUSTED_GAME_POOL
    assert not updated_manually

    # Updating
    await TournamentService.update_tournament_configurations(
        app_id=app_id,
        tournament_ids=["8"],
        adjusted_game_pool=4500,
        adjusted_game_pool_updated_manually=True
    )
    # Checking
    tournament_data = await TournamentService.get_tournament_by_id(app_id=app_id, tournament_id="8")
    assert tournament_data.tournament_id == "8"
    assert tournament_data.adjusted_game_pool == 4500
    assert tournament_data.adjusted_game_pool_updated_manually


@pytest.mark.asyncio
async def test_get_all_multiflight_tournaments_day_one(mock_db):
    await TournamentConfiguration.insert_many([
        TournamentConfiguration(
            app_id=83,
            tournament_id='69',
            game_pool=1000,
            game_pool_updated_manually=False
        )
    ])

    await Tournament.insert_many([
        Tournament(
            app_id=83,
            tournament_id='69',
            tournament_name='Test Tournament',
            multi_flight_id=42,
            multi_flight_level=0,
            game_mode=2,
            is_satellite_mode=False,
            registration_fee=10.0,
            status=1,
            starting_time=datetime.now(),
            seats_per_table=9,
            currency='USD',
        )
    ])

    result = await get_all_multiflight_tournaments_day_one(83, 42)

    assert len(result) == 1
    assert result[0].tournament_id == 69
    assert result[0].adjusted_game_pool == 1000
