from datetime import datetime, timedelta, timezone
import pytest
import pytest_asyncio
from src.tasks.scanning import find_player
from src.db.models import Player, FutureLaunch
from src.utils.enums import PlayerStatus


@pytest_asyncio.fixture
def app_id():
    return 123


@pytest.mark.asyncio
async def test_find_player_returns_player(mock_db, app_id):
    # Insert a player that is eligible
    player = Player(
        player_id="p1",
        app_id=app_id,
        status=PlayerStatus.IDLE.value,
        enabled=True
    )
    await player.insert()
    # No FutureLaunch for this player
    result = await find_player(app_id)
    assert result is not None
    assert result.player_id == "p1"
    assert result.app_id == app_id
    assert result.status == PlayerStatus.IDLE.value
    assert result.enabled is True


@pytest.mark.asyncio
async def test_find_player_returns_none_when_no_players(mock_db, app_id):
    # No players inserted
    result = await find_player(app_id)
    assert result is None


@pytest.mark.asyncio
async def test_find_player_returns_none_when_no_idle_players(mock_db, app_id):
    player = Player(
        player_id="p1",
        app_id=app_id,
        status=PlayerStatus.PLAYING.value,
        enabled=True
    )
    await player.insert()
    # No idle players
    result = await find_player(app_id)
    assert result is None


@pytest.mark.asyncio
async def test_find_player_excludes_booked_players(mock_db, app_id):
    # Insert two players
    player1 = Player(
        player_id="p1",
        app_id=app_id,
        status=PlayerStatus.IDLE.value,
        enabled=True
    )
    player2 = Player(
        player_id="p2",
        app_id=app_id,
        status=PlayerStatus.IDLE.value,
        enabled=True
    )
    await player1.insert()
    await player2.insert()
    # Book player1 in FutureLaunch
    future_launch = FutureLaunch(
        player_id="p1",
        tournament_id="t1",
        app_id=app_id,
        starting_time=datetime.now(timezone.utc) + timedelta(minutes=5)
    )
    await future_launch.insert()
    # Only player2 should be returned
    found = set()
    # Run multiple times to check random sampling
    for _ in range(5):
        result = await find_player(app_id)
        assert result is not None
        assert result.player_id == "p2"
        found.add(result.player_id)
    assert found == {"p2"}
