import setup  # noqa: F401
import pytest_asyncio
from beanie import Document, init_beanie
from mongomock_motor import AsyncMongoMockClient
from src.db.models import __dict__ as model_dict

document_models = [
    obj for obj in model_dict.values()
    if isinstance(obj, type) and issubclass(obj, Document) and obj is not Document
]


@pytest_asyncio.fixture
async def mock_db():
    client = AsyncMongoMockClient()
    db = client.test_db
    await init_beanie(database=db, document_models=document_models)
    yield db
    client.close()
