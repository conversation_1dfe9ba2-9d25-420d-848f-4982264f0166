from typing import List

from src.routers.base_router import BaseRouter
from src.schemas.requests import IPPoolAssignRequest, IPPoolConfigurationCreateRequest
from src.schemas.responses import IPPoolConfigurationResponse, Response
from src.services.ip_pool_service import IPPoolConfigurationService

ip_pool_router = BaseRouter(prefix="/ip_pool", tags=["ip_pool_configurations"])


@ip_pool_router.get("/")
async def list_all_ip_pool_configs() -> Response[List[IPPoolConfigurationResponse]]:
    all_configs = await IPPoolConfigurationService.get_all()
    return [ip_conf.to_response() for ip_conf in all_configs]


@ip_pool_router.post("/")
async def create_ip_pool_config(request: IPPoolConfigurationCreateRequest) -> Response[IPPoolConfigurationResponse]:
    new_config = await IPPoolConfigurationService.create(request)
    return new_config.to_response()


@ip_pool_router.patch("/{ip_conf_id}")
async def update_ip_pool_config(ip_conf_id: str, request: IPPoolConfigurationCreateRequest) -> Response[IPPoolConfigurationResponse]:
    updated_config = await IPPoolConfigurationService.update(ip_conf_id, request)
    return updated_config.to_response()


@ip_pool_router.post("/{ip_conf_id}/assign",
                     summary="Assign player to an existing IP Pool configuration")
async def assign_config(ip_conf_id: str, request: IPPoolAssignRequest) -> Response[IPPoolConfigurationResponse]:
    assigned_config = await IPPoolConfigurationService.assign_player_to_ip(request.player_id, ip_conf_id)
    return assigned_config.to_response()


@ip_pool_router.post("/{ip_conf_id}/unassign",
                     summary="Unassign player from an existing IP Pool configuration")
async def unassign_config(ip_conf_id: str, request: IPPoolAssignRequest) -> Response[IPPoolConfigurationResponse]:
    unassigned_config = await IPPoolConfigurationService.unassign_player_from_ip(request.player_id, ip_conf_id)
    return unassigned_config.to_response()


@ip_pool_router.delete("/{ip_conf_id}", status_code=204)
async def delete_ip_pool_config(ip_conf_id: str):
    await IPPoolConfigurationService.delete(ip_conf_id)
