from typing import Annotated, List

from fastapi import Depends, Query, Response as FastApiResponse

from src.db.models import FutureLaunch
from src.routers.base_router import BaseRouter
from src.schemas.requests import (
    AddFutureLaunchRequest,
    AddMultipleUserAccountsRequest,
    PlayerUpdateRequest,
    RegisterPlayerRequest,
    TransferBalanceRequest,
    ChangeAvatarRequest,
)
from src.schemas.responses import (
    AddUserAccountResponse,
    FutureLaunchResponse,
    LaunchResponse,
    PlayerResponse,
    PlayerResponseExtended,
    PlayerStatusResponse,
    RegisterPlayerResponse,
    Response,
    PendingRegistrationResponse,
    PlayerResponseFiltered
)
from src.schemas.query_params import PageQueryParams, PlayerFilterQueryParams
from src.services.bots import BotService
from src.services.players import PlayerService
from src.services.player_registration_service import PlayerRegistrationService
from src.services.task_launches import TaskLaunchService

player_router = BaseRouter(prefix="/players", tags=["players"])


@player_router.get("/", summary="List players")
async def list_players(
    app_id: int | None = None,
    club_id: int | None = None,
    only_active: Annotated[bool, Query(description="Return only active accounts (which have associated bot)")] = False,
    only_enabled: Annotated[bool, Query(description="Return only enabled accounts")] = True,
) -> Response[List[PlayerResponseExtended]]:
    players = await PlayerService.get_players(app_id, club_id, only_active, only_enabled)
    return players


@player_router.get("/search", summary="List filtered players")
async def list_filtered_players(
    page: PageQueryParams = Depends(),
    query: PlayerFilterQueryParams = Depends(),
) -> PlayerResponseFiltered:
    response = await PlayerService.get_filtered_players(
        page=page.page,
        page_size=page.page_size,
        sort=query.sort,
        query=query.get_query(),
    )
    return response


@player_router.get("/states", summary="List player states")
async def list_player_statuses(
    app_id: int | None = None,
    only_active: Annotated[bool, Query(description="Return only active accounts (which have associated bot)")] = False,
    only_enabled: Annotated[bool, Query(description="Return only enabled accounts")] = True,
) -> Response[List[PlayerStatusResponse]]:

    players = await PlayerService.get_players(app_id, only_active, only_enabled)
    return [player.to_status_response() for player in players]


@player_router.post("/register", status_code=201)
async def register_players(
    request: List[RegisterPlayerRequest],
    players_reg_service: Annotated[PlayerRegistrationService, Depends()],
    response: FastApiResponse
) -> Response[List[RegisterPlayerResponse]]:

    players = await players_reg_service.register_players(request)
    for player in players:
        if player.error is not None:
            response.status_code = 500
    return players


@player_router.get(
    "/pending_registrations", summary="List pending registrations. Can be filtered by app_id"
)
async def list_pending_registrations(
    players_reg_service: Annotated[PlayerRegistrationService, Depends()], app_id: int | None = None
) -> Response[List[PendingRegistrationResponse]]:
    return await players_reg_service.list_pending_registrations(app_id)


@player_router.post(
    "/pending_registrations",
    status_code=201,
    summary="Add pending registrations",
)
async def add_pending_registrations(
    request: List[RegisterPlayerRequest],
    players_reg_service: Annotated[PlayerRegistrationService, Depends()],
) -> Response[List[PendingRegistrationResponse]]:
    return await players_reg_service.add_pending_registrations(request)


@player_router.delete(
    "/pending_registrations",
    status_code=204,
    summary="Delete pending registrations by IDs, app_id or all of them",
)
async def delete_pending_registrations(
    players_reg_service: Annotated[PlayerRegistrationService, Depends()],
    pending_registration_ids: List[str] | None = None,
    app_id: int | None = None,
):
    await players_reg_service.delete_pending_registrations(pending_registration_ids, app_id)
    return None


@player_router.patch("/{player_id}")
async def update_player_info(player_id: str, player_data: PlayerUpdateRequest) -> Response[PlayerResponse]:
    player = await PlayerService.update_player(player_id, player_data)
    return player.to_response()


@player_router.post("/{player_id}/refresh_balance", summary="Refresh player balance")
async def mark_player_for_balance_update(player_id: str) -> Response[PlayerResponse]:
    player = await PlayerService.mark_player_for_balance_update(player_id)
    return player.to_response()


@player_router.post("/refresh_balance", summary="Refresh player balance")
async def mark_players_for_balance_update(query: PlayerFilterQueryParams) -> None:
    await PlayerService.mark_players_for_balance_update(query.get_query())


@player_router.post("/user_accounts/", status_code=201,
                    description="Add already registered user accounts to the worker database")
async def add_user_accounts(request: AddMultipleUserAccountsRequest) -> Response[List[AddUserAccountResponse]]:
    user_accounts = await PlayerService.add_user_accounts(request)
    return user_accounts


@player_router.get("/future_launches",
                   description="Get future launches a special entities for reserved bots")
async def get_future_launches(app_id: int) -> Response[List[FutureLaunchResponse]]:
    future_launches = await FutureLaunch.find(FutureLaunch.app_id == app_id).to_list()
    return [launch.to_response() for launch in future_launches]


@player_router.post("/future_launches", status_code=201)
async def add_future_launch(request: AddFutureLaunchRequest) -> Response[FutureLaunchResponse]:
    response = await BotService.add_future_launch(request)
    return response.to_response()


@player_router.get("/{player_id}/launches", summary="List player launches")
async def list_player_launches(
    player_id: str,
    finished: bool | None = Query(None, description="Filter by finished status"),
) -> Response[List[LaunchResponse]]:
    launches = await BotService.get_launches(player_id, finished)
    return launches


@player_router.post("/transfer", summary="Transfer balance to receiver")
async def transfer_balance(request: TransferBalanceRequest):
    await TaskLaunchService.add_transfer_task_launches(request)


@player_router.post("/{player_id}/change_avatar", summary="Change player avatar")
async def change_avatar(player_id: str, data: ChangeAvatarRequest) -> Response[PlayerResponse]:
    player = await PlayerRegistrationService().change_avatar(player_id=player_id, avatarBase64=data.avatarBase64, imgExt=data.imgExt)
    return Response(code=200, message="Avatar changed successfully", data=player.to_response())
