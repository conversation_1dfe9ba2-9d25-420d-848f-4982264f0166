from typing import List

import pymongo

from src.db.models import AutoStartAction, AutoStartConfig
from src.routers.base_router import BaseRouter
from src.schemas.requests import AutoStartConfigRequest, TablesAutomationConfigRequest, RegistrationAutomationConfigRequest
from src.schemas.responses import (
    AutoStartActionResponse, AutoStartConfigResponse, Response,
    TablesAutomationConfigResponse,
    RegistrationAutomationConfigResponse,
)
from src.services.automation import AutomationService
from src.services.registration_configs import RegistrationAutomationService


automation_router = BaseRouter("/automation", tags=["automation"])


@automation_router.get("/actions/")
async def get_autostart_actions(app_id: int, page: int = 0, page_size: int = 50) -> Response[List[AutoStartActionResponse]]:
    actions = (
        await AutoStartAction.find(AutoStartAction.app_id == app_id)
        .sort([(AutoStartAction.started, pymongo.DESCENDING)])
        .skip(page * page_size)
        .limit(page_size)
        .to_list()
    )
    return [action.to_response() for action in actions]


@automation_router.post("/tournament_config/")
async def create_or_update_autostart_config(request: AutoStartConfigRequest):
    await AutomationService.upsert_autostart_config(AutoStartConfig(**request.model_dump(exclude_none=True)))


@automation_router.get("/tournament_config/")
async def get_autostart_config(app_id: int) -> Response[AutoStartConfigResponse]:
    config = await AutomationService.get_or_create_autostart_config(app_id)
    return config


@automation_router.get("/table_configs/", summary="Get tables(R1&R2) automation config")
async def get_tables_automation_config(app_id: int, club_id: int = None, config_id: int = None) -> Response[List[TablesAutomationConfigResponse]]:
    configs = await AutomationService.get_tables_automation_config(app_id, club_id, config_id)
    return [TablesAutomationConfigResponse(**c.model_dump()) for c in configs]


@automation_router.post("/table_configs/", summary="Set tables(R1&R2) automation config")
async def set_tables_automation_config(request: TablesAutomationConfigRequest) -> Response[TablesAutomationConfigResponse]:
    config = await AutomationService.set_tables_automation_config(request)
    return TablesAutomationConfigResponse(**config.model_dump())


@automation_router.delete("/table_configs/{app_id}/{config_id}/", status_code=204)
async def delete_tables_automation_config(app_id: int, config_id: int):
    await AutomationService.delete_tables_automation_config(app_id=app_id, config_id=config_id)


@automation_router.get("/registration_config/", summary="Get registration automation config")
async def get_registration_automation_config() -> Response[RegistrationAutomationConfigResponse]:
    return await RegistrationAutomationService.get_registration_automation_config()


@automation_router.patch("/registration_config/", summary="Update registration automation config")
async def update_registration_automation_config(
    request: RegistrationAutomationConfigRequest,
) -> Response[RegistrationAutomationConfigResponse]:
    return await RegistrationAutomationService.update_registration_automation_config(request)
