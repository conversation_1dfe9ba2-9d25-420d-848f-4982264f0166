from typing import Callable, List
from fastapi import APIRouter
from fastapi.responses import ORJSONResponse
from functools import wraps
from pydantic import BaseModel

from src.schemas.responses import Response


def response_to_json(func, status_code: int):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        response = await func(*args, **kwargs)
        if isinstance(response, Response):
            return ORJSONResponse(content=response.model_dump(), status_code=response.code)

        if (
            isinstance(response, BaseModel)
            or (isinstance(response, list) and all(isinstance(item, BaseModel) for item in response))
        ):
            return ORJSONResponse(content=Response(data=response, code=status_code, message="OK").model_dump(), status_code=status_code)

        return response
    return wrapper


class BaseRouter(APIRouter):
    def __init__(self, prefix: str = None, tags: List[str] = None):
        super().__init__(prefix=prefix or "", tags=tags or [])

    def add_api_route(self, path: str, endpoint: Callable, **kwargs):
        # Automatically wrap the endpoint with the response_to_json decorator
        wrapped_endpoint = response_to_json(endpoint, status_code=kwargs.get('status_code') or 200)
        super().add_api_route(path, wrapped_endpoint, **kwargs)
