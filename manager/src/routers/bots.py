from typing import List

from src.routers.base_router import BaseRouter
from src.schemas.requests import BotStartRequest
from src.schemas.responses import BotResponse, ErrorResponse, Response
from src.services.bots import BotService


bot_router = BaseRouter("/bots", tags=["bots"])


@bot_router.get("/")
async def list_all_bots() -> Response[List[BotResponse]]:
    return await BotService.get_bots()


@bot_router.post("/start", summary="Start bots on specific tables")
async def start_bots(request_data: BotStartRequest) -> Response[List[BotResponse | ErrorResponse]]:
    return await BotService.start_bots(request_data)


@bot_router.post("/stop")
async def stop_bots_by_ids(bot_ids: List[str]):
    for bot_id in bot_ids:
        await BotService.stop_bot(bot_id, "User requested stop")
