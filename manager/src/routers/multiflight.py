from typing import List

from src.routers.base_router import BaseRouter
from src.schemas.requests import TournamentCheckStartRequest, TournamentCheckBatchRequest
from src.schemas.responses import Response, TournamentCheckResponse
from src.services.tournament_checks import TournamentCheckService

multiflight_router = BaseRouter("/multiflight", tags=["multiflight"])


@multiflight_router.get("/tournament_checks/")
async def get_tournament_checks(app_id: int, multiflight_id: int) -> Response[List[TournamentCheckResponse]]:
    tournaments = await TournamentCheckService.get_tournament_checks(app_id, multiflight_id)
    return tournaments


@multiflight_router.patch("/tournament_checks/")
async def update_tournament_check(request: TournamentCheckStartRequest) -> Response[TournamentCheckResponse]:
    """Start tournament check by changing 'checked' field to False"""
    tournament_response = await TournamentCheckService.update_tournament_check(request)
    return tournament_response


@multiflight_router.post("/tournament_checks", status_code=201)
async def add_tournament_checks(request: TournamentCheckBatchRequest) -> Response[List[TournamentCheckResponse]]:
    return await TournamentCheckService.add_tournament_checks(request.checks)
