from src.routers.base_router import BaseRouter
from src.schemas.requests import RefreshTokenRequest, SignInRequest
from src.services.authentication import AuthService


auth_router = BaseRouter("/auth", tags=["auth"])


@auth_router.post("/signin")
async def signin(data: SignInRequest):
    return await AuthService().signin(data)


@auth_router.post("/refresh")
async def refresh_token(data: RefreshTokenRequest):
    return await AuthService().refresh_token(data)
