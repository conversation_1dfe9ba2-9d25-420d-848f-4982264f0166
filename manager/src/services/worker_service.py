from src.utils.requests import get
from src.utils.settings import settings
from src.utils.logging import logger


class WorkerService:
    logger_name = "worker_service"

    async def get_user_id_from_worker(self, player_id: str) -> str:
        try:
            response = await get(f"{settings.worker_url}/user_id", {"player_id": player_id})
            return str(response.get("userId"))
        except Exception as e:
            logger.error(
                "PlayerService.get_user_id_from_worker",
                f"Failed to get user id from the worker at {settings.worker_url}",
                str(e),
            )
            return None
