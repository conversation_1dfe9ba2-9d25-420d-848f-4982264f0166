from beanie.operators import In

from src.db.models import TaskLaunch, Player
from src.schemas.requests import TransferBalanceRequest
from src.utils.enums import BotType, PlatformType, CurrencyType
from src.utils.logging import logger


class TaskLaunchService:
    @staticmethod
    async def add_transfer_task_launches(transfer_request: TransferBalanceRequest) -> None:
        # check if player_ids already exist in TaskLaunch
        existing_service_launches = await TaskLaunch.find(
            In(TaskLaunch.player_id, transfer_request.player_ids)
        ).to_list()
        existing_player_ids = [launch.player_id for launch in existing_service_launches]

        # filter out player_ids that already exist in TaskLaunch
        new_player_ids = set(transfer_request.player_ids) - set(existing_player_ids)
        players = await Player.find(In(Player.player_id, new_player_ids)).to_list()

        launches_to_add = []
        for player in players:
            launch = TaskLaunchService.add_transfer_task_launch(player, transfer_request)
            if launch:
                launches_to_add.append(launch)

        if launches_to_add:
            await TaskLaunch.insert_many(launches_to_add)

    @staticmethod
    def add_transfer_task_launch(player: Player, transfer_request: TransferBalanceRequest) -> TaskLaunch | None:
        # calculate the amount to transfer based on platform
        amount = TaskLaunchService._calculate_transfer_amount(
            player,
            transfer_request.keep_amount,
            transfer_request.currency,
        )
        if amount <= 0:
            return None

        launch = TaskLaunch(
            player_id=player.player_id,
            type=BotType.TRANSFER.value,
            currency=transfer_request.currency,
            transfer_amount=amount,
        )
        # We can use receiver_id and receiver_username from the request or from the database
        if player.platform_id == PlatformType.WPK.value:
            logger.debug(
                "TaskLaunchService.add_task_launches",
                f"Using receiver_id from transfer_request: {transfer_request.receiver_id}",
            )
            launch.receiver_id = transfer_request.receiver_id
        elif player.platform_id == PlatformType.WPTGO.value:
            logger.debug(
                "TaskLaunchService.add_task_launches",
                f"Using receiver_id from player: {player.receiver_id}",
            )
            launch.receiver_id = player.receiver_id
            launch.receiver_username = player.receiver_username

        return launch

    @staticmethod
    def _calculate_transfer_amount(player: Player, keep_amount: float, currency: CurrencyType) -> int:
        """Calculate the transfer amount based on player's platform and currency."""
        balance = 0

        match player.platform_id:
            case PlatformType.RWPK.value:
                raise ValueError("RWPK platform is deprecated and does not support transfers.")
            case PlatformType.WPK.value:
                if currency == CurrencyType.DIAMOND:
                    balance = player.balance.diamond
                else:
                    balance = player.balance.gold
            case PlatformType.WPTGO.value:
                balance = player.balance.usd

        return int(balance - keep_amount)
