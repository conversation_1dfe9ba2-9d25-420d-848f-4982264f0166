import uuid
import random

from datetime import datetime, timed<PERSON><PERSON>
from zoneinfo import ZoneInfo
from pydantic import BaseModel
from typing import Optional, List

from beanie import BulkWriter
from beanie.operators import In

from src.db.models import FutureLaunch, Player, Launch, Table, Tournament, FutureTournamentCheck
from src.schemas.requests import BotStartRequest, Seat, AddFutureLaunchRequest
from src.schemas.responses import BotResponse, ErrorResponse, LaunchResponse
from src.mq.bullmq_manager import mq_manager, BullMQException
from src.utils.enums import AppType, PlayerStatus, BotType
from src.utils.logging import log_duration, logger
from src.utils.settings import settings
from src.services.ip_pool_service import IPPoolConfigurationService


class LaunchToUpdate(BaseModel):
    launch_id: str
    stop_reason: str | None


class BotService:
    @staticmethod
    async def get_bots() -> List[BotResponse]:
        bots = await mq_manager.get_jobs()
        return [BotResponse(bot_id=bot.id, redis_data=bot.data) for bot in bots]

    @staticmethod
    async def start_bots(bot_start_request: BotStartRequest) -> List[BotResponse]:
        name = "start_bots"
        bot_responses = []
        delay = 0
        players = await Player.find(
            Player.enabled == True,  # noqa: E712
            Player.status == PlayerStatus.IDLE.value,
            In(Player.player_id, [seat.player_id for seat in bot_start_request.seats])
        ).to_list()

        tables = await Table.find(
            In(Table.table_id, [seat.table_id for seat in bot_start_request.seats]),
        ).to_list()

        for seat_request in bot_start_request.seats:
            try:
                player = next((p for p in players if p.player_id == seat_request.player_id), None)
                if not player:
                    raise ValueError(f"Player with ID {seat_request.player_id} not found or not idle")

                app_id = next((t.app_id for t in tables if t.table_id == seat_request.table_id), None)

                if not app_id and seat_request.table_id == "0" and seat_request.create_room_params is not None:
                    # Create and play on new friends table
                    app_id = AppType.FRIENDS

                seat = Seat(
                    player=player,
                    app_id=app_id or player.app_id,
                    **seat_request.model_dump(exclude_unset=True, exclude={"player_id", "attempts"})
                )

                logger.info(name, f"Starting bot for player {player.player_id}, seat={seat_request.model_dump()}")
                bot_response = await BotService.start_bot(
                    seat=seat,
                    bot_type=BotType(bot_start_request.type),
                    delay=delay,
                    attempts=seat_request.attempts
                )
                bot_responses.append(bot_response)
            except ValueError as e:
                error_response = ErrorResponse(error_message=str(e))
                bot_responses.append(error_response)
                continue

            if bot_start_request.delay_min and bot_start_request.delay_max:
                delay += random.uniform(bot_start_request.delay_min, bot_start_request.delay_max)

        return bot_responses

    @staticmethod
    async def start_bot(
        seat: Seat,
        bot_type: BotType,
        delay: int = 0,
        attempts: int = 3,
        is_from_future_launch: bool = False,
        config_id: int = None
    ) -> BotResponse:
        """
        Validate existing FutureLaunches and PlayTimeRange,
        create new Launch,
        add RabbitMQ job,
        update Player fields
        """
        name = "start_bot"

        now = datetime.now(ZoneInfo("UTC"))
        player = seat.player

        if player.status != PlayerStatus.IDLE.value:
            error_message = f"Player {player.player_id} is not IDLE, current status: {player.status}"
            logger.error(name, error_message)
            raise ValueError(error_message)

        if player.bot_id:
            job = await mq_manager.get_job(player.bot_id)
            if job:
                error_message = f"Player {player.player_id} already has a bot with id {player.bot_id}, job found in the queue"
                logger.error(name, error_message)
                raise ValueError(error_message)
            else:
                logger.warning(name, f"Player {player.player_id} has a bot with id {player.bot_id}, but job not found in the queue")

        # check if player is reserved for future launch
        if not is_from_future_launch:
            max_booked_launch_time = now + timedelta(hours=settings.no_launch_range_hours)
            scheduled_for_future_launch = await FutureLaunch.find(
                FutureLaunch.player_id == player.player_id,
                FutureLaunch.starting_time <= max_booked_launch_time,
            ).exists()
            if scheduled_for_future_launch:
                error_message = f"Player {player.player_id} is reserved for future launch"
                logger.error(name, error_message)
                raise ValueError(error_message)

            if bot_type == BotType.PLAY:
                play_time = now.time()
                if not player.is_in_play_time_range(play_time):
                    error_message = (
                        f"Failed to start bot, play time not in range, player_id:{player.player_id}, " +
                        f"play_time_range:{player.play_time_range}, play_time:{play_time}"
                    )
                    logger.error(name, error_message)
                    raise ValueError(error_message)

        # IP Pool
        proxy_config = await IPPoolConfigurationService.get_configuration_for_player(player)
        proxy_url = proxy_config.full_proxy_url if proxy_config else None
        if proxy_url:
            logger.info(name, f'Proxy url: {proxy_url} is used for player {player.player_id}')
        else:
            logger.info(name, f'Proxy url is not used for player {player.player_id}')

        data = BotService.prepare_start_bot_data(seat, bot_type, proxy_url)

        # create job id
        match bot_type:
            case BotType.SCAN:
                job_id = f"scan:{player.app_id}"
                if seat.club_id:
                    job_id = f"scan:{seat.app_id}:{seat.club_id}"
            case BotType.BALANCE:
                job_id = f"balance:{player.platform_id}:{player.player_id}"
            case BotType.CHECK:
                job_id = f"check:{player.platform_id}:{player.player_id}"
            case BotType.PLAY:
                job_id = f"play:{player.platform_id}:{player.player_id}"
            case BotType.TRANSFER:
                job_id = f"transfer:{player.platform_id}:{player.player_id}"
            case _:
                raise ValueError(f"Unknown bot type: {bot_type}")

        # check if scanning bot was already started
        if bot_type == BotType.SCAN:
            scan_player_query = Player.find(
                Player.bot_type == BotType.SCAN.value,
            )
            if seat.club_id:
                scan_player_query = scan_player_query.find(
                    In(Player.club_ids, [seat.club_id]),  # can fail if one bot is in multiple clubs
                )
            else:
                scan_player_query = scan_player_query.find(
                    Player.app_id == seat.app_id,
                )

            scan_player = await scan_player_query.first_or_none()
            if scan_player is not None:
                error_message = f"Scan bot is already running for app {player.app_id}, player {scan_player.player_id}"
                if seat.club_id:
                    error_message += f", club {seat.club_id}"
                    # check for multiple clubs
                    if len(scan_player.club_ids) > 1:
                        error_message += f", clubs: {scan_player.club_ids}"
                        logger.error(name, "Scan players should not have multiple clubs")
                logger.error(name, error_message)
                raise ValueError(error_message)

        # produce message
        try:
            await mq_manager.add_job(data=data, job_id=job_id, job_type=bot_type.value, delay=delay, attempts=attempts)
        except BullMQException as e:
            error_message = f"Error while adding job {job_id} to the queue: {str(e)}"
            logger.error(name, error_message, e)
            raise ValueError(error_message)

        launch_id = await BotService.create_new_launch(seat, config_id=config_id)
        await mq_manager.update_job(job_id, {"launchId": launch_id})

        # update player model
        player.updated_at = datetime.now(ZoneInfo("UTC"))
        player.bot_id = job_id
        player.bot_type = bot_type.value
        player.should_stop = False
        player.last_error = None
        player.status = PlayerStatus.PENDING.value
        player.launch_id = launch_id
        player.ticket_id = seat.ticket_id
        player.strategy_profile = seat.strategy_profile

        player.hands_played = 0
        player.total_buy_in = 0
        player.last_buy_in = 0
        player.rebuy_count = 0
        player.stack = 0

        if bot_type == BotType.PLAY:
            player.table_id = seat.table_id

        await player.save()

        logger.info(name, f"Bot {job_id} started for player {player.player_id}")

        return BotResponse(redis_data=data, bot_id=job_id, player=player.to_response())

    @staticmethod
    def prepare_start_bot_data(seat: Seat, bot_type: BotType, proxy_url: str | None) -> dict:
        data = {
            "appId": seat.app_id or seat.player.app_id,
            "tableId": seat.table_id,
            "playerId": seat.player.player_id,
            "platformId": seat.player.platform_id,
            "shouldStop": False,
            "type": bot_type.value,
            "proxyUrl": proxy_url,
        }
        # Add optional fields
        if seat.buy_in_multiplier is not None:
            data["buyInMultiplier"] = seat.buy_in_multiplier
        if seat.rebuy_enabled is not None:
            data["rebuyEnabled"] = seat.rebuy_enabled
        if seat.rebuy_threshold is not None:
            data["rebuyThreshold"] = seat.rebuy_threshold
        if seat.ticket_id is not None:
            data["ticketId"] = seat.ticket_id
        if seat.withdraw_amount is not None:
            data["withdrawAmount"] = seat.withdraw_amount
        if seat.withdraw_threshold is not None:
            data["withdrawThreshold"] = seat.withdraw_threshold
        if seat.strategy_profile is not None:
            data["profileName"] = seat.strategy_profile
        if seat.create_room_params is not None:
            data["createRoomParams"] = seat.create_room_params
        if seat.club_id is not None:
            data["clubId"] = seat.club_id
        if seat.max_rebuy_count is not None:
            data["maxRebuyCount"] = seat.max_rebuy_count

        if bot_type == BotType.TRANSFER:
            data["transferAmount"] = seat.transfer_amount
            data["receiverId"] = seat.receiver_id
            data["receiverUsername"] = seat.receiver_username
            data["currency"] = seat.currency

        # Multiple app ids for scan bot
        if bot_type == BotType.SCAN and seat.app_ids is not None:
            data["appIds"] = seat.app_ids
        return data

    @staticmethod
    async def stop_bot(bot_id: str, reason: str, delay_seconds: int | None = None):
        """
        Update RabbitMQ job,
        Delete job if player not found,
        Update Player and finish Launch if job not found
        """
        name = "stop_bot"

        if not bot_id:
            error_message = "Bot ID is required to stop the bot"
            logger.error(name, error_message)
            raise ValueError(error_message)

        logger.info(name, f"Stopping {bot_id=}, {reason=}")

        job_id = None
        # update redis bot data
        try:
            data = {"shouldStop": True, "stopReason": reason, "stopDelay": delay_seconds}
            job_id = await mq_manager.update_job(bot_id=bot_id, data=data)
        except BullMQException as exc:
            logger.error(name, f"Error updating job {bot_id}", str(exc))

        # update player status
        player = await Player.find_one(Player.bot_id == bot_id)

        if player is None:
            try:
                job_id = await mq_manager.remove_job(bot_id)
                logger.error(name, f"Player with {bot_id=} not found, but bot was stopped")
            except BullMQException as exc:
                logger.error(name, "Could not remove bot", exc)
            return

        # Remove the job from the Redis if it stuck in PENDING state
        if player.status == PlayerStatus.PENDING.value:
            try:
                job_id = await mq_manager.remove_job(bot_id)
            except BullMQException as exc:
                logger.error(name, "Player is in PENDING state, but job was not found in the queue", exc)
            except Exception as exc:
                logger.error(name, "Failed to remove PENDING job", exc)
                return

        player.should_stop = True

        if job_id is None:
            error_text = "No job was found in the queue"
            launch = await Launch.find_one(Launch.launch_id == player.launch_id)
            await BotService.finish_launch(launch, error_text)

            player.status = PlayerStatus.IDLE.value
            player.bot_id = None
            player.table_id = None
            player.bot_type = None
            player.last_error = error_text
            player.launch_id = None
            player.strategy_profile = None

        await player.save()

    @staticmethod
    async def add_future_launch(request: AddFutureLaunchRequest):
        future_launch_data = request.model_dump(exclude_none=True)
        future_launch = FutureLaunch(**future_launch_data)

        await future_launch.insert()
        return future_launch

    @staticmethod
    async def create_future_launches(future_launches: List[FutureLaunch]):
        name = 'bots.create_future_launches'
        async with BulkWriter() as bulk_writer:
            for future_launch in future_launches:
                if await FutureLaunch.find(
                    FutureLaunch.player_id == future_launch.player_id,
                    FutureLaunch.tournament_id == future_launch.tournament_id,
                    FutureLaunch.app_id == future_launch.app_id
                ).exists():
                    continue

                tournament = await Tournament.find_one(
                    Tournament.tournament_id == future_launch.tournament_id,
                    Tournament.app_id == future_launch.app_id
                )
                if tournament is None:
                    logger.error(name, f'Failed to create future launch for player_id:{future_launch.player_id} '
                                 f'tourmanent_id:{future_launch.tournament_id}', f'Tournament {future_launch.tournament_id} not found')
                    continue

                future_launch.starting_time = tournament.starting_time
                await future_launch.save()

            await bulk_writer.commit()

    @staticmethod
    async def create_future_tournament_checks(future_tournament_checks: list[FutureTournamentCheck]):
        if future_tournament_checks:
            await FutureTournamentCheck.insert_many(future_tournament_checks)

    @staticmethod
    async def create_new_launch(seat: Seat, config_id: int) -> str:
        launch = Launch(
            launch_id=f"la-{uuid.uuid4()}",
            app_id=seat.app_id or seat.player.app_id,
            player_id=seat.player.player_id,
            table_id=seat.table_id or "",
            config_id=config_id,
            strategy_profile=seat.strategy_profile,
            can_reenter_tournament=seat.can_reenter_tournament,
            created_at=datetime.now(ZoneInfo("UTC")),
        )
        await launch.save()

        logger.info('bot_service.create_new_launch',
                    f'Created new launch {launch.launch_id} for {seat.player.player_id=} {seat.table_id=}')
        return launch.launch_id

    @staticmethod
    async def finish_launch(launch: Launch, stop_reason: str | None):
        name = 'bots.finish_launch'

        stop_reason = stop_reason.replace("\n", " | ") if stop_reason else None
        launch.error = stop_reason
        launch.updated_at = datetime.now(ZoneInfo("UTC"))
        launch.finished = True

        time_played = datetime.now(ZoneInfo("UTC")) - launch.created_at.replace(tzinfo=ZoneInfo("UTC"))
        logger.debug(name, f'Finished launch {launch.launch_id}, {stop_reason=}. '
                     f'{launch.player_id=} was playing for {time_played.total_seconds():.2f}s')
        await launch.save()

    @log_duration
    @staticmethod
    async def finish_launches(launch_updates: List[LaunchToUpdate]):
        launches = await Launch.find(
            In(Launch.launch_id, [lu.launch_id for lu in launch_updates])
        ).to_list()
        launches_dict = {launch.launch_id: launch for launch in launches}
        async with BulkWriter() as bulk_writer:
            for launch_update in launch_updates:
                launch = launches_dict.get(launch_update.launch_id)
                await BotService.finish_launch(launch, launch_update.stop_reason)
            await bulk_writer.commit()

    @staticmethod
    async def get_launches(player_id: str, finished: Optional[bool] = None) -> List[LaunchResponse]:
        # Build query with optional finished filter
        query = [Launch.player_id == player_id]
        if finished is not None:
            query.append(Launch.finished == finished)
        launches = await Launch.find(*query).sort(-Launch.created_at).to_list()
        return [launch.to_response() for launch in launches]
