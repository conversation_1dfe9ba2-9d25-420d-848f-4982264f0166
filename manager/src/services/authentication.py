import base64
import hashlib
import hmac

import jwt
import requests
from fastapi import HTTP<PERSON>xception, Request
import boto3
from botocore.exceptions import ClientError

from src.utils.settings import settings
from src.schemas.requests import SignInRequest, RefreshTokenRequest


class AuthService:
    def __init__(self):
        if settings.auth_disabled:
            return
        self.cognito_client = (
            boto3.client("cognito-idp", region_name=settings.cognito_region,
                         aws_access_key_id=settings.secret_key,
                         aws_secret_access_key=settings.secret_access_key))
        self.JWKS_URL = f"https://cognito-idp.{settings.cognito_region}.amazonaws.com/{settings.cognito_user_pool_id}/.well-known/jwks.json"

    def calculate_secret_hash(self, sub: str) -> str:
        message = sub + settings.cognito_client_id
        secret = settings.cognito_client_secret.encode('utf-8')
        message = message.encode('utf-8')

        dig = hmac.new(secret, message, hashlib.sha256)
        return base64.b64encode(dig.digest()).decode()

    def get_user_sub(self, access_token: str) -> str:
        """Fetch the user's 'sub' (unique identifier) from Cognito using the access token."""
        try:
            response = self.cognito_client.get_user(AccessToken=access_token)
            for attribute in response["UserAttributes"]:
                if attribute["Name"] == "sub":
                    return attribute["Value"]
            raise ValueError("User 'sub' attribute not found.")
        except ClientError as e:
            raise HTTPException(status_code=401, detail=f"Failed to fetch user sub: {str(e)}")

    async def signin(self, data: SignInRequest):
        try:
            secret_hash = self.calculate_secret_hash(data.email)
            response = self.cognito_client.initiate_auth(
                AuthFlow="USER_PASSWORD_AUTH",
                AuthParameters={
                    "USERNAME": data.email,
                    "PASSWORD": data.password,
                    "SECRET_HASH": secret_hash
                },
                ClientId=settings.cognito_client_id
            )
            access_token = response["AuthenticationResult"]["AccessToken"]
            id_token = response["AuthenticationResult"]["IdToken"]
            refresh_token = response["AuthenticationResult"]["RefreshToken"]

            token_data = self.decode_token(access_token)
            user_sub = token_data.get("sub")
            user_groups = token_data.get("cognito:groups", [])

            return {
                "access_token": access_token,
                "id_token": id_token,
                "refresh_token": refresh_token,
                "sub": user_sub,
                "groups": user_groups
            }

        except ClientError as e:
            raise HTTPException(status_code=401, detail=f"Authentication failed: {str(e)}")

    async def refresh_token(self, data: RefreshTokenRequest):
        try:
            secret_hash = self.calculate_secret_hash(data.sub)
            response = self.cognito_client.initiate_auth(
                AuthFlow='REFRESH_TOKEN_AUTH',
                AuthParameters={
                    'REFRESH_TOKEN': data.refresh_token,
                    'SECRET_HASH': secret_hash
                },
                ClientId=settings.cognito_client_id,
            )
            return {
                "access_token": response["AuthenticationResult"]["AccessToken"],
                "id_token": response["AuthenticationResult"]["IdToken"]
            }
        except ClientError as e:
            if "InvalidRefreshTokenException" in str(e):
                raise HTTPException(status_code=400, detail="Refresh token expired or invalid, please log in again.")
            raise HTTPException(status_code=401, detail=f"Token refresh failed: {str(e)}")

    async def verify_token(self, request: Request):
        """
            Dependency to verify the AccessToken.
        """

        auth_header = request.headers.get("Authorization")
        if not auth_header:
            raise Exception("Authorization header missing")

        try:
            # I got as an Authorization header Bearer {token} and extract exactly token there
            token = auth_header.split(" ")[1]
            # Perform token validation
            self.cognito_client.get_user(AccessToken=token)
            return request
        except IndexError:
            raise Exception("Invalid token format")
        except ClientError as e:
            raise Exception(f"Authentication failed due to: {str(e)}")

    def decode_token(self, token: str):
        try:
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get("kid")
            jwks = requests.get(self.JWKS_URL).json()
            signing_key = next(
                (jwt.algorithms.RSAAlgorithm.from_jwk(key) for key in jwks["keys"] if key["kid"] == kid),
                None
            )
            if not signing_key:
                raise HTTPException(status_code=401, detail="Invalid token signing key")

            payload = jwt.decode(
                token,
                signing_key,
                algorithms=["RS256"],
                options={"verify_aud": False},
                issuer=f"https://cognito-idp.{settings.cognito_region}.amazonaws.com/{settings.cognito_user_pool_id}",
            )

            return payload

        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token has expired")

        except jwt.InvalidTokenError as e:
            raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error decoding token: {str(e)}")
