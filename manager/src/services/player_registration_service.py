import random
import string
import uuid
from typing import List

import phone_gen
from beanie.operators import In
from fastapi import H<PERSON><PERSON>Exception

from src.db.models import Player, PendingRegistration
from src.schemas.requests import RegisterPlayerRequest
from src.schemas.responses import RegisterPlayerResponse, PendingRegistrationResponse
from src.services.ip_pool_service import IPPoolConfigurationService
from src.services.platforms.wpk import WPKService
from src.services.platforms.wptgo import WptgoService
from src.utils.enums import PlatformType, PlayerStatus
from src.utils.logging import logger
from src.utils.requests import post
from src.utils.settings import settings
from src.services.worker_service import WorkerService


class PlayerRegistrationService:
    logger_name = "player_registration_service"

    def __init__(self):
        self.wpk_service = WPKService()
        self.wptgo_service = WptgoService()

    async def register_players(self, user_accounts: List[RegisterPlayerRequest]) -> List[RegisterPlayerResponse]:
        """Register multiple players and return their registration responses."""
        responses = []
        for user_account in user_accounts:
            response = await self.register_player(user_account)
            responses.append(response)
        return responses

    async def register_player(
        self,
        request: RegisterPlayerRequest,
        is_register_request: bool = True
    ) -> RegisterPlayerResponse:
        """Register a single player, handling all steps from password generation to DB and IP pool assignment."""
        response = RegisterPlayerResponse(
            app_id=request.app_id,
            external_ip=None,
            country_code=request.country_code,
            user_id=request.user_id,
        )
        if not request.password:
            request.password = self._generate_password()
        new_player_id = str(uuid.uuid4())
        try:
            ip_conf = await IPPoolConfigurationService.get_free_ip(request.app_id, request.country_code)
            if ip_conf is None:
                if IPPoolConfigurationService.ip_pool_enabled():
                    raise Exception("No free IP pool configuration found")
                logger.warning(self.logger_name,
                               "Failed to find free IP pool value, player will not use proxy IP")
            external_ip = ip_conf.external_ip if ip_conf else None

            device_id = str(uuid.uuid4())
            if is_register_request:
                await self._register_in_game_app(request, response, device_id, external_ip)
            await self._send_player_to_worker(request, new_player_id, response.user_id, device_id)
        except Exception as e:
            response.error = "Failed to add user"
            response.exception = str(e)
            return response

        await self._add_single_player(request, new_player_id)

        if ip_conf:
            try:
                await IPPoolConfigurationService.assign_player_to_ip(
                    player_id=new_player_id, ip_conf_id=ip_conf.ip_conf_id
                )
            except Exception as e:
                logger.error(f"{self.logger_name}.register_player", "Failed to assign player to IP pool", str(e))
                raise e

        response.player_id = new_player_id
        response.password = request.password
        response.platform_id = request.platform_id
        response.external_ip = external_ip
        return response

    def _generate_password(self) -> str:
        characters = string.ascii_letters + string.digits
        return "".join(random.choice(characters) for _ in range(8))

    async def _register_in_game_app(self, request: RegisterPlayerRequest, response: RegisterPlayerResponse,
                                    device_id: str, external_ip: str = None):
        if not request.area_code and request.phone_number:
            request.area_code = f"+{phone_gen.PhoneNumber(request.country_code).get_code()}"

        if request.platform_id == PlatformType.WPK.value:
            await self._register_in_wpk(request, response, external_ip)
        elif request.platform_id == PlatformType.WPTGO.value:
            await self._register_in_wptgo(request, response, device_id, external_ip)
        else:
            raise Exception(f"Platform {request.platform_id} is not supported")

    async def _register_in_wpk(self, request: RegisterPlayerRequest, response: RegisterPlayerResponse,
                               external_ip: str = None):
        try:
            wpk_response = await self.wpk_service.register_user(
                request.username,
                request.account,
                request.password,
                request.country_code,
                request.area_code,
                request.phone_number,
                external_ip,
            )
        except Exception as e:
            logger.error(f"{self.logger_name}._register_in_wpk", "Failed to register user", str(e))
            raise Exception(f"Failed to register user: {str(e)}")

        if wpk_response.get("data") is None:
            logger.error(
                f"{self.logger_name}._register_in_wpk",
                "Failed to register user, no data in response",
                wpk_response,
            )
            raise Exception(
                f"Failed to register user, no data in response: {wpk_response.get('errMsg', '')}. {str(wpk_response)}")

        response.user_id = str(wpk_response["data"].get("userId"))
        response.account = wpk_response["data"].get("account")
        response.username = wpk_response["data"].get("nickname")

    async def _register_in_wptgo(self, request: RegisterPlayerRequest, response: RegisterPlayerResponse,
                                 device_id: str, external_ip: str = None):
        # email or phone number is required
        if not request.email and not request.phone_number:
            logger.error(
                f"{self.logger_name}._register_in_wptgo",
                "Email or phone number is required for registration",
            )
            raise Exception("Email or phone number is required for registration")

        area_code = request.area_code if not request.email else None
        phone_number = request.phone_number if not request.email else None

        wptgo_response = await self.wptgo_service.register_user(
            request.username,
            request.password,
            request.country_code,
            area_code,
            phone_number,
            request.email,
            device_id,
            external_ip,
            request.model_extra
        )
        if wptgo_response.get("error") is not None:
            logger.error(
                f"{self.logger_name}._register_in_wptgo",
                "Failed to register user",
                wptgo_response.get("error"),
            )
            raise Exception(f"Failed to register user: {wptgo_response.get('error')}")

        response.user_id = str(wptgo_response.get("user_id"))
        if request.phone_number:
            response.area_code = request.area_code
            response.phone_number = request.phone_number
        else:
            response.email = request.email

    async def _send_player_to_worker(self, request: RegisterPlayerRequest,
                                     player_id: str, user_id: str = "", device_id: str = None):
        worker_user_account_data = {
            "appId": request.app_id,
            "userAccounts": [
                {
                    "playerId": player_id,
                    "username": request.account or request.username,
                    "password": request.password,
                    "countryCode": request.country_code,
                    "areaCode": request.area_code or None,
                    "phoneNum": request.phone_number or None,
                    "email": request.email or None,
                    "userId": user_id,
                    "platformId": request.platform_id,
                    "deviceId": device_id
                }
            ],
        }
        try:
            await post(f"{settings.worker_url}/user_accounts", worker_user_account_data)
        except Exception as e:
            errorMessage = f"Failed to add user account to worker at {settings.worker_url}, " + \
                "player still registered, but worker did not receive data"
            logger.error(f"{self.logger_name}._send_player_to_worker", errorMessage, str(e))
            raise e

    async def _add_single_player(
        self, player_request: RegisterPlayerRequest, player_id: str
    ) -> Player:
        player = Player(
            player_id=player_id,
            app_id=player_request.app_id,
            platform_id=player_request.platform_id,
            country_code=player_request.country_code,
            enabled=True,
            status=PlayerStatus.IDLE.value,
            receiver_id=player_request.receiver_id,
            receiver_username=player_request.receiver_username,
            club_ids=player_request.club_ids or [],
        )

        return await player.save()

    async def add_pending_registrations(
        self, request: List[RegisterPlayerRequest]
    ) -> List[PendingRegistrationResponse]:
        """Add pending registrations to the database."""
        pending_registrations: List[PendingRegistration] = []
        for player_request in request:
            pending_registration = PendingRegistration(
                **player_request.model_dump(exclude_unset=True)
            )
            pending_registration.extra_data = player_request.model_extra
            pending_registrations.append(pending_registration)
        await PendingRegistration.insert_many(pending_registrations)
        return [pr.to_response() for pr in pending_registrations]

    async def list_pending_registrations(
        self, app_id: int | None = None
    ) -> List[PendingRegistrationResponse]:
        """List pending registrations, optionally filtered by app_id."""
        query = []
        if app_id is not None:
            query.append(PendingRegistration.app_id == app_id)
        pending_registrations = await PendingRegistration.find(*query).to_list()

        return [pr.to_response() for pr in pending_registrations]

    async def delete_pending_registrations(
        self, pending_registration_ids: List[str] | None = None, app_id: int | None = None
    ) -> None:
        """Delete pending registrations by IDs, app_id or all of them."""
        query = []
        if pending_registration_ids:
            query.append(In(PendingRegistration.pending_registration_id, pending_registration_ids))
        if app_id is not None:
            query.append(PendingRegistration.app_id == app_id)

        await PendingRegistration.find(*query).delete()

    async def change_avatar(self, player_id: str, avatarBase64: str,  imgExt: str) -> Player:
        player = await Player.find_one(Player.player_id == player_id)
        if player is None:
            raise HTTPException(status_code=404, detail=f"Player {player_id} not found")

        if player.platform_id != PlatformType.WPTGO.value:
            raise HTTPException(
                status_code=400, detail=f"Player {player_id} is not a WPTGO player, avatar change is not supported for platforms other than WPTGO")

        user_id = await WorkerService().get_user_id_from_worker(player_id=player_id)
        if user_id is None:
            raise HTTPException(status_code=404, detail=f"User_id for player {player_id} not found")

        try:
            result = await self.wptgo_service.change_avatar(avatarBase64=avatarBase64, imgExt=imgExt, user_id=user_id)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"WPTGO request failed to change avatar: {str(e)}")

        from src.services.players import PlayerService
        return await PlayerService.update_avatar(player_id=player_id, avatar_url=result.get("avatarUrl"))
