from typing import Optional

from src.db.models import MultiflightConfiguration
from src.schemas.responses import MultiflightConfigurationResponse


class MultiflightConfigurationService:
    @staticmethod
    async def upsert(request: MultiflightConfiguration) -> MultiflightConfiguration:
        mf_config = await MultiflightConfiguration.find_one(
            MultiflightConfiguration.multi_flight_id == request.multi_flight_id)
        if mf_config is None:
            mf_config = MultiflightConfiguration(
                multi_flight_id=request.multi_flight_id,
                app_id=request.app_id,
                day_one_tournament_ids=[],
                day_two_tournament_id="-1"
            )

        mf_config.app_id = request.app_id
        # Concatenating unique old + new
        mf_config.day_one_tournament_ids = list(set(
            request.day_one_tournament_ids + mf_config.day_one_tournament_ids))
        if request.day_two_tournament_id:
            mf_config.day_two_tournament_id = request.day_two_tournament_id
        return await mf_config.save()

    @staticmethod
    async def get_mf_configuration(mf_id: int) -> Optional[MultiflightConfigurationResponse]:
        mf_config = await MultiflightConfiguration.find_one(MultiflightConfiguration.multi_flight_id == mf_id)
        return mf_config.to_response() if mf_config else None
