from beanie.odm.operators.update.general import Set

from src.utils.enums import CurrencyType
from src.db.models import AutoStartAction, AutoStartConfig, TablesAutomationConfig
from src.schemas.requests import TablesAutomationConfigRequest
from src.schemas.responses import AutoStartConfigResponse


class AutomationService:
    @staticmethod
    async def upsert_autostart_config(config: AutoStartConfig) -> None:
        await AutoStartConfig.find(AutoStartConfig.app_id == config.app_id).upsert(Set(config.model_dump()), on_insert=config)

    @staticmethod
    async def get_or_create_autostart_config(app_id: int) -> AutoStartConfigResponse:
        config = await AutoStartConfig.find(AutoStartAction.app_id == app_id).first_or_none()
        if config is None:
            config = AutoStartConfig(app_id=app_id)
            await AutomationService.upsert_autostart_config(config)
        return config.to_response()

    async def get_tables_automation_config(app_id: int, club_id: int = None, config_id: int = None) -> list[TablesAutomationConfig]:
        configs = []
        if config_id is not None:
            config = await TablesAutomationConfig.find_one(
                TablesAutomationConfig.app_id == app_id,
                TablesAutomationConfig.config_id == config_id
            )
            if not config:
                raise ValueError("Config not found")
            configs = [config]
        else:
            query = TablesAutomationConfig.find(
                TablesAutomationConfig.app_id == app_id
            )
            if club_id is not None:
                query = query.find(TablesAutomationConfig.club_id == club_id)

            configs = await query.to_list()

        return configs

    @staticmethod
    async def set_tables_automation_config(request: TablesAutomationConfigRequest) -> TablesAutomationConfig:
        if CurrencyType.DIAMOND in request.currencies and request.withdraw_amount > 0:
            raise ValueError("Diamond tables do not support withdrawal")

        config = None
        if request.config_id:
            existing_config = await TablesAutomationConfig.find(
                TablesAutomationConfig.app_id == request.app_id,
                TablesAutomationConfig.config_id == request.config_id
            ).first_or_none()

            if existing_config:
                config = await existing_config.update(Set(request.model_dump(exclude_unset=True)))
            else:
                raise ValueError("Tables automation config not found")
        else:
            config = await TablesAutomationConfig(
                **request.model_dump(exclude=("config_id")),
                config_id=await TablesAutomationConfig.get_next_config_id(),
            ).save()

        return config

    async def delete_tables_automation_config(app_id: int, config_id: int) -> None:
        config = await TablesAutomationConfig.find_one(
            TablesAutomationConfig.app_id == app_id,
            TablesAutomationConfig.config_id == config_id
        )
        if not config:
            raise ValueError("Tables automation config not found")

        await config.delete()
