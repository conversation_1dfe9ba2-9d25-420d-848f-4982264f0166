from beanie.odm.operators.update.general import Set

from src.db.models import RegistrationAutomationConfig
from src.schemas.responses import RegistrationAutomationConfigResponse
from src.schemas.requests import RegistrationAutomationConfigRequest


class RegistrationAutomationService:
    @staticmethod
    async def get_registration_automation_config() -> RegistrationAutomationConfigResponse:
        config = await RegistrationAutomationConfig.find().first_or_none()
        return config.to_response()

    @staticmethod
    async def update_registration_automation_config(
        request: RegistrationAutomationConfigRequest,
    ) -> RegistrationAutomationConfigResponse:
        existing_config = await RegistrationAutomationConfig.find().first_or_none()
        config = await existing_config.update(Set(request.model_dump()))
        return config.to_response()
