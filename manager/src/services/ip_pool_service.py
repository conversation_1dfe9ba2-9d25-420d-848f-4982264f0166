# from pymongo import ASCENDING
from beanie.operators import All, In
from typing import List
from fastapi.exceptions import RequestValidationError
import uuid

from src.db.models import IPPoolConfiguration, Player
from src.schemas.requests import IPPoolConfigurationCreateRequest
from src.utils.settings import settings

from urllib.parse import urlparse
from phone_gen import PhoneNumber, PhoneNumberNotFound


class IPPoolConfigurationService:
    @staticmethod
    def ip_pool_enabled() -> bool:
        return settings.ip_pool_enabled

    @staticmethod
    async def get_all() -> List[IPPoolConfiguration]:
        if not IPPoolConfigurationService.ip_pool_enabled():
            return []
        return await IPPoolConfiguration.find().to_list()

    @staticmethod
    async def get_by_id(ip_conf_id: str) -> IPPoolConfiguration | None:
        if not IPPoolConfigurationService.ip_pool_enabled():
            return None
        return await IPPoolConfiguration.find(IPPoolConfiguration.ip_conf_id == ip_conf_id).first_or_none()

    @staticmethod
    async def create(request: IPPoolConfigurationCreateRequest) -> IPPoolConfiguration:
        check = await IPPoolConfiguration.find(IPPoolConfiguration.full_proxy_url == request.full_proxy_url).first_or_none()
        if check is not None:
            raise RequestValidationError(f"IP Pool configuration with full_proxy_url = {request.full_proxy_url} already exists (id: {check.ip_conf_id})")

        check = await IPPoolConfiguration.find(IPPoolConfiguration.external_ip == request.external_ip).first_or_none()
        if check is not None:
            raise RequestValidationError(f"IP Pool configuration with external_ip = {request.external_ip} already exists (id: {check.ip_conf_id})")

        try:
            PhoneNumber(request.country_code)
        except PhoneNumberNotFound:
            raise RequestValidationError("Input value country_code is incorrect. Correct examples: DE, GB, RU etc")

        ip_conf = IPPoolConfiguration(**request.model_dump(exclude_none=True), ip_conf_id=str(uuid.uuid4()))

        url_dict = IPPoolConfigurationService._parse_proxy_url(request.full_proxy_url)
        ip_conf.host = url_dict["host"]
        ip_conf.port = url_dict["port"]
        ip_conf.username = url_dict["username"]
        ip_conf.password = url_dict["password"]

        await ip_conf.insert()
        return ip_conf

    @staticmethod
    async def update(ip_conf_id: str, request: IPPoolConfigurationCreateRequest) -> IPPoolConfiguration:
        ip_conf = await IPPoolConfigurationService.get_by_id(ip_conf_id)
        if not ip_conf:
            raise RequestValidationError(f"IP Pool configuration was not found by id: {ip_conf_id}")

        update_object = request.model_dump(exclude_none=True)
        if request.full_proxy_url:
            url_dict = IPPoolConfigurationService._parse_proxy_url(request.full_proxy_url)
            update_object["host"] = url_dict["host"]
            update_object["port"] = url_dict["port"]
            update_object["username"] = url_dict["username"]
            update_object["password"] = url_dict["password"]
        return await ip_conf.set(update_object)

    @staticmethod
    async def delete(ip_conf_id: str):
        ip_conf = await IPPoolConfigurationService.get_by_id(ip_conf_id)
        if ip_conf is None:
            raise RequestValidationError(f"IP Pool configuration was not found by id: {ip_conf_id}")

        await ip_conf.delete()

    @staticmethod
    async def get_configuration_for_player(player: Player) -> IPPoolConfiguration | None:
        if not IPPoolConfigurationService.ip_pool_enabled():
            return None

        return await IPPoolConfiguration.find(
            All(IPPoolConfiguration.assigned_player_ids, [player.player_id])).first_or_none()

    @staticmethod
    async def get_multiple_configurations_for_players(player_ids: List[str]) -> List[IPPoolConfiguration]:
        if not IPPoolConfigurationService.ip_pool_enabled():
            return []

        return await IPPoolConfiguration.find(
            In(IPPoolConfiguration.assigned_player_ids, player_ids)
        ).to_list()

    @staticmethod
    async def assign_player_to_ip(player_id: str, ip_conf_id: str):
        ip_conf = await IPPoolConfigurationService.get_by_id(ip_conf_id)
        if ip_conf is None:
            raise RequestValidationError(f"IP Pool configuration was not found by id: {ip_conf_id}")

        if len(ip_conf.assigned_player_ids) >= settings.max_players_per_ip:
            raise RequestValidationError(f"IP Pool configuration has already maximum possible players ({settings.max_players_per_ip})")

        player = await Player.find(Player.player_id == player_id).first_or_none()
        if player is None:
            raise RequestValidationError(f"Player was not found by id: {player_id}")
        if player.country_code != ip_conf.country_code:
            raise RequestValidationError(
                f"Player country code ({player.country_code}) does not match IP's country code ({ip_conf.country_code})"
            )

        if not settings.disable_ip_pool_app_ids_check and ip_conf.assigned_player_ids:
            assigned_players = await Player.find(In(Player.player_id, ip_conf.assigned_player_ids)).to_list()
            for assigned_player in assigned_players:
                if assigned_player.app_id == player.app_id:
                    raise RequestValidationError(
                        f"IP Pool configuration ({ip_conf_id}) has already a player ({assigned_player.player_id})\
                            with the same app_id ({assigned_player.app_id}) as the requested player ({player_id}, app_id: {player.app_id})")

        if player_id in ip_conf.assigned_player_ids:
            raise RequestValidationError(f"Player ({player_id}) is already assigned to this exactly IP Pool configuration ({ip_conf_id})")

        # Check if player is already assigned to another IP configuration
        existing_conf = await IPPoolConfigurationService.get_configuration_for_player(player)
        if existing_conf and existing_conf.ip_conf_id != ip_conf_id:
            raise RequestValidationError(f"Player ({player_id}) is already assigned to another IP Pool configuration ({existing_conf.ip_conf_id})")

        ip_conf.assigned_player_ids.append(player_id)
        return await ip_conf.save()

    @staticmethod
    async def unassign_player_from_ip(player_id: str, ip_conf_id: str):
        ip_conf = await IPPoolConfigurationService.get_by_id(ip_conf_id)
        if ip_conf is None:
            raise RequestValidationError(f"IP Pool configuration was not found by id: {ip_conf_id}")

        if player_id not in ip_conf.assigned_player_ids:
            raise RequestValidationError(f"Player ({player_id}) is not yet assigned to IP Pool configuration ({ip_conf_id})")

        ip_conf.assigned_player_ids.remove(player_id)
        return await ip_conf.save()

    @staticmethod
    def _parse_proxy_url(url: str) -> dict[str, any]:
        parsed = urlparse(url)
        username = parsed.username
        password = parsed.password
        host = parsed.hostname
        port = parsed.port
        return {
            "username": username,
            "password": password,
            "host": host,
            "port": port
        }

    @staticmethod
    async def assign_player_to_new_free_ip(player_id: str, app_id: int) -> IPPoolConfiguration | None:
        player = await Player.find_one(Player.player_id == player_id)
        existing_conf = await IPPoolConfigurationService.get_configuration_for_player(player)
        if existing_conf:
            return existing_conf

        new_ip_conf = await IPPoolConfigurationService.get_free_ip(app_id=app_id)
        if not new_ip_conf:
            return None

        return await IPPoolConfigurationService.assign_player_to_ip(player_id=player_id, ip_conf_id=new_ip_conf.ip_conf_id)

    @staticmethod
    async def get_free_ip(app_id: int, country_code: str = None) -> IPPoolConfiguration | None:
        if not IPPoolConfigurationService.ip_pool_enabled():
            return None

        match_criteria = {
            "assigned_count": {"$lt": settings.max_players_per_ip}
        }  # Ensure assigned_count is not more than max_players_per_ip
        if country_code:
            match_criteria["country_code"] = country_code

        result = (
            await IPPoolConfiguration.find()
            .aggregate(
                [
                    {
                        "$project": {
                            "full_proxy_url": 1,
                            "assigned_player_ids": 1,
                            "external_ip": 1,
                            "ip_conf_id": 1,
                            "country_code": 1,
                            "assigned_count": {"$size": "$assigned_player_ids"},
                        }
                    },
                    {"$match": match_criteria},
                    {
                        "$sort": {
                            "assigned_count": 1
                        }  # Sort by `assigned_count` in ascending order
                    },
                ]
            )
            .to_list()
        )

        # Early return if check app_id_check is disabled (recomended for dev env only)
        if settings.disable_ip_pool_app_ids_check:
            return IPPoolConfiguration(**result[0]) if result else None

        # Now checking if the app_id is not in the already assigned app_ids
        for ip_conf in result:
            already_assigned_players = await Player.find(
                In(Player.player_id, ip_conf["assigned_player_ids"])
            ).to_list()
            already_assigned_app_ids = [player.app_id for player in already_assigned_players]
            if app_id not in already_assigned_app_ids:
                return IPPoolConfiguration(**ip_conf)
        return None
