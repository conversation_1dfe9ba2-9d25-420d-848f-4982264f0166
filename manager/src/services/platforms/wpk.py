import hashlib
import json
import phone_gen

from src.utils.settings import settings
from src.utils.requests import form_post
from src.utils.logging import logger


class WPKService:

    async def register_user(self, nickname, account, password, country_code, area_code=None, phone_number=None, external_ip=None):
        form_name = "regPlayer"
        payload = self._prepare_data_for_registration(nickname, account, password, country_code, area_code, phone_number, external_ip)
        param = json.dumps(payload)
        input_for_sign = form_name + param + settings.wpk_sign_salt
        sign = hashlib.md5(input_for_sign.encode("utf-8")).hexdigest()[8:16].upper()
        data = {
            "name": form_name,
            "param": param,
            "sign": sign,
        }

        url = f"{settings.wpk_url}/wepoker_boss/pn/router.anon"
        logger.info("WPKService.register_user", f"Making request: {url}, data: {data}")
        response = await form_post(url, data)
        return response

    def _prepare_data_for_registration(self, nickname, account, password, country_code, area_code=None, phone_number=None, external_ip=None):
        random_national_phone_number = phone_gen.PhoneNumber(country_code)

        payload = {
            "nickname": nickname,
            "password": password,
            "countryCode": area_code or f"+{random_national_phone_number.get_code()}",
            "phoneNum": phone_number or random_national_phone_number.get_number(full=False),
            "account": account,
            "ip": external_ip,
        }

        return payload
