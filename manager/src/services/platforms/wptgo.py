import aiohttp

from src.utils.settings import settings
from src.utils.logging import logger


class WptgoService:
    async def register_user(
        self,
        username,
        password,
        country_code,
        area_code="",
        phone_number="",
        email="",
        device_id=None,
        external_ip=None,
        optional_fields: dict = {},
    ):
        payload = {
            "ip_address": external_ip,
            "password": password,
            "nick_name": username,
            "user_name": username,
            "reg_country": country_code,
            "language": "en_US",
            "device_uuid": device_id,
            "license_type": "",
            "currency_code": "",
            "verification_type": 1,  # 1 is email, 0 is phone_number
            "email": email,
        }

        if area_code and phone_number:
            payload.update(
                {
                    "phone_number": phone_number,
                    "area_code": area_code,
                    "verification_type": 0,
                }
            )

        # Add optional fields to the payload
        if optional_fields:
            logger.info(
                "WptgoService.register_user",
                f"Adding optional fields to payload: {optional_fields}",
            )
            payload = payload | optional_fields

        log_data = payload.copy()
        log_data.pop("password", None)
        logger.info(
            "WptgoService.register_user",
            f"Payload: {log_data}, ",
        )

        headers = {
            "Content-Type": "application/json",
            "X-AppBundleID": settings.wptgo_app_bundle_id,
            "Authorization": f"Basic {settings.wptgo_token}",
        }

        async with aiohttp.ClientSession() as session:
            url = f"{settings.wptgo_url}/player/create"
            logger.info("WptgoService.register_user", f"Making request: {url}")
            async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                if response.status != 200:
                    error_message = await response.text()
                    logger.error(
                        "WptgoService.register_user",
                        f"Failed to register user: {error_message}, request: {payload}, headers: {headers}",
                        error_message,
                    )
                    raise Exception(f"Failed to register user: {error_message}")
                return await response.json()

    async def change_avatar(self, avatarBase64: str, imgExt: str, user_id: str):
        payload = {
            "avatarBase64": avatarBase64,
            "imgExt": imgExt,
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Basic {settings.wptgo_token}",
            "X-USER-ID": user_id,
        }

        async with aiohttp.ClientSession() as session:
            url = f"{settings.wptgo_url}/api/v1/user/avatars/custom"
            logger.info("WptgoService.change_avatar", f"Making request: {url}")
            async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                if response.status != 200:
                    error_message = await response.text()
                    logger.error(
                        "WptgoService.change_avatar",
                        f"Failed to change avatar: {error_message}, request: {payload}, headers: {headers}",
                        error_message,
                    )
                    raise Exception(f"Failed to change avatar: {error_message}")
                result = await response.json()

        return {
            "avatarType": result.get("avatarType"),
            "avatarUrl": result.get("avatarUrl"),
        }
