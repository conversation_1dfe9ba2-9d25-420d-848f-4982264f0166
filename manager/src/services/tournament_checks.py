from beanie.operators import In
from fastapi.exceptions import RequestValidationError

from src.db.models import FutureTournamentCheck, MultiflightConfiguration, FutureLaunch, Launch
from src.schemas.requests import TournamentCheckStartRequest
from src.schemas.responses import TournamentCheckResponse
from src.utils.enums import FutureTournamentCheckStatus


class TournamentCheckService:
    @staticmethod
    async def get_tournament_checks(app_id: int, multi_flight_id: int) -> list[TournamentCheckResponse]:
        # Multiflight configuration helps us to find the connection between day 2 and day 1 tournaments
        config = await MultiflightConfiguration.find(
            MultiflightConfiguration.app_id == app_id,
            MultiflightConfiguration.multi_flight_id == multi_flight_id
        ).first_or_none()
        if config is None:
            raise RequestValidationError("Multiflight configuration not found")

        checks = await FutureTournamentCheck.find(
            FutureTournamentCheck.app_id == app_id,
            In(FutureTournamentCheck.tournament_id, config.day_one_tournament_ids)
        ).to_list()
        future_launches = await FutureLaunch.find(
            FutureLaunch.app_id == app_id,
            FutureLaunch.tournament_id == config.day_two_tournament_id
        ).to_list()
        future_launches_player_ids = [launch.player_id for launch in future_launches]

        # get launch_ids to check if checks are over
        launch_ids = [check.launch_id for check in checks if check.launch_id]
        launches = await Launch.find(In(Launch.launch_id, launch_ids)).to_list()
        launches_dict = {launch.launch_id: launch for launch in launches}

        check_responses = []

        for check in checks:
            check_response = TournamentCheckResponse(
                player_id=check.player_id,
                tournament_id=check.tournament_id,
                app_id=check.app_id,
                checked=check.checked,
                status=FutureTournamentCheckStatus.NOT_AVAILABLE.value,
            )

            # If we have a future launch for this player, we can mark it as eligible 100%
            if check.player_id in future_launches_player_ids:
                check_response.day_2_tournament_id = config.day_two_tournament_id
                check_response.status = FutureTournamentCheckStatus.ELIGIBLE.value
            # If we have a launch_id and the launch is over, we can mark it as not eligible
            elif (launch := launches_dict.get(check.launch_id)) and launch.updated_at is not None:
                check_response.status = FutureTournamentCheckStatus.NOT_ELIGIBLE.value

            check_responses.append(check_response)

        return check_responses

    @staticmethod
    async def update_tournament_check(request: TournamentCheckStartRequest):
        check = await FutureTournamentCheck.find_one(
            FutureTournamentCheck.player_id == request.player_id,
            FutureTournamentCheck.tournament_id == request.tournament_id,
            FutureTournamentCheck.app_id == request.app_id,
        )
        if not check:
            raise RequestValidationError("Future tournament check not found")

        update_data = request.model_dump(exclude_none=True)

        await check.set(update_data)

        return TournamentCheckResponse(
            player_id=check.player_id,
            tournament_id=check.tournament_id,
            app_id=check.app_id,
            checked=check.checked
        )

    @staticmethod
    async def add_tournament_checks(checks: list[TournamentCheckStartRequest]) -> list[TournamentCheckResponse]:
        await FutureTournamentCheck.insert_many(
            [FutureTournamentCheck(**check.model_dump(exclude_unset=True)) for check in checks]
        )
        return [
            TournamentCheckResponse(
                **check.model_dump(exclude_unset=True),
                checked=getattr(check, "checked", False)
            )
            for check in checks
        ]
