{{- define "library-hpa" -}}
{{- $root := .root -}}
{{- $context := .context -}}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
{{- include "library-metadata" (dict "root" $root "context" $context) | nindent 0 }}
spec:
  scaleTargetRef:
    apiVersion: {{ $context.targetDeployment.apiVersion | default "apps/v1" | quote }}
    kind: {{ $context.targetDeployment.kind | default "Deployment" | quote }}
    name: {{ $context.targetDeployment.name | quote }}
  minReplicas: {{ $context.minReplicas | default 1 }}
  maxReplicas: {{ $context.maxReplicas | default 10 }}
  {{- if $context.metrics }}
  metrics:
    {{- range $metric := $context.metrics }}
    - type: {{ $metric.type | default "Resource" | quote }}
      {{- if eq ($metric.type | default "Resource") "Resource" }}
      resource:
        name: {{ $metric.name | quote }}
        target:
          type: {{ $metric.targetType | quote }}
          {{- if hasKey $metric "averageUtilization" }}
          averageUtilization: {{ $metric.averageUtilization }}
          {{- end }}
          {{- if hasKey $metric "averageValue" }}
          averageValue: {{ $metric.averageValue }}
          {{- end }}
          {{- if hasKey $metric "value" }}
          value: {{ $metric.value }}
          {{- end }}
      {{- else if eq $metric.type "Pods" }}
      pods:
        metric:
          name: {{ $metric.name | quote }}
        target:
          type: {{ $metric.targetType | default "AverageValue" | quote }}
          {{- if hasKey $metric "averageValue" }}
          averageValue: {{ $metric.averageValue }}
          {{- end }}
          {{- if hasKey $metric "value" }}
          value: {{ $metric.value }}
          {{- end }}
      {{- else if eq $metric.type "External" }}
      external:
        metric:
          name: {{ $metric.name | quote }}
          {{- if $metric.selector }}
          selector:
            {{- toYaml $metric.selector | nindent 12 }}
          {{- end }}
        target:
          type: {{ $metric.targetType | default "Value" | quote }}
          {{- if hasKey $metric "averageValue" }}
          averageValue: {{ $metric.averageValue }}
          {{- end }}
          {{- if hasKey $metric "value" }}
          value: {{ $metric.value }}
          {{- end }}
      {{- end }}
    {{- end }}
  {{- end }}
  {{- if $context.behavior }}
  behavior:
    {{- if $context.behavior.scaleUp }}
    scaleUp:
      {{- if hasKey $context.behavior.scaleUp "stabilizationWindowSeconds" }}
      stabilizationWindowSeconds: {{ $context.behavior.scaleUp.stabilizationWindowSeconds }}
      {{- end }}
      {{- if $context.behavior.scaleUp.selectPolicy }}
      selectPolicy: {{ $context.behavior.scaleUp.selectPolicy | quote }}
      {{- end }}
      {{- if $context.behavior.scaleUp.policies }}
      policies:
        {{- range $policy := $context.behavior.scaleUp.policies }}
        - type: {{ $policy.type | quote }}
          value: {{ $policy.value }}
          periodSeconds: {{ $policy.periodSeconds }}
        {{- end }}
      {{- end }}
    {{- end }}
    {{- if $context.behavior.scaleDown }}
    scaleDown:
      {{- if hasKey $context.behavior.scaleDown "stabilizationWindowSeconds" }}
      stabilizationWindowSeconds: {{ $context.behavior.scaleDown.stabilizationWindowSeconds }}
      {{- end }}
      {{- if $context.behavior.scaleDown.selectPolicy }}
      selectPolicy: {{ $context.behavior.scaleDown.selectPolicy | quote }}
      {{- end }}
      {{- if $context.behavior.scaleDown.policies }}
      policies:
        {{- range $policy := $context.behavior.scaleDown.policies }}
        - type: {{ $policy.type | quote }}
          value: {{ $policy.value }}
          periodSeconds: {{ $policy.periodSeconds }}
        {{- end }}
      {{- end }}
    {{- end }}
  {{- end }}
{{- end }}
