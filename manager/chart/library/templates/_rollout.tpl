{{- define "library-rollout" }}
{{- $root := .root }}
{{- $context := .context }}
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
{{- include "library-metadata" (dict "root" $root "context" $context) }}
spec:
{{- if not $root.Values.autoscaling.enabled }}
  replicas: {{ required "Missing rollout.replicas" $context.replicas }}
{{- end }}
  revisionHistoryLimit: {{ default 10 $context.revisionHistoryLimit }}
  workloadRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ default $root.Chart.Name $context.name }}
  strategy:
    canary:
      stableService: {{ required "Missing stableService" $context.stableService }}
      canaryService: {{ required "Missing canaryService" $context.canaryService }}
      abortScaleDownDelaySeconds: {{ default 30 $context.abortScaleDownDelaySeconds }}
      steps:
        {{- range $step := $context.strategy.canary.steps }}
        - {{ toYaml $step | indent 8 }}
        {{- end }}
{{- end }}
