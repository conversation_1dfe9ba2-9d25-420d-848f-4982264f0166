{{- define "library-service" }}
{{- $root := .root }}
{{- $context := .context }}
apiVersion: v1
kind: Service
{{- include "library-metadata" (dict "root" $root "context" $context) }}
spec:
  type: {{ required "Missing service.type" $context.type }}
  ports:
    {{- range $context.ports }}
    - port: {{ required "Missing port" .port }}
      targetPort: {{ required "Missing targetPort" .targetPort }}
      {{- if .name }}
      name: {{ .name }}
      {{- end }}
      protocol: {{ default "TCP" .protocol }}
    {{- end }}
  selector:
    app: {{ include "name" $root }}
{{- end }}
