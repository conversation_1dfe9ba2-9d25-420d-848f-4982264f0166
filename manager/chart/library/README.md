# Library chart

This package contains helper functions used throughout the Helm charts stack.

This is a library chart, and cannot be installed.


## What this package contains

- Helm templates:
  - `metadata` creates the standard metadata block, with
    - name
    - namespace
    - labels
  - `secrets` takes in a pre-formatted object and creates either a Secret or an ExternalSecret.

## How to package

```bash
helm package <path to chart>
```
