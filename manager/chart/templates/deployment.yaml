{{- $deployment := .Values.deployment | default dict }}
{{- $annotations := $deployment.annotations | default dict }}
{{- $labels := $deployment.labels | default dict }}
{{- $context := dict "name" $deployment.name "annotations" $annotations "labels" $labels }}
apiVersion: apps/v1
kind: Deployment
{{- include "library-metadata" (dict "root" $ "context" $context ) }}
spec:
{{- if not .Values.autoscaling.enabled }}
  replicas: {{ required "Missing deployment.replicaCount" .Values.deployment.replicaCount }}
{{- end }}
  revisionHistoryLimit: {{ default 10 .Values.deployment.revisionHistoryLimitCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 0 
  selector:
    matchLabels:
      app: {{ include "name" . }}
  template:
    {{ include "library-metadata" (dict "root" $ "context" $context ) | nindent 4}}
    spec:
    {{- if .Values.serviceAccount.enabled }}
        serviceAccountName: {{ include "name" . }}
    {{- end }}
      tolerations:
        - key: "karpenter.sh/nodepool"
          operator: "Equal"
          value: {{ default "default" .Values.deployment.nodePoolTolerationValue | quote }}
          effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: karpenter.sh/nodepool
                    operator: In
                    values:
                      - {{ default "default" .Values.deployment.nodePoolTolerationValue }}
      containers:
        - name: {{ include "name" . }}
          image: "{{ .Values.deployment.image.repository }}:{{ .Values.deployment.image.tag }}"
          imagePullPolicy: {{ .Values.deployment.image.pullPolicy }}
          ports:
            {{- range .Values.service }}
            - containerPort: {{ .port }}
              protocol: TCP
            {{- end }}
          envFrom:
            - secretRef:
                name: redis-endpoint
            - secretRef:
                name: documentdb-endpoint
            - secretRef:
                name: secret-key
            - secretRef:
                name: unleash-api-token
            {{- range .Values.secrets }}
              {{- if eq .name "wptgo-token" }}
            - secretRef:
                name: wptgo-token
              {{- end }}
            {{- end }}
            - configMapRef:
                name: {{ include "name" . }}
          env:
            - name: MONGO_USER
              valueFrom:
                secretKeyRef:
                  name: mongo-user
                  key: MONGO_USER
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: mongo-password
                  key: MONGO_PASSWORD
          resources:
            {{- toYaml .Values.deployment.resources | nindent 12 }}
