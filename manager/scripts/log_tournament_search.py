import json
import re
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
from ast import literal_eval


# Config values (simulate AutoStartConfig)
class Config:
    check_before_start_min = 5
    app_id = "botmanager"
    min_prize_pool = 0


config = Config()

# Time setup
now = datetime.now(ZoneInfo("UTC"))

# Pattern to extract the tournament dictionary from the log line
pattern = r"Processing tournament: ({.*})"


def tournament_matches(t: dict) -> bool:
    try:
        starting_time = datetime.fromisoformat(t["startingTime"].replace("Z", "+00:00"))
        late_register_time = t.get("lateRegistrationTime")
        if late_register_time:
            late_register = datetime.fromisoformat(t["lateRegistrationTime"].replace("Z", "+00:00"))

            return (
                starting_time <= now + timedelta(minutes=config.check_before_start_min)
                and late_register > now
                and t["gameMode"] == 2
                and t["regFee"] > 0
                and t["overlay"] > 0
                and t["status"] < 2
                and t["gamePool"] >= config.min_prize_pool
                and t.get("multiFlightLevel", 0) == 0
            )
        else:
            return False
    except Exception as e:
        print(f"Error evaluating tournament: {e}")
        return False


def main():
    with open("log.json", "r", encoding="utf-8") as f:
        logs = json.load(f)

    matching = []

    for entry in logs:
        line = entry.get("line", "")
        match = re.search(pattern, line)
        if match:
            try:
                tournament = literal_eval(match.group(1))
                if tournament_matches(tournament):
                    matching.append(tournament)
            except Exception as e:
                print(f"Error parsing tournament: {e}")

    print(f"Found {len(matching)} matching tournaments:")
    print(f"{'ID':<10} {'Starting Time':<30} {'Late Registration Time':<30} {'Tournament Name'}")
    print("-" * 80)
    for t in matching:
        print(
            f"{t['id']:<10} {t['startingTime']:<30} {t['lateRegistrationTime']:<30} {t['tournamentName']}"
        )


if __name__ == "__main__":
    main()
