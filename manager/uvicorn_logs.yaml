version: 1
disable_existing_loggers: False

formatters:
  default:
    (): 'uvicorn.logging.DefaultFormatter'
    fmt: '%(asctime)s %(levelprefix)-9s %(name)s -: %(message)s'
  access:
    (): 'uvicorn.logging.AccessFormatter'
    fmt: '%(asctime)s %(levelprefix)-9s %(name)s -: %(client_addr)s - "%(request_line)s" %(status_code)s'
  detailed:
    format: '%(asctime)s %(levelname)-8s %(name)s - %(filename)s:%(lineno)d - %(message)s'

handlers:
  default:
    class: logging.StreamHandler
    formatter: default
    stream: ext://sys.stderr
  access:
    class: logging.StreamHandler
    formatter: access
    stream: ext://sys.stdout
  error:
    class: logging.StreamHandler
    formatter: detailed
    stream: ext://sys.stderr

loggers:
  uvicorn:
    level: INFO
    handlers:
      - default
  uvicorn.error:
    level: ERROR
    handlers:
      - error
  uvicorn.access:
    level: INFO
    propagate: False
    handlers:
      - access
